---
title: "Hello, Penn State"
subtitle: "We are..."
author: "<PERSON>"
date: "2025-07-08"
format:
  html:
    preview-links: true
    crossrefs-hover: true
    link-external-icon: true
  # docx: default  # Commented out for faster rendering
  # pdf: default   # Commented out for faster rendering
execute:
  echo: true
  warning: false
  cache: true      # Enable caching for faster re-renders
knitr:
  opts_chunk:
    fig.path: include/fig/
---

## About

This tutorial document is inspired by the 'Hello, Quarto' [tutorial page](https://quarto.org/docs/get-started/hello/rstudio.html).

## Meet Quarto

Quarto enables you to combine text, images, video, and executable code into a document.

### Images

From a URL on the web:

![Quarto logo from <https://quarto.org/quarto.png>](https://quarto.org/quarto.png){#fig-quarto-logo}

From a local img file:

![2025 Bootcamp Banner](include/img/bootcamp-banner.png){#fig-bootcamp-banner}

### Video/Audio

From YouTube:

{{< video https://youtu.be/_f3latmOhew?si=-mi60ml4Nn_KcEmq >}}

From a local file:

{{< video include/vid/rot30.mov >}}

### Computations

In R:

```{r}
#| label: fig-histogram-two-samples
#| fig-cap: "Histogram of two *n*=1,000 samples whose means differ by 0.5."
#| lst-label: lst-histogram-two-samples
#| lst-cap: Code to generate the histogram.
#| warning: false
#| cache: true

library(ggplot2)                                                  # <1>

x0 <- rnorm(n = 1000, mean = 0, sd = 1)                           # <2>
x1 <- rnorm(n = 1000, mean = 0.5, sd = 1)

rand_data <- data.frame(value = c(x0, x1), 
                        sample = rep(c('ctrl', 'expt'), 
                                     each = 1000))                # <3>

rand_data |>                                                      # <4>
  ggplot() +
  geom_histogram(aes(x = value, fill = sample)) 
```

1. Load the `ggplot2` library.
2. Create random data.
3. Create a data frame (rectangular table of data) with two variables (columns): `value` and `sample`.
4. 'Pipe' (`|>') the data to `ggplot` to make a histogram.

### Tables

::: {#tbl-workshop-schedule}

| Topic | Location | Materials |
|:--------------|:----------|-------|
| Quarto (Part I): A tool for open scholarship ([Gilmore](directors.qmd#rick-gilmore)) | W211A | [slides](https://penn-state-open-science.github.io/bootcamp-2025-quarto-I) |
| Questionable research practices ([Lazar](program-committee.qmd#nicole-lazar) & [Valcin](program-committee.qmd#jennifer-valcin))| Dewey Room | |

Workshop details for this session.

:::

### Equations

It's common in many fields to use equations in a scholarly document.
Quarto supports LaTex [equation syntax](https://www.overleaf.com/learn/latex/Learn_LaTeX_in_30_minutes).

For example, this is an inline equation: $sqrt(9)=3$.

This is a display equation, centered in the column:

$$ax^2+bx+c$$ {#eq-polynomial}

### Cross-referencing

We can write Quarto code so that we can easily create linked cross-references to images in our document.
For example, @fig-quarto-logo links to the Quarto logo; @fig-bootcamp-banner links to the Bootcamp banner;  @fig-histogram-two-samples links to the histogram we just made;  @tbl-workshop-schedule links to the table; and @eq-polynomial links to the display (centered) equation.

And by adding labels to code chunks, we can do the same thing for code. The code in @lst-histogram-two-samples produced the figure in @fig-histogram-two-samples.

## Under the hood

### Markdown

Quarto, like Rmarkdown, extends [Markdown](https://www.google.com/url?sa=t&source=web&rct=j&opi=89978449&url=https://www.youtube.com/watch%3Fv%3DTrv2TKgSkq0%26t%3D10&ved=2ahUKEwip47OKgrWOAxWvFFkFHY51AzoQma4LegQIRBAC&usg=AOvVaw1SWscA0dixgr35NkS9OzLA). With Markdown, we write in plain text, but add special characters that format our text or code, or retrieve resources we want for our document. 
For example, we can make text *italic*, **bold**, ***bold and italicized***, or ~~struck through~~. We can have superscripts^1^ and subscripts~1~.

We can make unordered lists:

- Eggs
- Bread
- Jam

And ordered lists:

1. Star Trek
2. Battlestar Galactica
3. Star Wars

We can [link](https://www.psu.edu) to other sites.

We use hash/pound marks '#' to indicate different sections of our document.
So, the top (Level I) section is indicated by `# Level I`; the next level `## Level II`, etc.

::: {.callout-note}
See https://quarto.org/docs/authoring/markdown-basics.html
for complete documentation about the Markdown features Quarto supports.
:::

### Code

Quarto treats text separated from other portions by triple backticks as code.

````markdown
```{{r}}
this_is_r <- TRUE
```
````

Code chunks can be executed or formatted just for display and documentation. See <https://quarto.org/docs/computations/inline-code.html> for more details.

You can also execute code inline as follows $\sqrt 25$ = `{r} sqrt(25)`. The *answer* was generated by the following code:

```` markdown
$\sqrt 25$ = `{{r}} sqrt(25)`.
````

The first part surrounded by '$' is LaTex code, the second part surrounded by backticks is the R code.
This is handy if you want to embed statistical calculations or variables into your text, like this:

The mean of x1 (from @fig-histogram-two-samples above) was `{r} mean(x1)`.

This helps ensure that the statistical values you report in your text come from your *data* not from cutting and pasting.

### YAML

Quarto makes use of the YAML mark-up language to specify parameters about a document. YAML can be placed at the top of a Quarto (`.qmd`) document or in a separate file with the `.yml` file extension.

Here's the YAML code for this file:

```yaml
---
title: "Hello, Penn State"
subtitle: "We are..."
author: "Rick O. Gilmore"
date: "2025-07-08"
format: 
  html:
    preview-links: true         #<1>
    crossrefs-hover: true
    link-external-icon: true    #<2>
  docx: default                 #<3>
  pdf: default    
execute:
  echo: true
  warning: false
knitr:                          #<4>
  opts_chunk:
    fig.path: include/fig/      
---
```

1. Set this to `false` if you don't want to show a preview of linked images, tables, and equations when you hover (mouse) over them. 
2. Set this to `false` if you don't want inline links to have special styling.
3. The *same* document can have be rendered into different output formats.
4. This YAML code tells Quarto where to save figures that are generated by the code you write. I find it useful to specify where these go so I can reuse them later.
