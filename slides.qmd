---
title: "My awesome talk"
subtitle: "Seriously, it's good."
author: "<PERSON>"
date: "2025-07-08"
institute: "Penn State"
format: 
  revealjs:
    footer: "[My awesome talk](slides.qmd)"
    slide-number: true
    fig-align: center
    logo: https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg
    # bibliography: [include/bib/packages.bib, include/bib/data-viz.bib]
    # csl: include/bib/apa.csl
    # css: include/css/gilmore-revealjs.css
    theme: default
    code-tools: true
    chalkboard: true
    codelink: true
    code-overflow: wrap
    code-line-numbers: true
    output-location: column-fragment
    code-copy: true
    transition: none
    transition-speed: default
    progress: true
    preview-links: true
    crossrefs-hover: true
    link-external-icon: true
  pptx: default
execute:
  echo: true
  warning: false
knitr:
  opts_chunk:
    fig.path: include/fig/ 
---

# Preliminaries

## Outline

- Slides as pages
- Adding content
- *Computing* content
- Under the hood

# Slides as pages

## Pages with titles

---

### Pages without titles

# Adding content

## Bullet points

- Here's a point
- Here's another on with *italics*
- We can also do **boldface** and `monospace`
- And we can add [links](https://www.psu.edu)

## Incremental reveal

::: {.incremental}
- This point appears first
- Then this one
- And this...
:::

## Multiple columns

:::: {.columns}
::: {.column width=50%}
- Here are some bullets in the left column
- Still on the left
:::
::: {.column width=50%}
::: {.incremental}
- This bullet appears on the right
- And so does this one
- These can have a [link](https://penn-state-open-science.github.io)
:::
:::
::::

## Images

:::: {.columns}
::: {.column width=50%}
```
![Penn State shield from <https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg>](https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg)
```
:::
::: {.column width=50%}
![Penn State shield from <https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg>](https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg)
:::
::::

## Controlling images

```
![Penn State shield from <https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg>](https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg){width="200px" height="200px" fig-align="right"}
```

![Penn State shield from <https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg>](https://brand.psu.edu/images/backgrounds/penn-state-shield.jpg){width="200px" height="200px" fig-align="right"}

## Videos

{{< video https://youtu.be/UYR59pAYfnc?si=wDyH4h6dbj1yttCF height="70%" width="100%" >}}

## Code

```{r}
#| label: tbl-names-ages
#| tbl-cap: "Some names and ages."
#| code-line-numbers: "|1-3|4|5-7|8-9|"
name <- c("Tom", "Dick", 
          "Harry", "Teresa", 
          "Diane", "Helena")
age <- c(21, 22, 20, 19, 23, 30)
peeps <- 
  data.frame(first_name = name, 
             age_years = age)
peeps |>
  knitr::kable(format = "html")
```

## Figures

```{r}
#| label: fig-sample-barplot
#| fig-cap: "A sample barplot"
#| warning: false
#| code-line-numbers: "|1|2|3|4-5|6|"
library(ggplot2)
peeps |>
  ggplot() +
  aes(x = first_name, 
      y = age_years) +
  geom_bar(stat = "identity")
```

## Cross-referencing

- This links to the prior table: @tbl-names-ages
- This links to the prior figure: @fig-sample-barplot
  - The link was generated automagically from this special code: `@fig-sample-barplot` that represents the *label* from the prior figure `label: fig-sample-barplot`

# Thank you!

# Resources

https://youtu.be/UYR59pAYfnc?si=wDyH4h6dbj1yttCF