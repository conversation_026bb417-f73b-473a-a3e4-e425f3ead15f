{"R": {"Version": "4.5.1", "Repositories": [{"Name": "CRAN", "URL": "https://cloud.r-project.org"}]}, "Packages": {"MASS": {"Package": "MASS", "Version": "7.3-65", "Source": "Repository", "Priority": "recommended", "Date": "2025-02-19", "Revision": "$Rev: 3681 $", "Depends": ["R (>= 4.4.0)", "grDevices", "graphics", "stats", "utils"], "Imports": ["methods"], "Suggests": ["lattice", "nlme", "nnet", "survival"], "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<PERSON>.<PERSON>@R-project.org\"), person(\"<PERSON>\", \"Venables\", role = c(\"aut\", \"cph\")), person(c(\"<PERSON>\", \"<PERSON><PERSON>\"), \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"trl\", comment = \"partial port ca 1998\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"support functions for polr\"))", "Description": "Functions and datasets to support <PERSON><PERSON><PERSON> and <PERSON>ipley, \"Modern Applied Statistics with S\" (4th edition, 2002).", "Title": "Support Functions and Datasets for Venables and <PERSON><PERSON><PERSON>'s MASS", "LazyData": "yes", "ByteCompile": "yes", "License": "GPL-2 | GPL-3", "URL": "http://www.stats.ox.ac.uk/pub/MASS4/", "Contact": "<<EMAIL>>", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre, cph], <PERSON> [aut, cph], <PERSON> [ctb], <PERSON> [trl] (partial port ca 1998), <PERSON><PERSON> [trl] (partial port ca 1998), <PERSON> [ctb] (support functions for polr)", "Maintainer": "<PERSON> <<PERSON><PERSON>@R-project.org>", "Repository": "CRAN"}, "Matrix": {"Package": "Matrix", "Version": "1.7-3", "Source": "Repository", "VersionNote": "do also bump src/version.h, inst/include/Matrix/version.h", "Date": "2025-03-05", "Priority": "recommended", "Title": "Sparse and Dense Matrix Classes and Methods", "Description": "A rich hierarchy of sparse and dense matrix classes, including general, symmetric, triangular, and diagonal matrices with numeric, logical, or pattern entries.  Efficient methods for operating on such matrices, often wrapping the 'BLAS', 'LAPACK', and 'SuiteSparse' libraries.", "License": "GPL (>= 2) | file LICENCE", "URL": "https://Matrix.R-forge.R-project.org", "BugReports": "https://R-forge.R-project.org/tracker/?atid=294&group_id=61", "Contact": "<EMAIL>", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"mm<PERSON><PERSON><PERSON>+<PERSON>@gmail.com\", comment = c(ORCID = \"0000-0002-8685-9910\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3542-2938\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0001-7614-6899\", \"SuiteSparse libraries\", \"collaborators listed in dir(system.file(\\\"doc\\\", \\\"SuiteSparse\\\", package=\\\"Matrix\\\"), pattern=\\\"License\\\", full.names=TRUE, recursive=TRUE)\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-2753-1437\", \"METIS library\", \"Copyright: Regents of the University of Minnesota\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4345-4200\", \"GNU Octave's condest() and onenormest()\", \"Copyright: Regents of the University of California\")), person(\"Jens\", \"Oehlschlägel\", role = \"ctb\", comment = \"initial nearPD()\"), person(\"R Core Team\", role = \"ctb\", comment = c(ROR = \"02zz1nj61\", \"base R's matrix implementation\")))", "Depends": ["R (>= 4.4)", "methods"], "Imports": ["grDevices", "graphics", "grid", "lattice", "stats", "utils"], "Suggests": ["MASS", "datasets", "sfsmisc", "tools"], "Enhances": ["SparseM", "graph"], "LazyData": "no", "LazyDataNote": "not possible, since we use data/*.R and our S4 classes", "BuildResaveData": "no", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-8685-9910>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3542-2938>), <PERSON> [ctb] (<https://orcid.org/0000-0001-7614-6899>, SuiteSparse libraries, collaborators listed in dir(system.file(\"doc\", \"SuiteSparse\", package=\"Matrix\"), pattern=\"License\", full.names=TRUE, recursive=TRUE)), <PERSON> [ctb] (<https://orcid.org/0000-0003-2753-1437>, METIS library, Copyright: Regents of the University of Minnesota), <PERSON> [ctb] (<https://orcid.org/0000-0002-4345-4200>, GNU Octave's condest() and onenormest(), Copyright: Regents of the University of California), <PERSON><PERSON> [ctb] (initial nearPD()), R Core Team [ctb] (02zz1nj61, base R's matrix implementation)", "Maintainer": "<PERSON> <mmae<PERSON>ler+<PERSON>@gmail.com>", "Repository": "CRAN"}, "R6": {"Package": "R6", "Version": "2.6.1", "Source": "Repository", "Title": "Encapsulated Classes with Reference Semantics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Creates classes with reference semantics, similar to R's built-in reference classes. Compared to reference classes, R6 classes are simpler and lighter-weight, and they are not built on S4 classes so they do not require the methods package. These classes allow public and private members, and they support inheritance, even when the classes are defined in different packages.", "License": "MIT + file LICENSE", "URL": "https://r6.r-lib.org, https://github.com/r-lib/R6", "BugReports": "https://github.com/r-lib/R6/issues", "Depends": ["R (>= 3.6)"], "Suggests": ["lobstr", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate, ggplot2, microbenchmark, scales", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RColorBrewer": {"Package": "RColorBrewer", "Version": "1.1-3", "Source": "Repository", "Date": "2022-04-03", "Title": "ColorBrewer Palettes", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"))", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Depends": ["R (>= 2.0.0)"], "Description": "Provides color schemes for maps (and other graphics) designed by <PERSON> as described at http://colorbrewer2.org.", "License": "Apache License 2.0", "NeedsCompilation": "no", "Repository": "CRAN"}, "Rcpp": {"Package": "Rcpp", "Version": "1.1.0", "Source": "Repository", "Title": "Seamless R and C++ Integration", "Date": "2025-07-01", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"<PERSON><PERSON>\", \"<PERSON>aire\", role = \"aut\", comment = c(ORCID = \"0000-0003-0174-9868\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-6403-5550\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0001-8316-9503\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"))", "Description": "The 'Rcpp' package provides R functions as well as C++ classes which offer a seamless integration of R and C++. Many R data types and objects can be mapped back and forth to C++ equivalents which facilitates both writing of new code as well as easier integration of third-party libraries. Documentation about 'Rcpp' is provided by several vignettes included in this package, via the 'Rcpp Gallery' site at <https://gallery.rcpp.org>, the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> (2011, <doi:10.18637/jss.v040.i08>), the book by <PERSON><PERSON><PERSON><PERSON><PERSON> (2013, <doi:10.1007/978-1-4614-6868-4>) and the paper by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (2018, <doi:10.1080/00031305.2017.1375990>); see 'citation(\"Rcpp\")' for details.", "Imports": ["methods", "utils"], "Suggests": ["tinytest", "inline", "rbenchmark", "pkgKitten (>= 0.1.2)"], "URL": "https://www.rcpp.org, https://dirk.eddelbuettel.com/code/rcpp.html, https://github.com/RcppCore/Rcpp", "License": "GPL (>= 2)", "BugReports": "https://github.com/RcppCore/Rcpp/issues", "MailingList": "<EMAIL>", "RoxygenNote": "6.1.1", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0001-6419-907X>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0174-9868>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-2880-7407>), <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6786-5453>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-6403-5550>), <PERSON> [aut] (ORCID: <https://orcid.org/0000-0001-8316-9503>), <PERSON> [aut]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "RcppTOML": {"Package": "RcppTOML", "Version": "0.2.3", "Source": "Repository", "Type": "Package", "Title": "'<PERSON><PERSON><PERSON>' Bindings to <PERSON><PERSON><PERSON> for \"Tom's Obvious Mark<PERSON>\"", "Date": "2025-03-08", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of 'toml++' header library\"))", "Description": "The configuration format defined by 'TOML' (which expands to \"Tom's Obvious Markup Language\") specifies an excellent format (described at <https://toml.io/en/>) suitable for both human editing as well as the common uses of a machine-readable format. This package uses 'Rcpp' to connect to the 'toml++' parser written by <PERSON> to R.", "SystemRequirements": "A C++17 compiler", "BugReports": "https://github.com/eddelbuettel/rcpptoml/issues", "URL": "http://dirk.eddelbuettel.com/code/rcpp.toml.html", "Imports": ["Rcpp (>= 1.0.8)"], "Depends": ["R (>= 3.3.0)"], "LinkingTo": ["Rcpp"], "Suggests": ["tinytest"], "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [aut] (Author of 'toml++' header library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "base64enc": {"Package": "base64enc", "Version": "0.1-3", "Source": "Repository", "Title": "Tools for base64 encoding", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Enhances": ["png"], "Description": "This package provides tools for handling base64 encoding. It is more flexible than the orphaned base64 package.", "License": "GPL-2 | GPL-3", "URL": "http://www.rforge.net/base64enc", "NeedsCompilation": "yes", "Repository": "CRAN"}, "bslib": {"Package": "bslib", "Version": "0.9.0", "Source": "Repository", "Title": "Custom 'Bootstrap' 'Sass' Themes for 'shiny' and 'rmarkdown'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7111-0077\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"<PERSON><PERSON>\", \"<PERSON>gu<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap colorpicker library\"), person(\"<PERSON>\", \"Park\", role = c(\"ctb\", \"cph\"), comment = \"Bootswatch library\"), person(, \"PayP<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Bootstrap accessibility plugin\") )", "Description": "Simplifies custom 'CSS' styling of both 'shiny' and 'rmarkdown' via 'Bootstrap' 'Sass'. Supports 'Bootstrap' 3, 4 and 5 as well as their various 'Bootswatch' themes. An interactive widget is also provided for previewing themes in real time.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/bslib/, https://github.com/rstudio/bslib", "BugReports": "https://github.com/rstudio/bslib/issues", "Depends": ["R (>= 2.10)"], "Imports": ["base64enc", "cachem", "fastmap (>= 1.1.1)", "grDevices", "htmltools (>= 0.5.8)", "jquerylib (>= 0.1.3)", "jsonlite", "lifecycle", "memoise (>= 2.0.1)", "mime", "rlang", "sass (>= 0.4.9)"], "Suggests": ["bsicons", "curl", "fontawesome", "future", "ggplot2", "knitr", "magrit<PERSON>", "rapp<PERSON>s", "rmarkdown (>= 2.7)", "shiny (> 1.8.1)", "testthat", "thematic", "tools", "utils", "withr", "yaml"], "Config/Needs/deploy": "BH, chiflights22, colourpicker, commonmark, cpp11, cpsievert/chiflights22, cpsievert/histoslider, dplyr, DT, ggplot2, ggridges, gt, hexbin, histoslider, htmlwidgets, lattice, leaflet, lubridate, markdown, modelr, plotly, reactable, reshape2, rprojroot, rsconnect, rstudio/shiny, scales, styler, tibble", "Config/Needs/routine": "chromote, desc, renv", "Config/Needs/website": "brio, crosstalk, dplyr, DT, ggplot2, glue, htmlwidgets, leaflet, lorem, palmerpenguins, plotly, purrr, rprojroot, rstudio/htmltools, scales, stringr, tidyr, webshot2", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "zzzz-bs-sass, fonts, zzz-precompile, theme-*, rmd-*", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'accordion.R' 'breakpoints.R' 'bs-current-theme.R' 'bs-dependencies.R' 'bs-global.R' 'bs-remove.R' 'bs-theme-layers.R' 'bs-theme-preset-bootswatch.R' 'bs-theme-preset-brand.R' 'bs-theme-preset-builtin.R' 'bs-theme-preset.R' 'utils.R' 'bs-theme-preview.R' 'bs-theme-update.R' 'bs-theme.R' 'bslib-package.R' 'buttons.R' 'card.R' 'deprecated.R' 'files.R' 'fill.R' 'imports.R' 'input-dark-mode.R' 'input-switch.R' 'layout.R' 'nav-items.R' 'nav-update.R' 'navbar_options.R' 'navs-legacy.R' 'navs.R' 'onLoad.R' 'page.R' 'popover.R' 'precompiled.R' 'print.R' 'shiny-devmode.R' 'sidebar.R' 'staticimports.R' 'tooltip.R' 'utils-deps.R' 'utils-shiny.R' 'utils-tags.R' 'value-box.R' 'version-default.R' 'versions.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-7111-0077>), Posit Software, PBC [cph, fnd], Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), <PERSON><PERSON> [ctb, cph] (Bootstrap colorpicker library), <PERSON> [ctb, cph] (Bootswatch library), PayPal [ctb, cph] (Bootstrap accessibility plugin)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cachem": {"Package": "cachem", "Version": "1.1.0", "Source": "Repository", "Title": "<PERSON><PERSON> R Objects with Automatic Pruning", "Description": "Key-value stores with automatic pruning. Caches can limit either their total size or the age of the oldest object (or both), automatically pruning objects to maintain the constraints.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\")), person(family = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")))", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "ByteCompile": "true", "URL": "https://cachem.r-lib.org/, https://github.com/r-lib/cachem", "Imports": ["rlang", "fastmap (>= 1.2.0)"], "Suggests": ["testthat"], "RoxygenNote": "7.2.3", "Config/Needs/routine": "lobstr", "Config/Needs/website": "pkgdown", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "cli": {"Package": "cli", "Version": "3.6.5", "Source": "Repository", "Title": "Helpers for Developing Command Line Interfaces", "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"g<PERSON><PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A suite of tools to build attractive command line interfaces ('CLIs'), from semantic elements: headings, lists, alerts, paragraphs, etc. Supports custom themes via a 'CSS'-like language. It also contains a number of lower level 'CLI' elements: rules, boxes, trees, and 'Unicode' symbols with 'ASCII' alternatives. It support ANSI colors and text styles as well.", "License": "MIT + file LICENSE", "URL": "https://cli.r-lib.org, https://github.com/r-lib/cli", "BugReports": "https://github.com/r-lib/cli/issues", "Depends": ["R (>= 3.4)"], "Imports": ["utils"], "Suggests": ["callr", "covr", "crayon", "digest", "glue (>= 1.6.0)", "grDevices", "htmltools", "htmlwidgets", "knitr", "methods", "processx", "ps (>= 1.3.4.9000)", "rlang (>= 1.0.2.9003)", "rmarkdown", "rprojroot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testthat (>= 3.2.0)", "tibble", "whoami", "withr"], "Config/Needs/website": "r-lib/asciicast, bench, brio, cpp11, decor, desc, fansi, prettyunits, sessioninfo, tidyverse/tidytemplate, usethis, vctrs", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "digest": {"Package": "digest", "Version": "0.6.37", "Source": "Repository", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-6419-907X\")), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0002-7579-5165\")), person(\"<PERSON>\", \"<PERSON>ek\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2297-1732\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role=\"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-5180-0567\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role=\"ctb\", comment = c(ORCID = \"0000-0001-6786-5453\")), person(\"Thierry\", \"Onkelinx\", role=\"ctb\", comment = c(ORCID = \"0000-0001-8804-4216\")), person(\"Michel\", \"Lang\", role=\"ctb\", comment = c(ORCID = \"0000-0001-9754-0393\")), person(\"Viliam\", \"Simko\", role=\"ctb\"), person(\"Kurt\", \"Hornik\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4198-9911\")), person(\"Radford\", \"Neal\", role=\"ctb\", comment = c(ORCID = \"0000-0002-2473-3407\")), person(\"Kendon\", \"Bell\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9093-8312\")), person(\"Matthew\", \"de Queljoe\", role=\"ctb\"), person(\"Dmitry\", \"Selivanov\", role=\"ctb\"), person(\"Ion\", \"Suruceanu\", role=\"ctb\"), person(\"Bill\", \"Denney\", role=\"ctb\"), person(\"Dirk\", \"Schumacher\", role=\"ctb\"), person(\"András\", \"Svraka\", role=\"ctb\"), person(\"Sergey\", \"Fedorov\", role=\"ctb\"), person(\"Will\", \"Landau\", role=\"ctb\", comment = c(ORCID = \"0000-0003-1878-3253\")), person(\"Floris\", \"Vanderhaeghe\", role=\"ctb\", comment = c(ORCID = \"0000-0002-6378-6229\")), person(\"Kevin\", \"Tappe\", role=\"ctb\"), person(\"Harris\", \"McGehee\", role=\"ctb\"), person(\"Tim\", \"Mastny\", role=\"ctb\"), person(\"Aaron\", \"Peikert\", role=\"ctb\", comment = c(ORCID = \"0000-0001-7813-818X\")), person(\"Mark\", \"van der Loo\", role=\"ctb\", comment = c(ORCID = \"0000-0002-9807-4686\")), person(\"Chris\", \"Muir\", role=\"ctb\", comment = c(ORCID = \"0000-0003-2555-3878\")), person(\"Moritz\", \"Beller\", role=\"ctb\", comment = c(ORCID = \"0000-0003-4852-0526\")), person(\"Sebastian\", \"Campbell\", role=\"ctb\"), person(\"Winston\", \"Chang\", role=\"ctb\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"Dean\", \"Attali\", role=\"ctb\", comment = c(ORCID = \"0000-0002-5645-3493\")), person(\"Michael\", \"Chirico\", role=\"ctb\", comment = c(ORCID = \"0000-0003-0787-087X\")), person(\"Kevin\", \"Ushey\", role=\"ctb\"))", "Date": "2024-08-19", "Title": "Create Compact Hash Digests of R Objects", "Description": "Implementation of a function 'digest()' for the creation of hash digests of arbitrary R objects (using the 'md5', 'sha-1', 'sha-256', 'crc32', 'xxhash', 'murmurhash', 'spookyhash', 'blake3', 'crc32c', 'xxh3_64', and 'xxh3_128' algorithms) permitting easy comparison of R language objects, as well as functions such as'hmac()' to create hash-based message authentication code. Please note that this package is not meant to be deployed for cryptographic purposes for which more comprehensive (and widely tested) libraries such as 'OpenSSL' should be used.", "URL": "https://github.com/eddelbuettel/digest, https://dirk.eddelbuettel.com/code/digest.html", "BugReports": "https://github.com/eddelbuettel/digest/issues", "Depends": ["R (>= 3.3.0)"], "Imports": ["utils"], "License": "GPL (>= 2)", "Suggests": ["tinytest", "simplermarkdown"], "VignetteBuilder": "simplermarkdown", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0001-6419-907X>), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-7579-5165>), <PERSON> [ctb] (<https://orcid.org/0000-0003-2297-1732>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-5180-0567>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-6786-5453>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0001-8804-4216>), <PERSON> [ctb] (<https://orcid.org/0000-0001-9754-0393>), <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0003-4198-9911>), <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-2473-3407>), <PERSON>don <PERSON> [ctb] (<https://or<PERSON>.org/0000-0002-9093-8312>), <PERSON> de <PERSON>l<PERSON>e [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON> [ctb], <PERSON> <PERSON> [ctb], Andr<PERSON> <PERSON>v<PERSON><PERSON> [ctb], <PERSON> <PERSON><PERSON>v [ctb], Will Landau [ctb] (<https://orcid.org/0000-0003-1878-3253>), Floris Vanderhaeghe [ctb] (<https://orcid.org/0000-0002-6378-6229>), Kevin Tappe [ctb], Harris McGehee [ctb], Tim Mastny [ctb], Aaron Peikert [ctb] (<https://orcid.org/0000-0001-7813-818X>), Mark van der Loo [ctb] (<https://orcid.org/0000-0002-9807-4686>), Chris Muir [ctb] (<https://orcid.org/0000-0003-2555-3878>), Moritz Beller [ctb] (<https://orcid.org/0000-0003-4852-0526>), Sebastian Campbell [ctb], Winston Chang [ctb] (<https://orcid.org/0000-0002-1576-2126>), Dean Attali [ctb] (<https://orcid.org/0000-0002-5645-3493>), Michael Chirico [ctb] (<https://orcid.org/0000-0003-0787-087X>), Kevin Ushey [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "evaluate": {"Package": "evaluate", "Version": "1.0.4", "Source": "Repository", "Type": "Package", "Title": "Parsing and Evaluation Tools that Provide More Details than the Default", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Parsing and evaluation tools that make it easy to recreate the command line behaviour of R.", "License": "MIT + file LICENSE", "URL": "https://evaluate.r-lib.org/, https://github.com/r-lib/evaluate", "BugReports": "https://github.com/r-lib/evaluate/issues", "Depends": ["R (>= 3.6.0)"], "Suggests": ["callr", "covr", "ggplot2 (>= 3.3.6)", "lattice", "methods", "pkgload", "ragg (>= 1.4.0)", "rlang (>= 1.1.5)", "knitr", "testthat (>= 3.0.0)", "withr"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [aut] (ORCID: <https://orcid.org/0000-0003-0645-5666>), <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "farver": {"Package": "farver", "Version": "2.1.2", "Source": "Repository", "Type": "Package", "Title": "High Performance Colour Space Manipulation", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = \"Author of the ColorSpace C++ library\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2444-4226\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "The encoding of colour can be handled in many different ways, using different colour spaces. As different colour spaces have different uses, efficient conversion between these representations are important. The 'farver' package provides a set of functions that gives access to very fast colour space conversion and comparisons implemented in C++, and offers speed improvements over the 'convertColor' function in the 'grDevices' package.", "License": "MIT + file LICENSE", "URL": "https://farver.data-imaginist.com, https://github.com/thomasp85/farver", "BugReports": "https://github.com/thomasp85/farver/issues", "Suggests": ["covr", "testthat (>= 3.0.0)"], "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "yes", "Author": "<PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut] (Author of the ColorSpace C++ library), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-2444-4226>), <PERSON><PERSON>t, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fastmap": {"Package": "fastmap", "Version": "1.2.0", "Source": "Repository", "Title": "Fast Data Structures", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(given = \"Tessil\", role = \"cph\", comment = \"hopscotch_map library\") )", "Description": "Fast implementation of data structures, including a key-value store, stack, and queue. Environments are commonly used as key-value stores in R, but every time a new key is used, it is added to R's global symbol table, causing a small amount of memory leakage. This can be problematic in cases where many different keys are used. Fastmap avoids this memory leak issue by implementing the map using data structures in C++.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "Suggests": ["testthat (>= 2.1.1)"], "URL": "https://r-lib.github.io/fastmap/, https://github.com/r-lib/fastmap", "BugReports": "https://github.com/r-lib/fastmap/issues", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], Posit Software, PBC [cph, fnd], Tessil [cph] (hopscotch_map library)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fontawesome": {"Package": "fontawesome", "Version": "0.5.3", "Source": "Repository", "Type": "Package", "Title": "Easily Work with 'Font Awesome' Icons", "Description": "Easily and flexibly insert 'Font Awesome' icons into 'R Markdown' documents and 'Shiny' apps. These icons can be inserted into HTML content through inline 'SVG' tags or 'i' tags. There is also a utility function for exporting 'Font Awesome' icons as 'PNG' images for those situations where raster graphics are needed.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome font\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/fontawesome, https://rstudio.github.io/fontawesome/", "BugReports": "https://github.com/rstudio/fontawesome/issues", "Encoding": "UTF-8", "ByteCompile": "true", "RoxygenNote": "7.3.2", "Depends": ["R (>= 3.3.0)"], "Imports": ["rlang (>= 1.0.6)", "htmltools (>= 0.5.1.1)"], "Suggests": ["covr", "dplyr (>= 1.0.8)", "gt (>= 0.9.0)", "knitr (>= 1.31)", "testthat (>= 3.0.0)", "rsvg"], "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb], <PERSON> [ctb, cph] (Font-Awesome font), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "fs": {"Package": "fs", "Version": "1.6.6", "Source": "Repository", "Title": "Cross-Platform File System Operations Based on 'libuv'", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"libuv project contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Joyent, Inc. and other Node contributors\", role = \"cph\", comment = \"libuv library\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A cross-platform interface to file system operations, built on top of the 'libuv' C library.", "License": "MIT + file LICENSE", "URL": "https://fs.r-lib.org, https://github.com/r-lib/fs", "BugReports": "https://github.com/r-lib/fs/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["covr", "crayon", "knitr", "pillar (>= 1.0.0)", "rmarkdown", "spelling", "testthat (>= 3.0.0)", "tibble (>= 1.1.0)", "vctrs (>= 0.3.0)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Copyright": "file COPYRIGHTS", "Encoding": "UTF-8", "Language": "en-US", "RoxygenNote": "7.2.3", "SystemRequirements": "GNU make", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON><PERSON><PERSON> [aut, cre], libuv project contributors [cph] (libuv library), Joyent, Inc. and other Node contributors [cph] (libuv library), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "ggplot2": {"Package": "ggplot2", "Version": "3.5.2", "Source": "Repository", "Title": "Create Elegant Data Visualisations Using the Grammar of Graphics", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"Woo\", role = \"aut\", comment = c(ORCID = \"0000-0002-5125-4188\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-3385-7233\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9415-4582\")), person(\"Teun\", \"van den <PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-9335-7468\")), person(\"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A system for 'declaratively' creating graphics, based on \"The Grammar of Graphics\". You provide the data, tell 'ggplot2' how to map variables to aesthetics, what graphical primitives to use, and it takes care of the details.", "License": "MIT + file LICENSE", "URL": "https://ggplot2.tidyverse.org, https://github.com/tidyverse/ggplot2", "BugReports": "https://github.com/tidyverse/ggplot2/issues", "Depends": ["R (>= 3.5)"], "Imports": ["cli", "glue", "grDevices", "grid", "gtable (>= 0.1.1)", "isoband", "lifecycle (> 1.0.1)", "MASS", "mgcv", "rlang (>= 1.1.0)", "scales (>= 1.3.0)", "stats", "tibble", "vctrs (>= 0.6.0)", "withr (>= 2.5.0)"], "Suggests": ["covr", "dplyr", "ggplot2movies", "hex<PERSON>", "Hmisc", "knitr", "map<PERSON><PERSON>j", "maps", "multcomp", "munsell", "nlme", "profvis", "quantreg", "ragg (>= 1.2.6)", "RColorBrewer", "rmarkdown", "rpart", "sf (>= 0.7-3)", "svglite (>= 2.1.2)", "testthat (>= 3.1.2)", "vdiffr (>= 1.0.6)", "xml2"], "Enhances": ["sp"], "VignetteBuilder": "knitr", "Config/Needs/website": "ggtext, tidyr, forcats, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.3.2", "Collate": "'ggproto.R' 'ggplot-global.R' 'aaa-.R' 'aes-colour-fill-alpha.R' 'aes-evaluation.R' 'aes-group-order.R' 'aes-linetype-size-shape.R' 'aes-position.R' 'compat-plyr.R' 'utilities.R' 'aes.R' 'utilities-checks.R' 'legend-draw.R' 'geom-.R' 'annotation-custom.R' 'annotation-logticks.R' 'geom-polygon.R' 'geom-map.R' 'annotation-map.R' 'geom-raster.R' 'annotation-raster.R' 'annotation.R' 'autolayer.R' 'autoplot.R' 'axis-secondary.R' 'backports.R' 'bench.R' 'bin.R' 'coord-.R' 'coord-cartesian-.R' 'coord-fixed.R' 'coord-flip.R' 'coord-map.R' 'coord-munch.R' 'coord-polar.R' 'coord-quickmap.R' 'coord-radial.R' 'coord-sf.R' 'coord-transform.R' 'data.R' 'docs_layer.R' 'facet-.R' 'facet-grid-.R' 'facet-null.R' 'facet-wrap.R' 'fortify-lm.R' 'fortify-map.R' 'fortify-multcomp.R' 'fortify-spatial.R' 'fortify.R' 'stat-.R' 'geom-abline.R' 'geom-rect.R' 'geom-bar.R' 'geom-bin2d.R' 'geom-blank.R' 'geom-boxplot.R' 'geom-col.R' 'geom-path.R' 'geom-contour.R' 'geom-count.R' 'geom-crossbar.R' 'geom-segment.R' 'geom-curve.R' 'geom-defaults.R' 'geom-ribbon.R' 'geom-density.R' 'geom-density2d.R' 'geom-dotplot.R' 'geom-errorbar.R' 'geom-errorbarh.R' 'geom-freqpoly.R' 'geom-function.R' 'geom-hex.R' 'geom-histogram.R' 'geom-hline.R' 'geom-jitter.R' 'geom-label.R' 'geom-linerange.R' 'geom-point.R' 'geom-pointrange.R' 'geom-quantile.R' 'geom-rug.R' 'geom-sf.R' 'geom-smooth.R' 'geom-spoke.R' 'geom-text.R' 'geom-tile.R' 'geom-violin.R' 'geom-vline.R' 'ggplot2-package.R' 'grob-absolute.R' 'grob-dotstack.R' 'grob-null.R' 'grouping.R' 'theme-elements.R' 'guide-.R' 'guide-axis.R' 'guide-axis-logticks.R' 'guide-axis-stack.R' 'guide-axis-theta.R' 'guide-legend.R' 'guide-bins.R' 'guide-colorbar.R' 'guide-colorsteps.R' 'guide-custom.R' 'layer.R' 'guide-none.R' 'guide-old.R' 'guides-.R' 'guides-grid.R' 'hexbin.R' 'import-standalone-obj-type.R' 'import-standalone-types-check.R' 'labeller.R' 'labels.R' 'layer-sf.R' 'layout.R' 'limits.R' 'margins.R' 'performance.R' 'plot-build.R' 'plot-construction.R' 'plot-last.R' 'plot.R' 'position-.R' 'position-collide.R' 'position-dodge.R' 'position-dodge2.R' 'position-identity.R' 'position-jitter.R' 'position-jitterdodge.R' 'position-nudge.R' 'position-stack.R' 'quick-plot.R' 'reshape-add-margins.R' 'save.R' 'scale-.R' 'scale-alpha.R' 'scale-binned.R' 'scale-brewer.R' 'scale-colour.R' 'scale-continuous.R' 'scale-date.R' 'scale-discrete-.R' 'scale-expansion.R' 'scale-gradient.R' 'scale-grey.R' 'scale-hue.R' 'scale-identity.R' 'scale-linetype.R' 'scale-linewidth.R' 'scale-manual.R' 'scale-shape.R' 'scale-size.R' 'scale-steps.R' 'scale-type.R' 'scale-view.R' 'scale-viridis.R' 'scales-.R' 'stat-align.R' 'stat-bin.R' 'stat-bin2d.R' 'stat-bindot.R' 'stat-binhex.R' 'stat-boxplot.R' 'stat-contour.R' 'stat-count.R' 'stat-density-2d.R' 'stat-density.R' 'stat-ecdf.R' 'stat-ellipse.R' 'stat-function.R' 'stat-identity.R' 'stat-qq-line.R' 'stat-qq.R' 'stat-quantilemethods.R' 'stat-sf-coordinates.R' 'stat-sf.R' 'stat-smooth-methods.R' 'stat-smooth.R' 'stat-sum.R' 'stat-summary-2d.R' 'stat-summary-bin.R' 'stat-summary-hex.R' 'stat-summary.R' 'stat-unique.R' 'stat-ydensity.R' 'summarise-plot.R' 'summary.R' 'theme.R' 'theme-defaults.R' 'theme-current.R' 'utilities-break.R' 'utilities-grid.R' 'utilities-help.R' 'utilities-matrix.R' 'utilities-patterns.R' 'utilities-resolution.R' 'utilities-tidy-eval.R' 'zxx.R' 'zzz.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-5147-4711>), <PERSON><PERSON><PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5125-4188>), <PERSON><PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-3385-7233>), <PERSON> [aut] (<https://orcid.org/0000-0002-9415-4582>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0002-9335-7468>), <PERSON><PERSON><PERSON>, <PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Title": "Interpreted String Literals", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\", comment = c(ORCID = \"0000-0002-2739-7082\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-6983-2759\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "An implementation of interpreted string literals, inspired by Python's Literal String Interpolation <https://www.python.org/dev/peps/pep-0498/> and Docstrings <https://www.python.org/dev/peps/pep-0257/> and <PERSON>'s Triple-Quoted String Literals <https://docs.julialang.org/en/v1.3/manual/strings/#Triple-Quoted-String-Literals-1>.", "License": "MIT + file LICENSE", "URL": "https://glue.tidyverse.org/, https://github.com/tidyverse/glue", "BugReports": "https://github.com/tidyverse/glue/issues", "Depends": ["R (>= 3.6)"], "Imports": ["methods"], "Suggests": ["crayon", "DBI (>= 1.2.0)", "dplyr", "knitr", "magrit<PERSON>", "rlang", "rmarkdown", "RSQLite", "testthat (>= 3.2.0)", "vctrs (>= 0.3.0)", "waldo (>= 0.5.3)", "withr"], "VignetteBuilder": "knitr", "ByteCompile": "true", "Config/Needs/website": "bench, forcats, ggbeeswarm, ggplot2, <PERSON><PERSON>utils, rprintf, tidyr, tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (<https://orcid.org/0000-0002-2739-7082>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-6983-2759>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "gtable": {"Package": "gtable", "Version": "0.3.6", "Source": "Repository", "Title": "Arrange 'Grobs' in Tables", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools to make it easier to work with \"tables\" of 'grobs'. The 'gtable' package defines a 'gtable' grob class that specifies a grid along with a list of grobs and their placement in the grid. Further the package makes it easy to manipulate and combine 'gtable' objects so that complex compositions can be built up sequentially.", "License": "MIT + file LICENSE", "URL": "https://gtable.r-lib.org, https://github.com/r-lib/gtable", "BugReports": "https://github.com/r-lib/gtable/issues", "Depends": ["R (>= 4.0)"], "Imports": ["cli", "glue", "grid", "lifecycle", "rlang (>= 1.1.0)", "stats"], "Suggests": ["covr", "ggplot2", "knitr", "profvis", "rmarkdown", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2024-10-25", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "here": {"Package": "here", "Version": "1.0.1", "Source": "Repository", "Title": "A Simpler Way to Find Your Files", "Date": "2020-12-13", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-6983-2759\")))", "Description": "Constructs paths to your project's files. Declare the relative path of a file within your project with 'i_am()'. Use the 'here()' function as a drop-in replacement for 'file.path()', it will always locate the files relative to your project root.", "License": "MIT + file LICENSE", "URL": "https://here.r-lib.org/, https://github.com/r-lib/here", "BugReports": "https://github.com/r-lib/here/issues", "Imports": ["rprojroot (>= 2.0.2)"], "Suggests": ["conflicted", "covr", "fs", "knitr", "palmerpenguins", "plyr", "readr", "rlang", "rmarkdown", "testthat", "uuid", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "LazyData": "true", "RoxygenNote": "7.1.1.9000", "Config/testthat/edition": "3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>), <PERSON> [ctb] (<https://orcid.org/0000-0002-6983-2759>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "highr": {"Package": "highr", "Version": "0.11", "Source": "Repository", "Type": "Package", "Title": "Syntax Highlighting for R Source Code", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\") )", "Description": "Provides syntax highlighting for R source code. Currently it supports LaTeX and HTML output. Source code of other languages is supported via <PERSON>'s highlight package (<https://gitlab.com/saalen/highlight>).", "Depends": ["R (>= 3.3.0)"], "Imports": ["xfun (>= 0.18)"], "Suggests": ["knitr", "markdown", "testit"], "License": "GPL", "URL": "https://github.com/yihui/highr", "BugReports": "https://github.com/yihui/highr/issues", "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON><PERSON><PERSON> [aut], <PERSON> [ctb], <PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "htmltools": {"Package": "htmltools", "Version": "*******", "Source": "Repository", "Type": "Package", "Title": "Tools for HTML", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-1576-2126\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Tools for HTML generation and output.", "License": "GPL (>= 2)", "URL": "https://github.com/rstudio/htmltools, https://rstudio.github.io/htmltools/", "BugReports": "https://github.com/rstudio/htmltools/issues", "Depends": ["R (>= 2.14.1)"], "Imports": ["base64enc", "digest", "fastmap (>= 1.1.0)", "grDevices", "rlang (>= 1.0.0)", "utils"], "Suggests": ["Cairo", "markdown", "ragg", "shiny", "testthat", "withr"], "Enhances": ["knitr"], "Config/Needs/check": "knitr", "Config/Needs/website": "rstudio/quillt, bench", "Encoding": "UTF-8", "RoxygenNote": "7.3.1", "Collate": "'colors.R' 'fill.R' 'html_dependency.R' 'html_escape.R' 'html_print.R' 'htmltools-package.R' 'images.R' 'known_tags.R' 'selector.R' 'staticimports.R' 'tag_query.R' 'utils.R' 'tags.R' 'template.R'", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut] (<https://orcid.org/0000-0002-1576-2126>), <PERSON><PERSON> [aut], <PERSON> [aut], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "isoband": {"Package": "isoband", "Version": "0.2.7", "Source": "Repository", "Title": "Generate Isolines and Isobands from Regularly Spaced Elevation Grids", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"<PERSON>\", \"W<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(\"Original author\", ORCID = \"0000-0002-7470-9261\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0002-5147-4711\")) )", "Description": "A fast C++ implementation to generate contour lines (isolines) and contour polygons (isobands) from regularly spaced grids containing elevation data.", "License": "MIT + file LICENSE", "URL": "https://isoband.r-lib.org", "BugReports": "https://github.com/r-lib/isoband/issues", "Imports": ["grid", "utils"], "Suggests": ["covr", "ggplot2", "knitr", "magick", "microbenchmark", "rmarkdown", "sf", "testthat", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "SystemRequirements": "C++11", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-4757-117X>), <PERSON> [aut] (Original author, <https://orcid.org/0000-0002-7470-9261>), <PERSON> [aut] (<https://orcid.org/0000-0002-5147-4711>)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jquerylib": {"Package": "j<PERSON><PERSON><PERSON>", "Version": "0.1.4", "Source": "Repository", "Title": "Obtain 'jQuery' as an HTML Dependency Object", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(family = \"RStudio\", role = \"cph\"), person(family = \"jQuery Foundation\", role = \"cph\", comment = \"jQuery library and jQuery UI library\"), person(family = \"jQuery contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt\") )", "Description": "Obtain any major version of 'jQuery' (<https://code.jquery.com/>) and use it in any webpage generated by 'htmltools' (e.g. 'shiny', 'htmlwidgets', and 'rmarkdown'). Most R users don't need to use this package directly, but other R packages (e.g. 'shiny', 'rmarkdown', etc.) depend on this package to avoid bundling redundant copies of 'jQuery'.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Config/testthat/edition": "3", "RoxygenNote": "7.0.2", "Imports": ["htmltools"], "Suggests": ["testthat"], "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [aut], <PERSON>tudio [cph], jQuery Foundation [cph] (jQuery library and jQuery UI library), jQuery contributors [ctb, cph] (jQuery library; authors listed in inst/lib/jquery-AUTHORS.txt)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "jsonlite": {"Package": "jsonlite", "Version": "2.0.0", "Source": "Repository", "Title": "A Simple and Robust JSON Parser and Generator for R", "License": "MIT + file LICENSE", "Depends": ["methods"], "Authors@R": "c( person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-4035-0289\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"cph\", comment=\"author of bundled libyajl\"))", "URL": "https://jeroen.r-universe.dev/jsonlite https://arxiv.org/abs/1403.2805", "BugReports": "https://github.com/jeroen/jsonlite/issues", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "VignetteBuilder": "knitr, R.rsp", "Description": "A reasonably fast JSON parser and generator, optimized for statistical  data and the web. Offers simple, flexible tools for working with JSON in R, and is particularly powerful for building pipelines and interacting with a web API.  The implementation is based on the mapping described in the vignette (Ooms, 2014). In addition to converting JSON data from/to R objects, 'jsonlite' contains  functions to stream, validate, and prettify JSON data. The unit tests included  with the package verify that all edge cases are encoded and decoded consistently  for use with dynamic data in systems and applications.", "Suggests": ["httr", "vctrs", "testthat", "knitr", "rmarkdown", "R.rsp", "sf"], "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-4035-0289>), <PERSON> [ctb], <PERSON> [cph] (author of bundled libyajl)", "Repository": "CRAN"}, "knitr": {"Package": "knitr", "Version": "1.50", "Source": "Repository", "Type": "Package", "Title": "A General-Purpose Package for Dynamic Report Generation in R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8335-495X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>t\", \"<PERSON><PERSON>rov\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>vie<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>-<PERSON>\", role = \"ctb\"), person(\"David\", \"Robinson\", role = \"ctb\"), person(\"Doug\", \"Hemken\", role = \"ctb\"), person(\"Duncan\", \"Murdoch\", role = \"ctb\"), person(\"Elio\", \"Campitelli\", role = \"ctb\"), person(\"Ellis\", \"Hughes\", role = \"ctb\"), person(\"Emily\", \"Riederer\", role = \"ctb\"), person(\"Fabian\", \"Hirschmann\", role = \"ctb\"), person(\"Fitch\", \"Simeon\", role = \"ctb\"), person(\"Forest\", \"Fang\", role = \"ctb\"), person(c(\"Frank\", \"E\", \"Harrell\", \"Jr\"), role = \"ctb\", comment = \"the Sweavel package at inst/misc/Sweavel.sty\"), person(\"Garrick\", \"Aden-Buie\", role = \"ctb\"), person(\"Gregoire\", \"Detrez\", role = \"ctb\"), person(\"Hadley\", \"Wickham\", role = \"ctb\"), person(\"Hao\", \"Zhu\", role = \"ctb\"), person(\"Heewon\", \"Jeon\", role = \"ctb\"), person(\"Henrik\", \"Bengtsson\", role = \"ctb\"), person(\"Hiroaki\", \"Yutani\", role = \"ctb\"), person(\"Ian\", \"Lyttle\", role = \"ctb\"), person(\"Hodges\", \"Daniel\", role = \"ctb\"), person(\"Jacob\", \"Bien\", role = \"ctb\"), person(\"Jake\", \"Burkhead\", role = \"ctb\"), person(\"James\", \"Manton\", role = \"ctb\"), person(\"Jared\", \"Lander\", role = \"ctb\"), person(\"Jason\", \"Punyon\", role = \"ctb\"), person(\"Javier\", \"Luraschi\", role = \"ctb\"), person(\"Jeff\", \"Arnold\", role = \"ctb\"), person(\"Jenny\", \"Bryan\", role = \"ctb\"), person(\"Jeremy\", \"Ashkenas\", role = c(\"ctb\", \"cph\"), comment = \"the CSS file at inst/misc/docco-classic.css\"), person(\"Jeremy\", \"Stephens\", role = \"ctb\"), person(\"Jim\", \"Hester\", role = \"ctb\"), person(\"Joe\", \"Cheng\", role = \"ctb\"), person(\"Johannes\", \"Ranke\", role = \"ctb\"), person(\"John\", \"Honaker\", role = \"ctb\"), person(\"John\", \"Muschelli\", role = \"ctb\"), person(\"Jonathan\", \"Keane\", role = \"ctb\"), person(\"JJ\", \"Allaire\", role = \"ctb\"), person(\"Johan\", \"Toloe\", role = \"ctb\"), person(\"Jonathan\", \"Sidi\", role = \"ctb\"), person(\"Joseph\", \"Larmarange\", role = \"ctb\"), person(\"Julien\", \"Barnier\", role = \"ctb\"), person(\"Kaiyin\", \"Zhong\", role = \"ctb\"), person(\"Kamil\", \"Slowikowski\", role = \"ctb\"), person(\"Karl\", \"Forner\", role = \"ctb\"), person(c(\"Kevin\", \"K.\"), \"Smith\", role = \"ctb\"), person(\"Kirill\", \"Mueller\", role = \"ctb\"), person(\"Kohske\", \"Takahashi\", role = \"ctb\"), person(\"Lorenz\", \"Walthert\", role = \"ctb\"), person(\"Lucas\", \"Gallindo\", role = \"ctb\"), person(\"Marius\", \"Hofert\", role = \"ctb\"), person(\"Martin\", \"Modrák\", role = \"ctb\"), person(\"Michael\", \"Chirico\", role = \"ctb\"), person(\"Michael\", \"Friendly\", role = \"ctb\"), person(\"Michal\", \"Bojanowski\", role = \"ctb\"), person(\"Michel\", \"Kuhlmann\", role = \"ctb\"), person(\"Miller\", \"Patrick\", role = \"ctb\"), person(\"Nacho\", \"Caballero\", role = \"ctb\"), person(\"Nick\", \"Salkowski\", role = \"ctb\"), person(\"Niels Richard\", \"Hansen\", role = \"ctb\"), person(\"Noam\", \"Ross\", role = \"ctb\"), person(\"Obada\", \"Mahdi\", role = \"ctb\"), person(\"Pavel N.\", \"Krivitsky\", role = \"ctb\", comment=c(ORCID = \"0000-0002-9101-3362\")), person(\"Pedro\", \"Faria\", role = \"ctb\"), person(\"Qiang\", \"Li\", role = \"ctb\"), person(\"Ramnath\", \"Vaidyanathan\", role = \"ctb\"), person(\"Richard\", \"Cotton\", role = \"ctb\"), person(\"Robert\", \"Krzyzanowski\", role = \"ctb\"), person(\"Rodrigo\", \"Copetti\", role = \"ctb\"), person(\"Romain\", \"Francois\", role = \"ctb\"), person(\"Ruaridh\", \"Williamson\", role = \"ctb\"), person(\"Sagiru\", \"Mati\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1413-3974\")), person(\"Scott\", \"Kostyshak\", role = \"ctb\"), person(\"Sebastian\", \"Meyer\", role = \"ctb\"), person(\"Sietse\", \"Brouwer\", role = \"ctb\"), person(c(\"Simon\", \"de\"), \"Bernard\", role = \"ctb\"), person(\"Sylvain\", \"Rousseau\", role = \"ctb\"), person(\"Taiyun\", \"Wei\", role = \"ctb\"), person(\"Thibaut\", \"Assus\", role = \"ctb\"), person(\"Thibaut\", \"Lamadon\", role = \"ctb\"), person(\"Thomas\", \"Leeper\", role = \"ctb\"), person(\"Tim\", \"Mastny\", role = \"ctb\"), person(\"Tom\", \"Torsney-Weir\", role = \"ctb\"), person(\"Trevor\", \"Davis\", role = \"ctb\"), person(\"Viktoras\", \"Veitas\", role = \"ctb\"), person(\"Weicheng\", \"Zhu\", role = \"ctb\"), person(\"Wush\", \"Wu\", role = \"ctb\"), person(\"Zachary\", \"Foster\", role = \"ctb\"), person(\"Zhian N.\", \"Kamvar\", role = \"ctb\", comment = c(ORCID = \"0000-0003-1458-7108\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a general-purpose tool for dynamic report generation in R using Literate Programming techniques.", "Depends": ["R (>= 3.6.0)"], "Imports": ["evaluate (>= 0.15)", "highr (>= 0.11)", "methods", "tools", "xfun (>= 0.51)", "yaml (>= 2.1.19)"], "Suggests": ["bslib", "codetools", "DBI (>= 0.4-1)", "digest", "formatR", "gifski", "gridSVG", "htmlwidgets (>= 0.7)", "jpeg", "JuliaCall (>= 0.11.1)", "magick", "litedown", "markdown (>= 1.3)", "png", "ragg", "reticulate (>= 1.4)", "rgl (>= 0.95.1201)", "rlang", "rmarkdown", "sass", "showtext", "styler (>= 1.2.0)", "targets (>= 0.6.0)", "testit", "tibble", "tikzDevice (>= 0.10)", "tinytex (>= 0.56)", "webshot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svglite"], "License": "GPL", "URL": "https://yihui.org/knitr/", "BugReports": "https://github.com/yihui/knitr/issues", "Encoding": "UTF-8", "VignetteBuilder": "litedown, knitr", "SystemRequirements": "Package vignettes based on R Markdown v2 or reStructuredText require Pandoc (http://pandoc.org). The function rst2pdf() requires rst2pdf (https://github.com/rst2pdf/rst2pdf).", "Collate": "'block.R' 'cache.R' 'citation.R' 'hooks-html.R' 'plot.R' 'utils.R' 'defaults.R' 'concordance.R' 'engine.R' 'highlight.R' 'themes.R' 'header.R' 'hooks-asciidoc.R' 'hooks-chunk.R' 'hooks-extra.R' 'hooks-latex.R' 'hooks-md.R' 'hooks-rst.R' 'hooks-textile.R' 'hooks.R' 'output.R' 'package.R' 'pandoc.R' 'params.R' 'parser.R' 'pattern.R' 'rocco.R' 'spin.R' 'table.R' 'template.R' 'utils-conversion.R' 'utils-rd2html.R' 'utils-string.R' 'utils-sweave.R' 'utils-upload.R' 'utils-vignettes.R' 'zzz.R'", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the CSS files under inst/themes/ were derived from the Highlight package http://www.andre-simon.de), <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-8335-495X>), <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb] (the Sweavel package at inst/mi<PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>.s<PERSON>), <PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON><PERSON>], <PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON> <PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON><PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON> [c<PERSON>b], <PERSON> <PERSON><PERSON><PERSON><PERSON> [ctb], James Manton [ctb], Jared Lander [ctb], Jason Punyon [ctb], Javier Luraschi [ctb], Jeff Arnold [ctb], Jenny Bryan [ctb], Jeremy Ashkenas [ctb, cph] (the CSS file at inst/misc/docco-classic.css), Jeremy Stephens [ctb], Jim Hester [ctb], Joe Cheng [ctb], Johannes Ranke [ctb], John Honaker [ctb], John Muschelli [ctb], Jonathan Keane [ctb], JJ Allaire [ctb], Johan Toloe [ctb], Jonathan Sidi [ctb], Joseph Larmarange [ctb], Julien Barnier [ctb], Kaiyin Zhong [ctb], Kamil Slowikowski [ctb], Karl Forner [ctb], Kevin K. Smith [ctb], Kirill Mueller [ctb], Kohske Takahashi [ctb], Lorenz Walthert [ctb], Lucas Gallindo [ctb], Marius Hofert [ctb], Martin Modrák [ctb], Michael Chirico [ctb], Michael Friendly [ctb], Michal Bojanowski [ctb], Michel Kuhlmann [ctb], Miller Patrick [ctb], Nacho Caballero [ctb], Nick Salkowski [ctb], Niels Richard Hansen [ctb], Noam Ross [ctb], Obada Mahdi [ctb], Pavel N. Krivitsky [ctb] (<https://orcid.org/0000-0002-9101-3362>), Pedro Faria [ctb], Qiang Li [ctb], Ramnath Vaidyanathan [ctb], Richard Cotton [ctb], Robert Krzyzanowski [ctb], Rodrigo Copetti [ctb], Romain Francois [ctb], Ruaridh Williamson [ctb], Sagiru Mati [ctb] (<https://orcid.org/0000-0003-1413-3974>), Scott Kostyshak [ctb], Sebastian Meyer [ctb], Sietse Brouwer [ctb], Simon de Bernard [ctb], Sylvain Rousseau [ctb], Taiyun Wei [ctb], Thibaut Assus [ctb], Thibaut Lamadon [ctb], Thomas Leeper [ctb], Tim Mastny [ctb], Tom Torsney-Weir [ctb], Trevor Davis [ctb], Viktoras Veitas [ctb], Weicheng Zhu [ctb], Wush Wu [ctb], Zachary Foster [ctb], Zhian N. Kamvar [ctb] (<https://orcid.org/0000-0003-1458-7108>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "labeling": {"Package": "labeling", "Version": "0.4.3", "Source": "Repository", "Type": "Package", "Title": "Axis Labeling", "Date": "2023-08-29", "Author": "<PERSON>,", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Description": "Functions which provide a range of axis labeling algorithms.", "License": "MIT + file LICENSE | Unlimited", "Collate": "'labeling.R'", "NeedsCompilation": "no", "Imports": ["stats", "graphics"], "Repository": "CRAN"}, "lattice": {"Package": "lattice", "Version": "0.22-7", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Trellis Graphics for R", "Authors@R": "c(person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>rka<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4107-1553\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", comment = \"documentation\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"miscellaneous improvements\"), person(\"<PERSON><PERSON><PERSON><PERSON> (<PERSON>)\", \"<PERSON>\", role = \"cph\", comment = \"filled contour code\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(\"<PERSON>\", \"Eng\", role = \"ctb\", comment = \"violin plot improvements\"), person(\"<PERSON>chi<PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"modern colors\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"generics for larrows, lpolygon, lrect and lsegments\") )", "Description": "A powerful and elegant high-level data visualization system inspired by Trellis graphics, with an emphasis on multivariate data. Lattice is sufficient for typical graphics needs, and is also flexible enough to handle most nonstandard requirements. See ?Lattice for an introduction.", "Depends": ["R (>= 4.0.0)"], "Suggests": ["KernSmooth", "MASS", "latticeExtra", "colorspace"], "Imports": ["grid", "grDevices", "graphics", "stats", "utils"], "Enhances": ["chron", "zoo"], "LazyLoad": "yes", "LazyData": "yes", "License": "GPL (>= 2)", "URL": "https://lattice.r-forge.r-project.org/", "BugReports": "https://github.com/deepayan/lattice/issues", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-4107-1553>), <PERSON> [ctb], <PERSON> [ctb] (documentation), <PERSON> [ctb], <PERSON> [ctb] (miscellaneous improvements), <PERSON><PERSON><PERSON><PERSON> (<PERSON>) <PERSON> [cph] (filled contour code), <PERSON> [ctb], <PERSON> [ctb] (violin plot improvements), <PERSON><PERSON><PERSON> [ctb] (modern colors), <PERSON> [ctb] (generics for larrows, lpolygon, lrect and lsegments)", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Title": "Manage the Life Cycle of your Package Functions", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Manage the life cycle of your exported functions with shared conventions, documentation badges, and user-friendly deprecation warnings.", "License": "MIT + file LICENSE", "URL": "https://lifecycle.r-lib.org/, https://github.com/r-lib/lifecycle", "BugReports": "https://github.com/r-lib/lifecycle/issues", "Depends": ["R (>= 3.6)"], "Imports": ["cli (>= 3.4.0)", "glue", "rlang (>= 1.1.0)"], "Suggests": ["covr", "crayon", "knitr", "lintr", "rmarkdown", "testthat (>= 3.0.1)", "tibble", "tidyverse", "tools", "vctrs", "withr"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate, usethis", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.1", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.3", "Source": "Repository", "Type": "Package", "Title": "A Forward-Pipe Operator for R", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cph\"), comment = \"Original author and creator of magrittr\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@rstudio.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"cre\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")) )", "Description": "Provides a mechanism for chaining commands with a new forward-pipe operator, %>%. This operator will forward a value, or the result of an expression, into the next function call/expression. There is flexible support for the type of right-hand side expressions. For more information, see package vignette.  To quote <PERSON>, \"Ceci n'est pas un pipe.\"", "License": "MIT + file LICENSE", "URL": "https://magrittr.tidyverse.org, https://github.com/tidyverse/magrittr", "BugReports": "https://github.com/tidyverse/magrittr/issues", "Depends": ["R (>= 3.4.0)"], "Suggests": ["covr", "knitr", "rlang", "rmarkdown", "testthat"], "VignetteBuilder": "knitr", "ByteCompile": "Yes", "Config/Needs/website": "tidyverse/tidytemplate", "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph] (Original author and creator of magrittr), <PERSON> [aut], <PERSON> [cre], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "memoise": {"Package": "memoise", "Version": "2.0.1", "Source": "Repository", "Title": "'Memoisation' of Functions", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\"), person(given = \"Kirill\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"))", "Description": "Cache the results of a function so that when you call it again with the same arguments it returns the previously computed value.", "License": "MIT + file LICENSE", "URL": "https://memoise.r-lib.org, https://github.com/r-lib/memoise", "BugReports": "https://github.com/r-lib/memoise/issues", "Imports": ["rlang (>= 0.4.10)", "cachem"], "Suggests": ["digest", "aws.s3", "covr", "googleAuthR", "googleCloudStorageR", "httr", "testthat"], "Encoding": "UTF-8", "RoxygenNote": "7.1.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mgcv": {"Package": "mgcv", "Version": "1.9-3", "Source": "Repository", "Authors@R": "person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\")", "Title": "Mixed GAM Computation Vehicle with Automatic Smoothness Estimation", "Description": "Generalized additive (mixed) models, some of their extensions and  other generalized ridge regression with multiple smoothing  parameter estimation by (Restricted) Marginal Likelihood,  Generalized Cross Validation and similar, or using iterated  nested Laplace approximation for fully Bayesian inference. See  <PERSON> (2017) <doi:10.1201/9781315370279> for an overview.  Includes a gam() function, a wide variety of smoothers, 'JAGS'  support and distributions beyond the exponential family.", "Priority": "recommended", "Depends": ["R (>= 3.6.0)", "nlme (>= 3.1-64)"], "Imports": ["methods", "stats", "graphics", "Matrix", "splines", "utils"], "Suggests": ["parallel", "survival", "MASS"], "LazyLoad": "yes", "ByteCompile": "yes", "License": "GPL (>= 2)", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "mime": {"Package": "mime", "Version": "0.13", "Source": "Repository", "Type": "Package", "Title": "Map Filenames to MIME Types", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\") )", "Description": "Guesses the MIME type from a filename extension using the data derived from /etc/mime.types in UNIX-type systems.", "Imports": ["tools"], "License": "GPL", "URL": "https://github.com/yihui/mime", "BugReports": "https://github.com/yihui/mime/issues", "RoxygenNote": "7.3.2", "Encoding": "UTF-8", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "nlme": {"Package": "nlme", "Version": "3.1-168", "Source": "Repository", "Date": "2025-03-31", "Priority": "recommended", "Title": "Linear and Nonlinear Mixed Effects Models", "Authors@R": "c(person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\", comment = \"S version\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\", comment = \"up to 2007\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2002\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = \"up to 2005\"), person(\"EISPACK authors\", role = \"ctb\", comment = \"src/rs.f\"), person(\"<PERSON><PERSON>\", \"Heisterka<PERSON>\", role = \"ctb\", comment = \"Author fixed sigma\"), person(\"<PERSON>\", \"<PERSON>\",role = \"ctb\", comment = \"Programmer fixed sigma\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\", comment = \"varConstProp()\"), person(\"R Core Team\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ROR = \"02zz1nj61\")))", "Contact": "see 'MailingList'", "Description": "Fit and compare Gaussian linear and nonlinear mixed-effects models.", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "stats", "utils", "lattice"], "Suggests": ["MASS", "SASmixed"], "LazyData": "yes", "Encoding": "UTF-8", "License": "GPL (>= 2)", "BugReports": "https://bugs.r-project.org", "MailingList": "<EMAIL>", "URL": "https://svn.r-project.org/R-packages/trunk/nlme/", "NeedsCompilation": "yes", "Author": "<PERSON> [aut] (S version), <PERSON> [aut] (up to 2007), <PERSON><PERSON> [ctb] (up to 2002), <PERSON><PERSON><PERSON> [ctb] (up to 2005), EISPACK authors [ctb] (src/rs.f), <PERSON><PERSON> [ctb] (Author fixed sigma), <PERSON> [ctb] (Programmer fixed sigma), <PERSON> [ctb] (varConstProp()), R Core Team [aut, cre] (02zz1nj61)", "Maintainer": "R Core Team <<EMAIL>>", "Repository": "CRAN"}, "pillar": {"Package": "pillar", "Version": "1.11.0", "Source": "Repository", "Title": "Coloured Formatting for Columns", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\"), person(given = \"RStudio\", role = \"cph\"))", "Description": "Provides 'pillar' and 'colonnade' generics designed for formatting columns of data using the full range of colours provided by modern terminals.", "License": "MIT + file LICENSE", "URL": "https://pillar.r-lib.org/, https://github.com/r-lib/pillar", "BugReports": "https://github.com/r-lib/pillar/issues", "Imports": ["cli (>= 2.3.0)", "glue", "lifecycle", "rlang (>= 1.0.2)", "utf8 (>= 1.1.0)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bit64", "DBI", "debugme", "DiagrammeR", "dplyr", "formattable", "ggplot2", "knitr", "lubridate", "nanotime", "nycflights13", "palmerpenguins", "rmarkdown", "scales", "stringi", "survival", "testthat (>= 3.1.1)", "tibble", "units (>= 0.7.2)", "vdiffr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "format_multi_fuzz, format_multi_fuzz_2, format_multi, ctl_colonnade, ctl_colonnade_1, ctl_colonnade_2", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/gha/extra-packages": "units=?ignore-before-r=4.3.0", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON>tudio [cph]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "pkgconfig": {"Package": "pkgconfig", "Version": "2.0.3", "Source": "Repository", "Title": "Private Configuration for 'R' Packages", "Author": "<PERSON><PERSON><PERSON>", "Maintainer": "<PERSON><PERSON><PERSON> <<EMAIL>>", "Description": "Set configuration options on a per-package basis. Options set by a given package only apply to that package, other packages are unaffected.", "License": "MIT + file LICENSE", "LazyData": "true", "Imports": ["utils"], "Suggests": ["covr", "testthat", "disposables (>= 1.0.3)"], "URL": "https://github.com/r-lib/pkgconfig#readme", "BugReports": "https://github.com/r-lib/pkgconfig/issues", "Encoding": "UTF-8", "NeedsCompilation": "no", "Repository": "CRAN"}, "png": {"Package": "png", "Version": "0.1-8", "Source": "Repository", "Title": "Read and write PNG images", "Author": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Maintainer": "<PERSON> <<PERSON>.<PERSON>@r-project.org>", "Depends": ["R (>= 2.9.0)"], "Description": "This package provides an easy and simple way to read, write and display bitmap images stored in the PNG format. It can read and write both files and in-memory raw vectors.", "License": "GPL-2 | GPL-3", "SystemRequirements": "libpng", "URL": "http://www.rforge.net/png/", "NeedsCompilation": "yes", "Repository": "CRAN"}, "rappdirs": {"Package": "rapp<PERSON>s", "Version": "0.3.3", "Source": "Repository", "Type": "Package", "Title": "Application Directories: Determine Where to Save Data, Caches, and Logs", "Authors@R": "c(person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = c(\"trl\", \"cre\", \"cph\"), email = \"<EMAIL>\"), person(given = \"RStudio\", role = \"cph\"), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"aut\"), person(given = \"ActiveState\", role = \"cph\", comment = \"R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs\"), person(given = \"<PERSON>\", family = \"Petrisor\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = c(\"trl\", \"aut\")), person(given = \"<PERSON><PERSON><PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person(given = \"<PERSON>\", family = \"<PERSON><PERSON><PERSON>\", role = \"ctb\"))", "Description": "An easy way to determine which directories on the users computer you should use to save data, caches and logs. A port of Python's 'Appdirs' (<https://github.com/ActiveState/appdirs>) to R.", "License": "MIT + file LICENSE", "URL": "https://rappdirs.r-lib.org, https://github.com/r-lib/rappdirs", "BugReports": "https://github.com/r-lib/rappdirs/issues", "Depends": ["R (>= 3.2)"], "Suggests": ["roxygen2", "testthat (>= 3.0.0)", "covr", "withr"], "Copyright": "Original python appdirs module copyright (c) 2010 ActiveState Software Inc. R port copyright Hadley <PERSON>, RStudio. See file LICENSE for details.", "Encoding": "UTF-8", "RoxygenNote": "7.1.1", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [trl, cre, cph], <PERSON><PERSON><PERSON> [cph], <PERSON><PERSON><PERSON> [aut], <PERSON> [aut], ActiveState [cph] (R/appdir.r, R/cache.r, R/data.r, R/log.r translated from appdirs), <PERSON> [ctb], <PERSON> [trl, aut], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "renv": {"Package": "renv", "Version": "1.1.4", "Source": "Repository", "Type": "Package", "Title": "Project Environments", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-2880-7407\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-4757-117X\")), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A dependency management toolkit for R. Using 'renv', you can create and manage project-local R libraries, save the state of these libraries to a 'lockfile', and later restore your library as required. Together, these tools can help make your projects more isolated, portable, and reproducible.", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/renv/, https://github.com/rstudio/renv", "BugReports": "https://github.com/rstudio/renv/issues", "Imports": ["utils"], "Suggests": ["BiocManager", "cli", "compiler", "covr", "cpp11", "devtools", "gitcreds", "jsonlite", "jsonvalidate", "knitr", "miniUI", "modules", "packrat", "pak", "R6", "remotes", "reticulate", "rmarkdown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiny", "testthat", "uuid", "waldo", "yaml", "webfakes"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "bioconductor,python,install,restore,snapshot,retrieve,remotes", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre] (<https://orcid.org/0000-0003-2880-7407>), <PERSON> [aut] (<https://orcid.org/0000-0003-4757-117X>), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "reticulate": {"Package": "reticulate", "Version": "1.42.0", "Source": "Repository", "Type": "Package", "Title": "Interface to 'Python'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cre\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\"), email = \"<EMAIL>\"), person(\"RStudio\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON>\", role = c(\"aut\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0001-5243-233X\")), person(\"<PERSON>\", \"Eddelbuettel\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), email = \"<EMAIL>\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"<PERSON>Thread library, http://tinythreadpp.bitsnbites.eu/\") )", "Description": "Interface to 'Python' modules, classes, and functions. When calling into 'Python', R data types are automatically converted to their equivalent 'Python' types. When values are returned from 'Python' to R they are converted back to R types. Compatible with all versions of 'Python' >= 2.7.", "License": "Apache License 2.0", "URL": "https://rstudio.github.io/reticulate/, https://github.com/rstudio/reticulate", "BugReports": "https://github.com/rstudio/reticulate/issues", "SystemRequirements": "Python (>= 2.7.0)", "Encoding": "UTF-8", "Depends": ["R (>= 3.5)"], "Imports": ["Matrix", "Rcpp (>= 1.0.7)", "RcppTOML", "graphics", "here", "jsonlite", "methods", "png", "rapp<PERSON>s", "utils", "rlang", "withr"], "Suggests": ["callr", "knitr", "glue", "cli", "rmarkdown", "pillar", "testthat"], "LinkingTo": ["Rcpp"], "RoxygenNote": "7.3.2", "VignetteBuilder": "knitr", "Config/build/compilation-database": "true", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [ctb, cre], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON><PERSON><PERSON> [cph, fnd], <PERSON> [aut, cph] (<https://orcid.org/0000-0001-5243-233X>), <PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb, cph], <PERSON> [ctb, cph] (TinyThread library, http://tinythreadpp.bitsnbites.eu/)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rlang": {"Package": "rlang", "Version": "1.1.6", "Source": "Repository", "Title": "Functions for Base Types and Core R and 'Tidyverse' Features", "Description": "A toolbox for working with base types, core R features like the condition system, and core 'Tidyverse' features like tidy evaluation.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", ,\"<EMAIL>\", c(\"aut\", \"cre\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", ,\"<EMAIL>\", \"aut\"), person(given = \"mikefc\", email = \"<EMAIL>\", role = \"cph\", comment = \"Hash implementation based on <PERSON>'s xxhashlite\"), person(given = \"Yann\", family = \"Collet\", role = \"cph\", comment = \"Author of the embedded xxHash library\"), person(given = \"Posit, PBC\", role = c(\"cph\", \"fnd\")) )", "License": "MIT + file LICENSE", "ByteCompile": "true", "Biarch": "true", "Depends": ["R (>= 3.5.0)"], "Imports": ["utils"], "Suggests": ["cli (>= 3.1.0)", "covr", "crayon", "desc", "fs", "glue", "knitr", "magrit<PERSON>", "methods", "pillar", "pkgload", "rmarkdown", "stats", "testthat (>= 3.2.0)", "tibble", "usethis", "vctrs (>= 0.2.3)", "withr"], "Enhances": ["winch"], "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "URL": "https://rlang.r-lib.org, https://github.com/r-lib/rlang", "BugReports": "https://github.com/r-lib/rlang/issues", "Config/build/compilation-database": "true", "Config/testthat/edition": "3", "Config/Needs/website": "dplyr, tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cre], <PERSON> [aut], mikef<PERSON> [cph] (Hash implementation based on <PERSON>'s xxhashlite), <PERSON><PERSON> [cph] (Author of the embedded xxHash library), Posit, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rmarkdown": {"Package": "rmarkdown", "Version": "2.29", "Source": "Repository", "Type": "Package", "Title": "Dynamic Documents for R", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0003-0645-5666\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"Andrew\", \"Dunning\", role = \"ctb\", comment = c(ORCID = \"0000-0003-0464-5036\")), person(\"Atsushi\", \"Yasumoto\", role = c(\"ctb\", \"cph\"), comment = c(ORCID = \"0000-0002-8335-495X\", cph = \"Number sections Lua filter\")), person(\"Barret\", \"Schloerke\", role = \"ctb\"), person(\"Carson\", \"Sievert\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4958-2844\")),  person(\"Devon\", \"Ryan\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"Frederik\", \"Aust\", , \"<EMAIL>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4900-788X\")), person(\"Jeff\", \"Allen\", , \"<EMAIL>\", role = \"ctb\"),  person(\"JooYoung\", \"Seo\", role = \"ctb\", comment = c(ORCID = \"0000-0002-4064-6012\")), person(\"Malcolm\", \"Barrett\", role = \"ctb\"), person(\"Rob\", \"Hyndman\", , \"<EMAIL>\", role = \"ctb\"), person(\"Romain\", \"Lesur\", role = \"ctb\"), person(\"Roy\", \"Storey\", role = \"ctb\"), person(\"Ruben\", \"Arslan\", , \"<EMAIL>\", role = \"ctb\"), person(\"Sergio\", \"Oller\", role = \"ctb\"), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(, \"jQuery UI contributors\", role = c(\"ctb\", \"cph\"), comment = \"jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt\"), person(\"Mark\", \"Otto\", role = \"ctb\", comment = \"Bootstrap library\"), person(\"Jacob\", \"Thornton\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Bootstrap contributors\", role = \"ctb\", comment = \"Bootstrap library\"), person(, \"Twitter, Inc\", role = \"cph\", comment = \"Bootstrap library\"), person(\"Alexander\", \"Farkas\", role = c(\"ctb\", \"cph\"), comment = \"html5shiv library\"), person(\"Scott\", \"Jehl\", role = c(\"ctb\", \"cph\"), comment = \"Respond.js library\"), person(\"Ivan\", \"Sagalaev\", role = c(\"ctb\", \"cph\"), comment = \"highlight.js library\"), person(\"Greg\", \"Franko\", role = c(\"ctb\", \"cph\"), comment = \"tocify library\"), person(\"John\", \"MacFarlane\", role = c(\"ctb\", \"cph\"), comment = \"Pandoc templates\"), person(, \"Google, Inc.\", role = c(\"ctb\", \"cph\"), comment = \"ioslides library\"), person(\"Dave\", \"Raggett\", role = \"ctb\", comment = \"slidy library\"), person(, \"W3C\", role = \"cph\", comment = \"slidy library\"), person(\"Dave\", \"Gandy\", role = c(\"ctb\", \"cph\"), comment = \"Font-Awesome\"), person(\"Ben\", \"Sperry\", role = \"ctb\", comment = \"Ionicons\"), person(, \"Drifty\", role = \"cph\", comment = \"Ionicons\"), person(\"Aidan\", \"Lister\", role = c(\"ctb\", \"cph\"), comment = \"jQuery StickyTabs\"), person(\"Benct Philip\", \"Jonsson\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\"), person(\"Albert\", \"Krewinkel\", role = c(\"ctb\", \"cph\"), comment = \"pagebreak Lua filter\") )", "Description": "Convert R Markdown documents into a variety of formats.", "License": "GPL-3", "URL": "https://github.com/rstudio/rmarkdown, https://pkgs.rstudio.com/rmarkdown/", "BugReports": "https://github.com/rstudio/rmarkdown/issues", "Depends": ["R (>= 3.0)"], "Imports": ["bslib (>= *******)", "evaluate (>= 0.13)", "fontawesome (>= 0.5.0)", "htmltools (>= 0.5.1)", "j<PERSON><PERSON><PERSON>", "jsonlite", "knitr (>= 1.43)", "methods", "tinytex (>= 0.31)", "tools", "utils", "xfun (>= 0.36)", "yaml (>= 2.1.19)"], "Suggests": ["digest", "dygraphs", "fs", "rsconnect", "downlit (>= 0.4.0)", "katex (>= 1.4.0)", "sass (>= 0.4.0)", "shiny (>= 1.6.0)", "testthat (>= 3.0.3)", "tibble", "vctrs", "cleanrmd", "withr (>= 2.4.2)", "xml2"], "VignetteBuilder": "knitr", "Config/Needs/website": "rstudio/quillt, pkgdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "pandoc (>= 1.14) - http://pandoc.org", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut], <PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0003-0645-5666>), <PERSON> [aut] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON> [ctb] (<https://orcid.org/0000-0003-0464-5036>), <PERSON><PERSON><PERSON> [ctb, cph] (<https://orcid.org/0000-0002-8335-495X>, Number sections Lua filter), <PERSON><PERSON> [ctb], <PERSON> [ctb] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0003-4900-788X>), <PERSON> [ctb], <PERSON><PERSON><PERSON><PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-4064-6012>), <PERSON> <PERSON> [ctb], Rob Hyn<PERSON> [ctb], <PERSON>in <PERSON>ur [ctb], <PERSON> <PERSON>y [ctb], Ruben Arslan [ctb], <PERSON> Oller [ctb], Posit Software, P<PERSON> [cph, fnd], jQuery UI contributors [ctb, cph] (jQuery UI library; authors listed in inst/rmd/h/jqueryui/AUTHORS.txt), Mark Otto [ctb] (Bootstrap library), Jacob Thornton [ctb] (Bootstrap library), Bootstrap contributors [ctb] (Bootstrap library), Twitter, Inc [cph] (Bootstrap library), Alexander Farkas [ctb, cph] (html5shiv library), Scott Jehl [ctb, cph] (Respond.js library), Ivan Sagalaev [ctb, cph] (highlight.js library), Greg Franko [ctb, cph] (tocify library), John MacFarlane [ctb, cph] (Pandoc templates), Google, Inc. [ctb, cph] (ioslides library), Dave Raggett [ctb] (slidy library), W3C [cph] (slidy library), Dave Gandy [ctb, cph] (Font-Awesome), Ben Sperry [ctb] (Ionicons), Drifty [cph] (Ionicons), Aidan Lister [ctb, cph] (jQuery StickyTabs), Benct Philip Jonsson [ctb, cph] (pagebreak Lua filter), Albert Krewinkel [ctb, cph] (pagebreak Lua filter)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "rprojroot": {"Package": "rprojroot", "Version": "2.0.4", "Source": "Repository", "Title": "Finding Files in Project Subdirectories", "Authors@R": "person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\"))", "Description": "Robust, reliable and flexible paths to files below a project root. The 'root' of a project is defined as a directory that matches a certain criterion, e.g., it contains a certain regular file.", "License": "MIT + file LICENSE", "URL": "https://rprojroot.r-lib.org/, https://github.com/r-lib/rprojroot", "BugReports": "https://github.com/r-lib/rprojroot/issues", "Depends": ["R (>= 3.0.0)"], "Suggests": ["covr", "knitr", "lifecycle", "mockr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre] (<https://orcid.org/0000-0002-1416-3412>)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "sass": {"Package": "sass", "Version": "0.4.10", "Source": "Repository", "Type": "Package", "Title": "Syntactically Awesome Style Sheets ('Sass')", "Description": "An 'SCSS' compiler, powered by the 'LibSass' library. With this, R developers can use variables, inheritance, and functions to generate dynamic style sheets. The package uses the 'Sass CSS' extension language, which is stable, powerful, and CSS compatible.", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0003-3925-190X\")), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", , \"<EMAIL>\", \"aut\", comment = c(ORCID = \"0000-0001-9986-114X\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"aut\", \"cre\"), comment = c(ORCID = \"0000-0002-4958-2844\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", c(\"ctb\"), comment = c(ORCID = \"0000-0003-4474-2498\")), person(family = \"RStudio\", role = c(\"cph\", \"fnd\")), person(family = \"Sass Open Source Foundation\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Mifsud\", \"<PERSON>\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Catlin\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"<PERSON>\", \"Weizenbaum\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Chris\", \"Eppstein\", role = c(\"ctb\", \"cph\"), comment = \"LibSass library\"), person(\"Adams\", \"Joseph\", role = c(\"ctb\", \"cph\"), comment = \"json.cpp\"), person(\"Trifunovic\", \"Nemanja\", role = c(\"ctb\", \"cph\"), comment = \"utf8.h\") )", "License": "MIT + file LICENSE", "URL": "https://rstudio.github.io/sass/, https://github.com/rstudio/sass", "BugReports": "https://github.com/rstudio/sass/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "SystemRequirements": "GNU make", "Imports": ["fs (>= 1.2.4)", "rlang (>= 0.4.10)", "htmltools (>= 0.5.1)", "R6", "rapp<PERSON>s"], "Suggests": ["testthat", "knitr", "rmarkdown", "withr", "shiny", "curl"], "VignetteBuilder": "knitr", "Config/testthat/edition": "3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut] (<https://orcid.org/0000-0003-3925-190X>), <PERSON><PERSON> [aut] (<https://orcid.org/0000-0001-9986-114X>), <PERSON> [aut, cre] (<https://orcid.org/0000-0002-4958-2844>), <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON>tu<PERSON> [cph, fnd], Sass Open Source Foundation [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON><PERSON><PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (LibSass library), <PERSON> [ctb, cph] (json.cpp), <PERSON><PERSON><PERSON><PERSON> [ctb, cph] (utf8.h)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "scales": {"Package": "scales", "Version": "1.4.0", "Source": "Repository", "Title": "Scale Functions for Visualization", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = c(\"cre\", \"aut\"), comment = c(ORCID = \"0000-0002-5147-4711\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"aut\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\"), comment = c(ROR = \"03wc8by49\")) )", "Description": "Graphical scales map data to aesthetics, and provide methods for automatically determining breaks and labels for axes and legends.", "License": "MIT + file LICENSE", "URL": "https://scales.r-lib.org, https://github.com/r-lib/scales", "BugReports": "https://github.com/r-lib/scales/issues", "Depends": ["R (>= 4.1)"], "Imports": ["cli", "farver (>= 2.0.3)", "glue", "labeling", "lifecycle", "R6", "RColorBrewer", "rlang (>= 1.1.0)", "viridisLite"], "Suggests": ["bit64", "covr", "dichromat", "ggplot2", "hms (>= 0.5.0)", "stringi", "testthat (>= 3.0.0)"], "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Config/usethis/last-upkeep": "2025-04-23", "Encoding": "UTF-8", "LazyLoad": "yes", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [cre, aut] (<https://orcid.org/0000-0002-5147-4711>), <PERSON> [aut], Posit Software, PBC [cph, fnd] (03wc8by49)", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tibble": {"Package": "tibble", "Version": "3.3.0", "Source": "Repository", "Title": "Simple Data Frames", "Authors@R": "c(person(given = \"Kirill\", family = \"M\\u00fcller\", role = c(\"aut\", \"cre\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"<PERSON>\", family = \"Wickham\", role = \"aut\", email = \"<EMAIL>\"), person(given = \"<PERSON>in\", family = \"<PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"<PERSON>\", family = \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\"), person(given = \"RStudio\", role = c(\"cph\", \"fnd\")))", "Description": "Provides a 'tbl_df' class (the 'tibble') with stricter checking and better formatting than the traditional data frame.", "License": "MIT + file LICENSE", "URL": "https://tibble.tidyverse.org/, https://github.com/tidyverse/tibble", "BugReports": "https://github.com/tidyverse/tibble/issues", "Depends": ["R (>= 3.4.0)"], "Imports": ["cli", "lifecycle (>= 1.0.0)", "magrit<PERSON>", "methods", "pillar (>= 1.8.1)", "pkgconfig", "rlang (>= 1.0.2)", "utils", "vctrs (>= 0.5.0)"], "Suggests": ["bench", "bit64", "blob", "brio", "callr", "DiagrammeR", "dplyr", "evaluate", "formattable", "ggplot2", "here", "hms", "htmltools", "knitr", "lubridate", "nycflights13", "pkgload", "purrr", "rmarkdown", "stringi", "testthat (>= 3.0.2)", "tidyr", "withr"], "VignetteBuilder": "knitr", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "Config/testthat/edition": "3", "Config/testthat/parallel": "true", "Config/testthat/start-first": "vignette-formats, as_tibble, add, invariants", "Config/autostyle/scope": "line_breaks", "Config/autostyle/strict": "true", "Config/autostyle/rmd": "false", "Config/Needs/website": "tidyverse/tidytemplate", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), <PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [cph, fnd]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "tinytex": {"Package": "tinytex", "Version": "0.57", "Source": "Repository", "Type": "Package", "Title": "Helper Functions to Install and Maintain TeX Live, and Compile LaTeX Documents", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\")), person(given = \"Posit Software, PBC\", role = c(\"cph\", \"fnd\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\", comment = c(ORCID = \"0000-0003-4474-2498\")), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-8549-0971\")), person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Helper functions to install and maintain the 'LaTeX' distribution named 'TinyTeX' (<https://yihui.org/tinytex/>), a lightweight, cross-platform, portable, and easy-to-maintain version of 'TeX Live'. This package also contains helper functions to compile 'LaTeX' documents, and install missing 'LaTeX' packages automatically.", "Imports": ["xfun (>= 0.48)"], "Suggests": ["testit", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "License": "MIT + file LICENSE", "URL": "https://github.com/rstudio/tinytex", "BugReports": "https://github.com/rstudio/tinytex/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "NeedsCompilation": "no", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>), Posit Software, PBC [cph, fnd], <PERSON> [ctb] (<https://orcid.org/0000-0003-4474-2498>), <PERSON> [ctb] (<https://orcid.org/0000-0002-8549-0971>), <PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "utf8": {"Package": "utf8", "Version": "1.2.6", "Source": "Repository", "Title": "Unicode Text Processing", "Authors@R": "c(person(given = c(\"<PERSON>\", \"<PERSON><PERSON>\"), family = \"<PERSON>\", role = c(\"aut\", \"cph\")), person(given = \"Kirill\", family = \"M\\u00fcller\", role = \"cre\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-1416-3412\")), person(given = \"Unicode, Inc.\", role = c(\"cph\", \"dtc\"), comment = \"Unicode Character Database\"))", "Description": "Process and print 'UTF-8' encoded international text (Unicode). Input, validate, normalize, encode, format, and display.", "License": "Apache License (== 2.0) | file LICENSE", "URL": "https://krlmlr.github.io/utf8/, https://github.com/krlmlr/utf8", "BugReports": "https://github.com/krlmlr/utf8/issues", "Depends": ["R (>= 2.10)"], "Suggests": ["cli", "covr", "knitr", "rlang", "rmarkdown", "testthat (>= 3.0.0)", "withr"], "VignetteBuilder": "knitr, rmarkdown", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2.9000", "NeedsCompilation": "yes", "Author": "<PERSON> [aut, cph], <PERSON><PERSON> [cre] (ORCID: <https://orcid.org/0000-0002-1416-3412>), Unicode, Inc. [cph, dtc] (Unicode Character Database)", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Title": "Vector Helpers", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"had<PERSON>@posit.co\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"data.table team\", role = \"cph\", comment = \"Radix sort based on data.table's forder() and their contribution to R's order()\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "Defines new notions of prototype and size that are used to provide tools for consistent and well-founded type-coercion and size-recycling, and are in turn connected to ideas of type- and size-stability useful for analysing function interfaces.", "License": "MIT + file LICENSE", "URL": "https://vctrs.r-lib.org/, https://github.com/r-lib/vctrs", "BugReports": "https://github.com/r-lib/vctrs/issues", "Depends": ["R (>= 3.5.0)"], "Imports": ["cli (>= 3.4.0)", "glue", "lifecycle (>= 1.0.3)", "rlang (>= 1.1.0)"], "Suggests": ["bit64", "covr", "crayon", "dplyr (>= 0.8.5)", "generics", "knitr", "pillar (>= 1.4.4)", "pkgdown (>= 2.0.1)", "rmarkdown", "testthat (>= 3.0.0)", "tibble (>= 3.1.3)", "waldo (>= 0.2.0)", "withr", "xml2", "zeallot"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "Language": "en-GB", "RoxygenNote": "7.2.3", "NeedsCompilation": "yes", "Author": "<PERSON> [aut], <PERSON> [aut], <PERSON> [aut, cre], data.table team [cph] (Radix sort based on data.table's forder() and their contribution to R's order()), Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "viridisLite": {"Package": "viridisLite", "Version": "0.4.2", "Source": "Repository", "Type": "Package", "Title": "Colorblind-Friendly Color Maps (Lite Version)", "Date": "2023-05-02", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", role = c(\"ctb\", \"cph\")), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>here<PERSON>\", email = \"<EMAIL>\", role = c(\"ctb\", \"cph\")) )", "Maintainer": "<PERSON> <<EMAIL>>", "Description": "Color maps designed to improve graph readability for readers with  common forms of color blindness and/or color vision deficiency. The color  maps are also perceptually-uniform, both in regular form and also when  converted to black-and-white for printing. This is the 'lite' version of the  'viridis' package that also contains 'ggplot2' bindings for discrete and  continuous color and fill scales and can be found at  <https://cran.r-project.org/package=viridis>.", "License": "MIT + file LICENSE", "Encoding": "UTF-8", "Depends": ["R (>= 2.10)"], "Suggests": ["hexbin (>= 1.27.0)", "ggplot2 (>= 1.0.1)", "testthat", "covr"], "URL": "https://sjmgarnier.github.io/viridisLite/, https://github.com/sjmgarnier/viridisLite/", "BugReports": "https://github.com/sjmgarnier/viridisLite/issues/", "RoxygenNote": "7.2.3", "NeedsCompilation": "no", "Author": "<PERSON> [aut, cre], <PERSON><PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON> [ctb, cph], <PERSON><PERSON><PERSON><PERSON> [ctb, cph], <PERSON><PERSON><PERSON> [ctb, cph]", "Repository": "CRAN"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Title": "Run Code 'With' Temporarily Modified Global State", "Authors@R": "c( person(\"<PERSON>\", \"<PERSON><PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = c(\"aut\", \"cre\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON>\", , \"kev<PERSON><PERSON>@gmail.com\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", , \"<EMAIL>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"aut\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Posit Software, PBC\", role = c(\"cph\", \"fnd\")) )", "Description": "A set of functions to run code 'with' safely and temporarily modified global state. Many of these functions were originally a part of the 'devtools' package, this provides a simple package with limited dependencies to provide access to these functions.", "License": "MIT + file LICENSE", "URL": "https://withr.r-lib.org, https://github.com/r-lib/withr#readme", "BugReports": "https://github.com/r-lib/withr/issues", "Depends": ["R (>= 3.6.0)"], "Imports": ["graphics", "grDevices"], "Suggests": ["callr", "DBI", "knitr", "methods", "rlang", "rmarkdown (>= 2.12)", "RSQLite", "testthat (>= 3.0.0)"], "VignetteBuilder": "knitr", "Config/Needs/website": "tidyverse/tidytemplate", "Config/testthat/edition": "3", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "Collate": "'aaa.R' 'collate.R' 'connection.R' 'db.R' 'defer-exit.R' 'standalone-defer.R' 'defer.R' 'devices.R' 'local_.R' 'with_.R' 'dir.R' 'env.R' 'file.R' 'language.R' 'libpaths.R' 'locale.R' 'makevars.R' 'namespace.R' 'options.R' 'par.R' 'path.R' 'rng.R' 'seed.R' 'wrap.R' 'sink.R' 'tempfile.R' 'timezone.R' 'torture.R' 'utils.R' 'with.R'", "NeedsCompilation": "no", "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [aut], <PERSON> [ctb], <PERSON> [ctb], Posit Software, PBC [cph, fnd]", "Maintainer": "<PERSON> <<EMAIL>>", "Repository": "CRAN"}, "xfun": {"Package": "xfun", "Version": "0.52", "Source": "Repository", "Type": "Package", "Title": "Supporting Functions for Packages Maintained by '<PERSON><PERSON>'", "Authors@R": "c( person(\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", role = c(\"aut\", \"cre\", \"cph\"), email = \"<EMAIL>\", comment = c(ORCID = \"0000-0003-0645-5666\", URL = \"https://yihui.org\")), person(\"<PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"Dai<PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON><PERSON>\", \"<PERSON>\", role = \"ctb\"), person(\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", role = \"ctb\", email = \"<EMAIL>\", comment = c(ORCID = \"0000-0002-5329-5987\")), person(\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", role = \"ctb\"), person() )", "Description": "Miscellaneous functions commonly used in other packages maintained by '<PERSON><PERSON>'.", "Depends": ["R (>= 3.2.0)"], "Imports": ["grDevices", "stats", "tools"], "Suggests": ["testit", "parallel", "codetools", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytex (>= 0.30)", "mime", "litedown (>= 0.4)", "commonmark", "knitr (>= 1.50)", "remotes", "pak", "curl", "xml2", "jsonlite", "magick", "yaml", "qs"], "License": "MIT + file LICENSE", "URL": "https://github.com/yihui/xfun", "BugReports": "https://github.com/yihui/xfun/issues", "Encoding": "UTF-8", "RoxygenNote": "7.3.2", "VignetteBuilder": "litedown", "NeedsCompilation": "yes", "Author": "<PERSON><PERSON> [aut, cre, cph] (<https://orcid.org/0000-0003-0645-5666>, https://yihui.org), <PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON><PERSON> [ctb] (<https://orcid.org/0000-0002-5329-5987>), <PERSON> [ctb]", "Maintainer": "<PERSON><PERSON> <<EMAIL>>", "Repository": "CRAN"}, "yaml": {"Package": "yaml", "Version": "2.3.10", "Source": "Repository", "Type": "Package", "Title": "Methods to Convert R Data to YAML and Back", "Date": "2024-07-22", "Suggests": ["RUnit"], "Author": "<PERSON> [aut], <PERSON> [aut, cre], <PERSON><PERSON> [aut], <PERSON><PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON><PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON> [ctb], <PERSON><PERSON> [ctb], <PERSON> [ctb]", "Maintainer": "<PERSON> <<EMAIL>>", "License": "BSD_3_clause + file LICENSE", "Description": "Implements the 'libyaml' 'YAML' 1.1 parser and emitter (<https://pyyaml.org/wiki/LibYAML>) for R.", "URL": "https://github.com/vubiostat/r-yaml/", "BugReports": "https://github.com/vubiostat/r-yaml/issues", "NeedsCompilation": "yes", "Repository": "CRAN"}}}