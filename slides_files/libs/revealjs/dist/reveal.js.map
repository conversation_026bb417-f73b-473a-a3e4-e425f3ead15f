{"version": 3, "file": "reveal.js", "sources": ["../js/utils/util.js", "../js/utils/device.js", "../node_modules/fitty/dist/fitty.module.js", "../js/controllers/slidecontent.js", "../js/utils/constants.js", "../js/controllers/slidenumber.js", "../js/controllers/jumptoslide.js", "../js/utils/color.js", "../js/controllers/backgrounds.js", "../js/controllers/autoanimate.js", "../js/controllers/scrollview.js", "../js/controllers/printview.js", "../js/controllers/fragments.js", "../js/controllers/overview.js", "../js/controllers/keyboard.js", "../js/controllers/location.js", "../js/controllers/controls.js", "../js/controllers/progress.js", "../js/controllers/pointer.js", "../js/utils/loader.js", "../js/controllers/plugins.js", "../js/controllers/touch.js", "../js/controllers/focus.js", "../js/controllers/notes.js", "../js/components/playback.js", "../js/config.js", "../js/reveal.js", "../js/index.js"], "sourcesContent": ["/**\n * Extend object a with the properties of object b.\n * If there's a conflict, object b takes precedence.\n *\n * @param {object} a\n * @param {object} b\n */\nexport const extend = ( a, b ) => {\n\n\tfor( let i in b ) {\n\t\ta[ i ] = b[ i ];\n\t}\n\n\treturn a;\n\n}\n\n/**\n * querySelectorAll but returns an Array.\n */\nexport const queryAll = ( el, selector ) => {\n\n\treturn Array.from( el.querySelectorAll( selector ) );\n\n}\n\n/**\n * classList.toggle() with cross browser support\n */\nexport const toggleClass = ( el, className, value ) => {\n\tif( value ) {\n\t\tel.classList.add( className );\n\t}\n\telse {\n\t\tel.classList.remove( className );\n\t}\n}\n\n/**\n * Utility for deserializing a value.\n *\n * @param {*} value\n * @return {*}\n */\nexport const deserialize = ( value ) => {\n\n\tif( typeof value === 'string' ) {\n\t\tif( value === 'null' ) return null;\n\t\telse if( value === 'true' ) return true;\n\t\telse if( value === 'false' ) return false;\n\t\telse if( value.match( /^-?[\\d\\.]+$/ ) ) return parseFloat( value );\n\t}\n\n\treturn value;\n\n}\n\n/**\n * Measures the distance in pixels between point a\n * and point b.\n *\n * @param {object} a point with x/y properties\n * @param {object} b point with x/y properties\n *\n * @return {number}\n */\nexport const distanceBetween = ( a, b ) => {\n\n\tlet dx = a.x - b.x,\n\t\tdy = a.y - b.y;\n\n\treturn Math.sqrt( dx*dx + dy*dy );\n\n}\n\n/**\n * Applies a CSS transform to the target element.\n *\n * @param {HTMLElement} element\n * @param {string} transform\n */\nexport const transformElement = ( element, transform ) => {\n\n\telement.style.transform = transform;\n\n}\n\n/**\n * Element.matches with IE support.\n *\n * @param {HTMLElement} target The element to match\n * @param {String} selector The CSS selector to match\n * the element against\n *\n * @return {Boolean}\n */\nexport const matches = ( target, selector ) => {\n\n\tlet matchesMethod = target.matches || target.matchesSelector || target.msMatchesSelector;\n\n\treturn !!( matchesMethod && matchesMethod.call( target, selector ) );\n\n}\n\n/**\n * Find the closest parent that matches the given\n * selector.\n *\n * @param {HTMLElement} target The child element\n * @param {String} selector The CSS selector to match\n * the parents against\n *\n * @return {HTMLElement} The matched parent or null\n * if no matching parent was found\n */\nexport const closest = ( target, selector ) => {\n\n\t// Native Element.closest\n\tif( typeof target.closest === 'function' ) {\n\t\treturn target.closest( selector );\n\t}\n\n\t// Polyfill\n\twhile( target ) {\n\t\tif( matches( target, selector ) ) {\n\t\t\treturn target;\n\t\t}\n\n\t\t// Keep searching\n\t\ttarget = target.parentNode;\n\t}\n\n\treturn null;\n\n}\n\n/**\n * Handling the fullscreen functionality via the fullscreen API\n *\n * @see http://fullscreen.spec.whatwg.org/\n * @see https://developer.mozilla.org/en-US/docs/DOM/Using_fullscreen_mode\n */\nexport const enterFullscreen = element => {\n\n\telement = element || document.documentElement;\n\n\t// Check which implementation is available\n\tlet requestMethod = element.requestFullscreen ||\n\t\t\t\t\t\telement.webkitRequestFullscreen ||\n\t\t\t\t\t\telement.webkitRequestFullScreen ||\n\t\t\t\t\t\telement.mozRequestFullScreen ||\n\t\t\t\t\t\telement.msRequestFullscreen;\n\n\tif( requestMethod ) {\n\t\trequestMethod.apply( element );\n\t}\n\n}\n\n/**\n * Creates an HTML element and returns a reference to it.\n * If the element already exists the existing instance will\n * be returned.\n *\n * @param {HTMLElement} container\n * @param {string} tagname\n * @param {string} classname\n * @param {string} innerHTML\n *\n * @return {HTMLElement}\n */\nexport const createSingletonNode = ( container, tagname, classname, innerHTML='' ) => {\n\n\t// Find all nodes matching the description\n\tlet nodes = container.querySelectorAll( '.' + classname );\n\n\t// Check all matches to find one which is a direct child of\n\t// the specified container\n\tfor( let i = 0; i < nodes.length; i++ ) {\n\t\tlet testNode = nodes[i];\n\t\tif( testNode.parentNode === container ) {\n\t\t\treturn testNode;\n\t\t}\n\t}\n\n\t// If no node was found, create it now\n\tlet node = document.createElement( tagname );\n\tnode.className = classname;\n\tnode.innerHTML = innerHTML;\n\tcontainer.appendChild( node );\n\n\treturn node;\n\n}\n\n/**\n * Injects the given CSS styles into the DOM.\n *\n * @param {string} value\n */\nexport const createStyleSheet = ( value ) => {\n\n\tlet tag = document.createElement( 'style' );\n\ttag.type = 'text/css';\n\n\tif( value && value.length > 0 ) {\n\t\tif( tag.styleSheet ) {\n\t\t\ttag.styleSheet.cssText = value;\n\t\t}\n\t\telse {\n\t\t\ttag.appendChild( document.createTextNode( value ) );\n\t\t}\n\t}\n\n\tdocument.head.appendChild( tag );\n\n\treturn tag;\n\n}\n\n/**\n * Returns a key:value hash of all query params.\n */\nexport const getQueryHash = () => {\n\n\tlet query = {};\n\n\tlocation.search.replace( /[A-Z0-9]+?=([\\w\\.%-]*)/gi, a => {\n\t\tquery[ a.split( '=' ).shift() ] = a.split( '=' ).pop();\n\t} );\n\n\t// Basic deserialization\n\tfor( let i in query ) {\n\t\tlet value = query[ i ];\n\n\t\tquery[ i ] = deserialize( unescape( value ) );\n\t}\n\n\t// Do not accept new dependencies via query config to avoid\n\t// the potential of malicious script injection\n\tif( typeof query['dependencies'] !== 'undefined' ) delete query['dependencies'];\n\n\treturn query;\n\n}\n\n/**\n * Returns the remaining height within the parent of the\n * target element.\n *\n * remaining height = [ configured parent height ] - [ current parent height ]\n *\n * @param {HTMLElement} element\n * @param {number} [height]\n */\nexport const getRemainingHeight = ( element, height = 0 ) => {\n\n\tif( element ) {\n\t\tlet newHeight, oldHeight = element.style.height;\n\n\t\t// Change the .stretch element height to 0 in order find the height of all\n\t\t// the other elements\n\t\telement.style.height = '0px';\n\n\t\t// In Overview mode, the parent (.slide) height is set of 700px.\n\t\t// Restore it temporarily to its natural height.\n\t\telement.parentNode.style.height = 'auto';\n\n\t\tnewHeight = height - element.parentNode.offsetHeight;\n\n\t\t// Restore the old height, just in case\n\t\telement.style.height = oldHeight + 'px';\n\n\t\t// Clear the parent (.slide) height. .removeProperty works in IE9+\n\t\telement.parentNode.style.removeProperty('height');\n\n\t\treturn newHeight;\n\t}\n\n\treturn height;\n\n}\n\nconst fileExtensionToMimeMap = {\n\t'mp4': 'video/mp4',\n\t'm4a': 'video/mp4',\n\t'ogv': 'video/ogg',\n\t'mpeg': 'video/mpeg',\n\t'webm': 'video/webm'\n}\n\n/**\n * Guess the MIME type for common file formats.\n */\nexport const getMimeTypeFromFile = ( filename='' ) => {\n\treturn fileExtensionToMimeMap[filename.split('.').pop()]\n}\n\n/**\n * Encodes a string for RFC3986-compliant URL format.\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/encodeURI#encoding_for_rfc3986\n *\n * @param {string} url\n */\nexport const encodeRFC3986URI = ( url='' ) => {\n\treturn encodeURI(url)\n\t  .replace(/%5B/g, \"[\")\n\t  .replace(/%5D/g, \"]\")\n\t  .replace(\n\t\t/[!'()*]/g,\n\t\t(c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`\n\t  );\n}", "const UA = navigator.userAgent;\n\nexport const isMobile = /(iphone|ipod|ipad|android)/gi.test( UA ) ||\n\t\t\t\t\t\t( navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1 ); // iPadOS\n\nexport const isChrome = /chrome/i.test( UA ) && !/edge/i.test( UA );\n\nexport const isAndroid = /android/gi.test( UA );", "/**\n * fitty v2.3.7 - Snugly resizes text to fit its parent container\n * Copyright (c) 2023 R<PERSON> Schennink <<EMAIL>> (https://pqina.nl/)\n */\n\nvar e=function(e){if(e){var t=function(e){return[].slice.call(e)},n=0,i=1,r=2,o=3,a=[],l=null,u=\"requestAnimationFrame\"in e?function(){e.cancelAnimationFrame(l),l=e.requestAnimationFrame((function(){return s(a.filter((function(e){return e.dirty&&e.active})))}))}:function(){},c=function(e){return function(){a.forEach((function(t){return t.dirty=e})),u()}},s=function(e){e.filter((function(e){return!e.styleComputed})).forEach((function(e){e.styleComputed=m(e)})),e.filter(y).forEach(v);var t=e.filter(p);t.forEach(d),t.forEach((function(e){v(e),f(e)})),t.forEach(S)},f=function(e){return e.dirty=n},d=function(e){e.availableWidth=e.element.parentNode.clientWidth,e.currentWidth=e.element.scrollWidth,e.previousFontSize=e.currentFontSize,e.currentFontSize=Math.min(Math.max(e.minSize,e.availableWidth/e.currentWidth*e.previousFontSize),e.maxSize),e.whiteSpace=e.multiLine&&e.currentFontSize===e.minSize?\"normal\":\"nowrap\"},p=function(e){return e.dirty!==r||e.dirty===r&&e.element.parentNode.clientWidth!==e.availableWidth},m=function(t){var n=e.getComputedStyle(t.element,null);return t.currentFontSize=parseFloat(n.getPropertyValue(\"font-size\")),t.display=n.getPropertyValue(\"display\"),t.whiteSpace=n.getPropertyValue(\"white-space\"),!0},y=function(e){var t=!1;return!e.preStyleTestCompleted&&(/inline-/.test(e.display)||(t=!0,e.display=\"inline-block\"),\"nowrap\"!==e.whiteSpace&&(t=!0,e.whiteSpace=\"nowrap\"),e.preStyleTestCompleted=!0,t)},v=function(e){e.element.style.whiteSpace=e.whiteSpace,e.element.style.display=e.display,e.element.style.fontSize=e.currentFontSize+\"px\"},S=function(e){e.element.dispatchEvent(new CustomEvent(\"fit\",{detail:{oldValue:e.previousFontSize,newValue:e.currentFontSize,scaleFactor:e.currentFontSize/e.previousFontSize}}))},h=function(e,t){return function(){e.dirty=t,e.active&&u()}},w=function(e){return function(){a=a.filter((function(t){return t.element!==e.element})),e.observeMutations&&e.observer.disconnect(),e.element.style.whiteSpace=e.originalStyle.whiteSpace,e.element.style.display=e.originalStyle.display,e.element.style.fontSize=e.originalStyle.fontSize}},b=function(e){return function(){e.active||(e.active=!0,u())}},z=function(e){return function(){return e.active=!1}},F=function(e){e.observeMutations&&(e.observer=new MutationObserver(h(e,i)),e.observer.observe(e.element,e.observeMutations))},g={minSize:16,maxSize:512,multiLine:!0,observeMutations:\"MutationObserver\"in e&&{subtree:!0,childList:!0,characterData:!0}},W=null,E=function(){e.clearTimeout(W),W=e.setTimeout(c(r),x.observeWindowDelay)},M=[\"resize\",\"orientationchange\"];return Object.defineProperty(x,\"observeWindow\",{set:function(t){var n=\"\".concat(t?\"add\":\"remove\",\"EventListener\");M.forEach((function(t){e[n](t,E)}))}}),x.observeWindow=!0,x.observeWindowDelay=100,x.fitAll=c(o),x}function C(e,t){var n=Object.assign({},g,t),i=e.map((function(e){var t=Object.assign({},n,{element:e,active:!0});return function(e){e.originalStyle={whiteSpace:e.element.style.whiteSpace,display:e.element.style.display,fontSize:e.element.style.fontSize},F(e),e.newbie=!0,e.dirty=!0,a.push(e)}(t),{element:e,fit:h(t,o),unfreeze:b(t),freeze:z(t),unsubscribe:w(t)}}));return u(),i}function x(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return\"string\"==typeof e?C(t(document.querySelectorAll(e)),n):C([e],n)[0]}}(\"undefined\"==typeof window?null:window);export default e;\n", "import { extend, queryAll, closest, getMimeTypeFromFile, encodeRFC3986URI } from '../utils/util.js'\nimport { isMobile } from '../utils/device.js'\n\nimport fitty from 'fitty';\n\n/**\n * Handles loading, unloading and playback of slide\n * content such as images, videos and iframes.\n */\nexport default class SlideContent {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.startEmbeddedIframe = this.startEmbeddedIframe.bind( this );\n\n\t}\n\n\t/**\n\t * Should the given element be preloaded?\n\t * Decides based on local element attributes and global config.\n\t *\n\t * @param {HTMLElement} element\n\t */\n\tshouldPreload( element ) {\n\n\t\tif( this.Reveal.isScrollView() ) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Prefer an explicit global preload setting\n\t\tlet preload = this.Reveal.getConfig().preloadIframes;\n\n\t\t// If no global setting is available, fall back on the element's\n\t\t// own preload setting\n\t\tif( typeof preload !== 'boolean' ) {\n\t\t\tpreload = element.hasAttribute( 'data-preload' );\n\t\t}\n\n\t\treturn preload;\n\t}\n\n\t/**\n\t * Called when the given slide is within the configured view\n\t * distance. Shows the slide element and loads any content\n\t * that is set to load lazily (data-src).\n\t *\n\t * @param {HTMLElement} slide Slide to show\n\t */\n\tload( slide, options = {} ) {\n\n\t\t// Show the slide element\n\t\tslide.style.display = this.Reveal.getConfig().display;\n\n\t\t// Media elements with data-src attributes\n\t\tqueryAll( slide, 'img[data-src], video[data-src], audio[data-src], iframe[data-src]' ).forEach( element => {\n\t\t\tif( element.tagName !== 'IFRAME' || this.shouldPreload( element ) ) {\n\t\t\t\telement.setAttribute( 'src', element.getAttribute( 'data-src' ) );\n\t\t\t\telement.setAttribute( 'data-lazy-loaded', '' );\n\t\t\t\telement.removeAttribute( 'data-src' );\n\t\t\t}\n\t\t} );\n\n\t\t// Media elements with <source> children\n\t\tqueryAll( slide, 'video, audio' ).forEach( media => {\n\t\t\tlet sources = 0;\n\n\t\t\tqueryAll( media, 'source[data-src]' ).forEach( source => {\n\t\t\t\tsource.setAttribute( 'src', source.getAttribute( 'data-src' ) );\n\t\t\t\tsource.removeAttribute( 'data-src' );\n\t\t\t\tsource.setAttribute( 'data-lazy-loaded', '' );\n\t\t\t\tsources += 1;\n\t\t\t} );\n\n\t\t\t// Enable inline video playback in mobile Safari\n\t\t\tif( isMobile && media.tagName === 'VIDEO' ) {\n\t\t\t\tmedia.setAttribute( 'playsinline', '' );\n\t\t\t}\n\n\t\t\t// If we rewrote sources for this video/audio element, we need\n\t\t\t// to manually tell it to load from its new origin\n\t\t\tif( sources > 0 ) {\n\t\t\t\tmedia.load();\n\t\t\t}\n\t\t} );\n\n\n\t\t// Show the corresponding background element\n\t\tlet background = slide.slideBackgroundElement;\n\t\tif( background ) {\n\t\t\tbackground.style.display = 'block';\n\n\t\t\tlet backgroundContent = slide.slideBackgroundContentElement;\n\t\t\tlet backgroundIframe = slide.getAttribute( 'data-background-iframe' );\n\n\t\t\t// If the background contains media, load it\n\t\t\tif( background.hasAttribute( 'data-loaded' ) === false ) {\n\t\t\t\tbackground.setAttribute( 'data-loaded', 'true' );\n\n\t\t\t\tlet backgroundImage = slide.getAttribute( 'data-background-image' ),\n\t\t\t\t\tbackgroundVideo = slide.getAttribute( 'data-background-video' ),\n\t\t\t\t\tbackgroundVideoLoop = slide.hasAttribute( 'data-background-video-loop' ),\n\t\t\t\t\tbackgroundVideoMuted = slide.hasAttribute( 'data-background-video-muted' );\n\n\t\t\t\t// Images\n\t\t\t\tif( backgroundImage ) {\n\t\t\t\t\t// base64\n\t\t\t\t\tif(  /^data:/.test( backgroundImage.trim() ) ) {\n\t\t\t\t\t\tbackgroundContent.style.backgroundImage = `url(${backgroundImage.trim()})`;\n\t\t\t\t\t}\n\t\t\t\t\t// URL(s)\n\t\t\t\t\telse {\n\t\t\t\t\t\tbackgroundContent.style.backgroundImage = backgroundImage.split( ',' ).map( background => {\n\t\t\t\t\t\t\t// Decode URL(s) that are already encoded first\n\t\t\t\t\t\t\tlet decoded = decodeURI(background.trim());\n\t\t\t\t\t\t\treturn `url(${encodeRFC3986URI(decoded)})`;\n\t\t\t\t\t\t}).join( ',' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Videos\n\t\t\t\telse if ( backgroundVideo && !this.Reveal.isSpeakerNotes() ) {\n\t\t\t\t\tlet video = document.createElement( 'video' );\n\n\t\t\t\t\tif( backgroundVideoLoop ) {\n\t\t\t\t\t\tvideo.setAttribute( 'loop', '' );\n\t\t\t\t\t}\n\n\t\t\t\t\tif( backgroundVideoMuted ) {\n\t\t\t\t\t\tvideo.muted = true;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Enable inline playback in mobile Safari\n\t\t\t\t\t//\n\t\t\t\t\t// Mute is required for video to play when using\n\t\t\t\t\t// swipe gestures to navigate since they don't\n\t\t\t\t\t// count as direct user actions :'(\n\t\t\t\t\tif( isMobile ) {\n\t\t\t\t\t\tvideo.muted = true;\n\t\t\t\t\t\tvideo.setAttribute( 'playsinline', '' );\n\t\t\t\t\t}\n\n\t\t\t\t\t// Support comma separated lists of video sources\n\t\t\t\t\tbackgroundVideo.split( ',' ).forEach( source => {\n\t\t\t\t\t\tconst sourceElement = document.createElement( 'source' );\n\t\t\t\t\t\tsourceElement.setAttribute( 'src', source );\n\n\t\t\t\t\t\tlet type = getMimeTypeFromFile( source );\n\t\t\t\t\t\tif( type ) {\n\t\t\t\t\t\t\tsourceElement.setAttribute( 'type', type );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvideo.appendChild( sourceElement );\n\t\t\t\t\t} );\n\n\t\t\t\t\tbackgroundContent.appendChild( video );\n\t\t\t\t}\n\t\t\t\t// Iframes\n\t\t\t\telse if( backgroundIframe && options.excludeIframes !== true ) {\n\t\t\t\t\tlet iframe = document.createElement( 'iframe' );\n\t\t\t\t\tiframe.setAttribute( 'allowfullscreen', '' );\n\t\t\t\t\tiframe.setAttribute( 'mozallowfullscreen', '' );\n\t\t\t\t\tiframe.setAttribute( 'webkitallowfullscreen', '' );\n\t\t\t\t\tiframe.setAttribute( 'allow', 'autoplay' );\n\n\t\t\t\t\tiframe.setAttribute( 'data-src', backgroundIframe );\n\n\t\t\t\t\tiframe.style.width  = '100%';\n\t\t\t\t\tiframe.style.height = '100%';\n\t\t\t\t\tiframe.style.maxHeight = '100%';\n\t\t\t\t\tiframe.style.maxWidth = '100%';\n\n\t\t\t\t\tbackgroundContent.appendChild( iframe );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Start loading preloadable iframes\n\t\t\tlet backgroundIframeElement = backgroundContent.querySelector( 'iframe[data-src]' );\n\t\t\tif( backgroundIframeElement ) {\n\n\t\t\t\t// Check if this iframe is eligible to be preloaded\n\t\t\t\tif( this.shouldPreload( background ) && !/autoplay=(1|true|yes)/gi.test( backgroundIframe ) ) {\n\t\t\t\t\tif( backgroundIframeElement.getAttribute( 'src' ) !== backgroundIframe ) {\n\t\t\t\t\t\tbackgroundIframeElement.setAttribute( 'src', backgroundIframe );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.layout( slide );\n\n\t}\n\n\t/**\n\t * Applies JS-dependent layout helpers for the scope.\n\t */\n\tlayout( scopeElement ) {\n\n\t\t// Autosize text with the r-fit-text class based on the\n\t\t// size of its container. This needs to happen after the\n\t\t// slide is visible in order to measure the text.\n\t\tArray.from( scopeElement.querySelectorAll( '.r-fit-text' ) ).forEach( element => {\n\t\t\tfitty( element, {\n\t\t\t\tminSize: 24,\n\t\t\t\tmaxSize: this.Reveal.getConfig().height * 0.8,\n\t\t\t\tobserveMutations: false,\n\t\t\t\tobserveWindow: false\n\t\t\t} );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Unloads and hides the given slide. This is called when the\n\t * slide is moved outside of the configured view distance.\n\t *\n\t * @param {HTMLElement} slide\n\t */\n\tunload( slide ) {\n\n\t\t// Hide the slide element\n\t\tslide.style.display = 'none';\n\n\t\t// Hide the corresponding background element\n\t\tlet background = this.Reveal.getSlideBackground( slide );\n\t\tif( background ) {\n\t\t\tbackground.style.display = 'none';\n\n\t\t\t// Unload any background iframes\n\t\t\tqueryAll( background, 'iframe[src]' ).forEach( element => {\n\t\t\t\telement.removeAttribute( 'src' );\n\t\t\t} );\n\t\t}\n\n\t\t// Reset lazy-loaded media elements with src attributes\n\t\tqueryAll( slide, 'video[data-lazy-loaded][src], audio[data-lazy-loaded][src], iframe[data-lazy-loaded][src]' ).forEach( element => {\n\t\t\telement.setAttribute( 'data-src', element.getAttribute( 'src' ) );\n\t\t\telement.removeAttribute( 'src' );\n\t\t} );\n\n\t\t// Reset lazy-loaded media elements with <source> children\n\t\tqueryAll( slide, 'video[data-lazy-loaded] source[src], audio source[src]' ).forEach( source => {\n\t\t\tsource.setAttribute( 'data-src', source.getAttribute( 'src' ) );\n\t\t\tsource.removeAttribute( 'src' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Enforces origin-specific format rules for embedded media.\n\t */\n\tformatEmbeddedContent() {\n\n\t\tlet _appendParamToIframeSource = ( sourceAttribute, sourceURL, param ) => {\n\t\t\tqueryAll( this.Reveal.getSlidesElement(), 'iframe['+ sourceAttribute +'*=\"'+ sourceURL +'\"]' ).forEach( el => {\n\t\t\t\tlet src = el.getAttribute( sourceAttribute );\n\t\t\t\tif( src && src.indexOf( param ) === -1 ) {\n\t\t\t\t\tel.setAttribute( sourceAttribute, src + ( !/\\?/.test( src ) ? '?' : '&' ) + param );\n\t\t\t\t}\n\t\t\t});\n\t\t};\n\n\t\t// YouTube frames must include \"?enablejsapi=1\"\n\t\t_appendParamToIframeSource( 'src', 'youtube.com/embed/', 'enablejsapi=1' );\n\t\t_appendParamToIframeSource( 'data-src', 'youtube.com/embed/', 'enablejsapi=1' );\n\n\t\t// Vimeo frames must include \"?api=1\"\n\t\t_appendParamToIframeSource( 'src', 'player.vimeo.com/', 'api=1' );\n\t\t_appendParamToIframeSource( 'data-src', 'player.vimeo.com/', 'api=1' );\n\n\t}\n\n\t/**\n\t * Start playback of any embedded content inside of\n\t * the given element.\n\t *\n\t * @param {HTMLElement} element\n\t */\n\tstartEmbeddedContent( element ) {\n\n\t\tif( element && !this.Reveal.isSpeakerNotes() ) {\n\n\t\t\t// Restart GIFs\n\t\t\tqueryAll( element, 'img[src$=\".gif\"]' ).forEach( el => {\n\t\t\t\t// Setting the same unchanged source like this was confirmed\n\t\t\t\t// to work in Chrome, FF & Safari\n\t\t\t\tel.setAttribute( 'src', el.getAttribute( 'src' ) );\n\t\t\t} );\n\n\t\t\t// HTML5 media elements\n\t\t\tqueryAll( element, 'video, audio' ).forEach( el => {\n\t\t\t\tif( closest( el, '.fragment' ) && !closest( el, '.fragment.visible' ) ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Prefer an explicit global autoplay setting\n\t\t\t\tlet autoplay = this.Reveal.getConfig().autoPlayMedia;\n\n\t\t\t\t// If no global setting is available, fall back on the element's\n\t\t\t\t// own autoplay setting\n\t\t\t\tif( typeof autoplay !== 'boolean' ) {\n\t\t\t\t\tautoplay = el.hasAttribute( 'data-autoplay' ) || !!closest( el, '.slide-background' );\n\t\t\t\t}\n\n\t\t\t\tif( autoplay && typeof el.play === 'function' ) {\n\n\t\t\t\t\t// If the media is ready, start playback\n\t\t\t\t\tif( el.readyState > 1 ) {\n\t\t\t\t\t\tthis.startEmbeddedMedia( { target: el } );\n\t\t\t\t\t}\n\t\t\t\t\t// Mobile devices never fire a loaded event so instead\n\t\t\t\t\t// of waiting, we initiate playback\n\t\t\t\t\telse if( isMobile ) {\n\t\t\t\t\t\tlet promise = el.play();\n\n\t\t\t\t\t\t// If autoplay does not work, ensure that the controls are visible so\n\t\t\t\t\t\t// that the viewer can start the media on their own\n\t\t\t\t\t\tif( promise && typeof promise.catch === 'function' && el.controls === false ) {\n\t\t\t\t\t\t\tpromise.catch( () => {\n\t\t\t\t\t\t\t\tel.controls = true;\n\n\t\t\t\t\t\t\t\t// Once the video does start playing, hide the controls again\n\t\t\t\t\t\t\t\tel.addEventListener( 'play', () => {\n\t\t\t\t\t\t\t\t\tel.controls = false;\n\t\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// If the media isn't loaded, wait before playing\n\t\t\t\t\telse {\n\t\t\t\t\t\tel.removeEventListener( 'loadeddata', this.startEmbeddedMedia ); // remove first to avoid dupes\n\t\t\t\t\t\tel.addEventListener( 'loadeddata', this.startEmbeddedMedia );\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Normal iframes\n\t\t\tqueryAll( element, 'iframe[src]' ).forEach( el => {\n\t\t\t\tif( closest( el, '.fragment' ) && !closest( el, '.fragment.visible' ) ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.startEmbeddedIframe( { target: el } );\n\t\t\t} );\n\n\t\t\t// Lazy loading iframes\n\t\t\tqueryAll( element, 'iframe[data-src]' ).forEach( el => {\n\t\t\t\tif( closest( el, '.fragment' ) && !closest( el, '.fragment.visible' ) ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif( el.getAttribute( 'src' ) !== el.getAttribute( 'data-src' ) ) {\n\t\t\t\t\tel.removeEventListener( 'load', this.startEmbeddedIframe ); // remove first to avoid dupes\n\t\t\t\t\tel.addEventListener( 'load', this.startEmbeddedIframe );\n\t\t\t\t\tel.setAttribute( 'src', el.getAttribute( 'data-src' ) );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Starts playing an embedded video/audio element after\n\t * it has finished loading.\n\t *\n\t * @param {object} event\n\t */\n\tstartEmbeddedMedia( event ) {\n\n\t\tlet isAttachedToDOM = !!closest( event.target, 'html' ),\n\t\t\tisVisible  \t\t= !!closest( event.target, '.present' );\n\n\t\tif( isAttachedToDOM && isVisible ) {\n\t\t\t// Don't restart if media is already playing\n\t\t\tif( event.target.paused || event.target.ended ) {\n\t\t\t\tevent.target.currentTime = 0;\n\t\t\t\tevent.target.play();\n\t\t\t}\n\t\t}\n\n\t\tevent.target.removeEventListener( 'loadeddata', this.startEmbeddedMedia );\n\n\t}\n\n\t/**\n\t * \"Starts\" the content of an embedded iframe using the\n\t * postMessage API.\n\t *\n\t * @param {object} event\n\t */\n\tstartEmbeddedIframe( event ) {\n\n\t\tlet iframe = event.target;\n\n\t\tif( iframe && iframe.contentWindow ) {\n\n\t\t\tlet isAttachedToDOM = !!closest( event.target, 'html' ),\n\t\t\t\tisVisible  \t\t= !!closest( event.target, '.present' );\n\n\t\t\tif( isAttachedToDOM && isVisible ) {\n\n\t\t\t\t// Prefer an explicit global autoplay setting\n\t\t\t\tlet autoplay = this.Reveal.getConfig().autoPlayMedia;\n\n\t\t\t\t// If no global setting is available, fall back on the element's\n\t\t\t\t// own autoplay setting\n\t\t\t\tif( typeof autoplay !== 'boolean' ) {\n\t\t\t\t\tautoplay = iframe.hasAttribute( 'data-autoplay' ) || !!closest( iframe, '.slide-background' );\n\t\t\t\t}\n\n\t\t\t\t// YouTube postMessage API\n\t\t\t\tif( /youtube\\.com\\/embed\\//.test( iframe.getAttribute( 'src' ) ) && autoplay ) {\n\t\t\t\t\tiframe.contentWindow.postMessage( '{\"event\":\"command\",\"func\":\"playVideo\",\"args\":\"\"}', '*' );\n\t\t\t\t}\n\t\t\t\t// Vimeo postMessage API\n\t\t\t\telse if( /player\\.vimeo\\.com\\//.test( iframe.getAttribute( 'src' ) ) && autoplay ) {\n\t\t\t\t\tiframe.contentWindow.postMessage( '{\"method\":\"play\"}', '*' );\n\t\t\t\t}\n\t\t\t\t// Generic postMessage API\n\t\t\t\telse {\n\t\t\t\t\tiframe.contentWindow.postMessage( 'slide:start', '*' );\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Stop playback of any embedded content inside of\n\t * the targeted slide.\n\t *\n\t * @param {HTMLElement} element\n\t */\n\tstopEmbeddedContent( element, options = {} ) {\n\n\t\toptions = extend( {\n\t\t\t// Defaults\n\t\t\tunloadIframes: true\n\t\t}, options );\n\n\t\tif( element && element.parentNode ) {\n\t\t\t// HTML5 media elements\n\t\t\tqueryAll( element, 'video, audio' ).forEach( el => {\n\t\t\t\tif( !el.hasAttribute( 'data-ignore' ) && typeof el.pause === 'function' ) {\n\t\t\t\t\tel.setAttribute('data-paused-by-reveal', '');\n\t\t\t\t\tel.pause();\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Generic postMessage API for non-lazy loaded iframes\n\t\t\tqueryAll( element, 'iframe' ).forEach( el => {\n\t\t\t\tif( el.contentWindow ) el.contentWindow.postMessage( 'slide:stop', '*' );\n\t\t\t\tel.removeEventListener( 'load', this.startEmbeddedIframe );\n\t\t\t});\n\n\t\t\t// YouTube postMessage API\n\t\t\tqueryAll( element, 'iframe[src*=\"youtube.com/embed/\"]' ).forEach( el => {\n\t\t\t\tif( !el.hasAttribute( 'data-ignore' ) && el.contentWindow && typeof el.contentWindow.postMessage === 'function' ) {\n\t\t\t\t\tel.contentWindow.postMessage( '{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}', '*' );\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// Vimeo postMessage API\n\t\t\tqueryAll( element, 'iframe[src*=\"player.vimeo.com/\"]' ).forEach( el => {\n\t\t\t\tif( !el.hasAttribute( 'data-ignore' ) && el.contentWindow && typeof el.contentWindow.postMessage === 'function' ) {\n\t\t\t\t\tel.contentWindow.postMessage( '{\"method\":\"pause\"}', '*' );\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tif( options.unloadIframes === true ) {\n\t\t\t\t// Unload lazy-loaded iframes\n\t\t\t\tqueryAll( element, 'iframe[data-src]' ).forEach( el => {\n\t\t\t\t\t// Only removing the src doesn't actually unload the frame\n\t\t\t\t\t// in all browsers (Firefox) so we set it to blank first\n\t\t\t\t\tel.setAttribute( 'src', 'about:blank' );\n\t\t\t\t\tel.removeAttribute( 'src' );\n\t\t\t\t} );\n\t\t\t}\n\t\t}\n\n\t}\n\n}\n", "\nexport const SLIDES_SELECTOR = '.slides section';\nexport const HORIZONTAL_SLIDES_SELECTOR = '.slides>section';\nexport const VERTICAL_SLIDES_SELECTOR = '.slides>section.present>section';\nexport const HORIZONTAL_BACKGROUNDS_SELECTOR = '.backgrounds>.slide-background';\n\n// Methods that may not be invoked via the postMessage API\nexport const POST_MESSAGE_METHOD_BLACKLIST = /registerPlugin|registerKeyboardShortcut|addKeyBinding|addEventListener|showPreview/;\n\n// Regex for retrieving the fragment style from a class attribute\nexport const FRAGMENT_STYLE_REGEX = /fade-(down|up|right|left|out|in-then-out|in-then-semi-out)|semi-fade-out|current-visible|shrink|grow/;\n\n// Slide number formats\nexport const SLIDE_NUMBER_FORMAT_HORIZONTAL_DOT_VERTICAL = 'h.v';\nexport const SLIDE_NUMBER_FORMAT_HORIZONTAL_SLASH_VERTICAL = 'h/v';\nexport const SLIDE_NUMBER_FORMAT_CURRENT = 'c';\nexport const SLIDE_NUMBER_FORMAT_CURRENT_SLASH_TOTAL = 'c/t';", "import {\n\tSLIDE_NUMBER_FORMAT_CURRENT,\n\tSLIDE_NUMBER_FORMAT_CURRENT_SLASH_TOTAL,\n\tSLIDE_NUMBER_FORMAT_HORIZONTAL_DOT_VERTICAL,\n\tSLIDE_NUMBER_FORMAT_HORIZONTAL_SLASH_VERTICAL\n} from \"../utils/constants\";\n\n/**\n * Handles the display of reveal.js' optional slide number.\n */\nexport default class SlideNumber {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\trender() {\n\n\t\tthis.element = document.createElement( 'div' );\n\t\tthis.element.className = 'slide-number';\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tlet slideNumberDisplay = 'none';\n\t\tif( config.slideNumber && !this.Reveal.isPrintView() ) {\n\t\t\tif( config.showSlideNumber === 'all' ) {\n\t\t\t\tslideNumberDisplay = 'block';\n\t\t\t}\n\t\t\telse if( config.showSlideNumber === 'speaker' && this.Reveal.isSpeakerNotes() ) {\n\t\t\t\tslideNumberDisplay = 'block';\n\t\t\t}\n\t\t}\n\n\t\tthis.element.style.display = slideNumberDisplay;\n\n\t}\n\n\t/**\n\t * Updates the slide number to match the current slide.\n\t */\n\tupdate() {\n\n\t\t// Update slide number if enabled\n\t\tif( this.Reveal.getConfig().slideNumber && this.element ) {\n\t\t\tthis.element.innerHTML = this.getSlideNumber();\n\t\t}\n\n\t}\n\n\t/**\n\t * Returns the HTML string corresponding to the current slide\n\t * number, including formatting.\n\t */\n\tgetSlideNumber( slide = this.Reveal.getCurrentSlide() ) {\n\n\t\tlet config = this.Reveal.getConfig();\n\t\tlet value;\n\t\tlet format = SLIDE_NUMBER_FORMAT_HORIZONTAL_DOT_VERTICAL;\n\n\t\tif ( typeof config.slideNumber === 'function' ) {\n\t\t\tvalue = config.slideNumber( slide );\n\t\t} else {\n\t\t\t// Check if a custom number format is available\n\t\t\tif( typeof config.slideNumber === 'string' ) {\n\t\t\t\tformat = config.slideNumber;\n\t\t\t}\n\n\t\t\t// If there are ONLY vertical slides in this deck, always use\n\t\t\t// a flattened slide number\n\t\t\tif( !/c/.test( format ) && this.Reveal.getHorizontalSlides().length === 1 ) {\n\t\t\t\tformat = SLIDE_NUMBER_FORMAT_CURRENT;\n\t\t\t}\n\n\t\t\t// Offset the current slide number by 1 to make it 1-indexed\n\t\t\tlet horizontalOffset = slide && slide.dataset.visibility === 'uncounted' ? 0 : 1;\n\n\t\t\tvalue = [];\n\t\t\tswitch( format ) {\n\t\t\t\tcase SLIDE_NUMBER_FORMAT_CURRENT:\n\t\t\t\t\tvalue.push( this.Reveal.getSlidePastCount( slide ) + horizontalOffset );\n\t\t\t\t\tbreak;\n\t\t\t\tcase SLIDE_NUMBER_FORMAT_CURRENT_SLASH_TOTAL:\n\t\t\t\t\tvalue.push( this.Reveal.getSlidePastCount( slide ) + horizontalOffset, '/', this.Reveal.getTotalSlides() );\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tlet indices = this.Reveal.getIndices( slide );\n\t\t\t\t\tvalue.push( indices.h + horizontalOffset );\n\t\t\t\t\tlet sep = format === SLIDE_NUMBER_FORMAT_HORIZONTAL_SLASH_VERTICAL ? '/' : '.';\n\t\t\t\t\tif( this.Reveal.isVerticalSlide( slide ) ) value.push( sep, indices.v + 1 );\n\t\t\t}\n\t\t}\n\n\t\tlet url = '#' + this.Reveal.location.getHash( slide );\n\t\treturn this.formatNumber( value[0], value[1], value[2], url );\n\n\t}\n\n\t/**\n\t * Applies HTML formatting to a slide number before it's\n\t * written to the DOM.\n\t *\n\t * @param {number} a Current slide\n\t * @param {string} delimiter Character to separate slide numbers\n\t * @param {(number|*)} b Total slides\n\t * @param {HTMLElement} [url='#'+locationHash()] The url to link to\n\t * @return {string} HTML string fragment\n\t */\n\tformatNumber( a, delimiter, b, url = '#' + this.Reveal.location.getHash() ) {\n\n\t\tif( typeof b === 'number' && !isNaN( b ) ) {\n\t\t\treturn  `<a href=\"${url}\">\n\t\t\t\t\t<span class=\"slide-number-a\">${a}</span>\n\t\t\t\t\t<span class=\"slide-number-delimiter\">${delimiter}</span>\n\t\t\t\t\t<span class=\"slide-number-b\">${b}</span>\n\t\t\t\t\t</a>`;\n\t\t}\n\t\telse {\n\t\t\treturn `<a href=\"${url}\">\n\t\t\t\t\t<span class=\"slide-number-a\">${a}</span>\n\t\t\t\t\t</a>`;\n\t\t}\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.element.remove();\n\n\t}\n\n}", "import {\n\tSLIDE_NUMBER_FORMAT_CURRENT,\n\tSLIDE_NUMBER_FORMAT_CURRENT_SLASH_TOTAL\n} from \"../utils/constants\";\n\n/**\n * Makes it possible to jump to a slide by entering its\n * slide number or id.\n */\nexport default class JumpToSlide {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.onInput = this.onInput.bind( this );\n\t\tthis.onBlur = this.onBlur.bind( this );\n\t\tthis.onKeyDown = this.onKeyDown.bind( this );\n\n\t}\n\n\trender() {\n\n\t\tthis.element = document.createElement( 'div' );\n\t\tthis.element.className = 'jump-to-slide';\n\n    this.jumpInput = document.createElement( 'input' );\n    this.jumpInput.type = 'text';\n    this.jumpInput.className = 'jump-to-slide-input';\n    this.jumpInput.placeholder = 'Jump to slide';\n\t\tthis.jumpInput.addEventListener( 'input', this.onInput );\n\t\tthis.jumpInput.addEventListener( 'keydown', this.onKeyDown );\n\t\tthis.jumpInput.addEventListener( 'blur', this.onBlur );\n\n    this.element.appendChild( this.jumpInput );\n\n\t}\n\n\tshow() {\n\n\t\tthis.indicesOnShow = this.Reveal.getIndices();\n\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\t\tthis.jumpInput.focus();\n\n\t}\n\n\thide() {\n\n\t\tif( this.isVisible() ) {\n\t\t\tthis.element.remove();\n\t\t\tthis.jumpInput.value = '';\n\n\t\t\tclearTimeout( this.jumpTimeout );\n\t\t\tdelete this.jumpTimeout;\n\t\t}\n\n\t}\n\n\tisVisible() {\n\n\t\treturn !!this.element.parentNode;\n\n\t}\n\n\t/**\n\t * Parses the current input and jumps to the given slide.\n\t */\n\tjump() {\n\n\t\tclearTimeout( this.jumpTimeout );\n\t\tdelete this.jumpTimeout;\n\n\t\tlet query = this.jumpInput.value.trim( '' );\n\t\tlet indices;\n\n\t\t// When slide numbers are formatted to be a single linear mumber\n\t\t// (instead of showing a separate horizontal/vertical index) we\n\t\t// use the same format for slide jumps\n\t\tif( /^\\d+$/.test( query ) ) {\n\t\t\tconst slideNumberFormat = this.Reveal.getConfig().slideNumber;\n\t\t\tif( slideNumberFormat === SLIDE_NUMBER_FORMAT_CURRENT || slideNumberFormat === SLIDE_NUMBER_FORMAT_CURRENT_SLASH_TOTAL ) {\n\t\t\t\tconst slide = this.Reveal.getSlides()[ parseInt( query, 10 ) - 1 ];\n\t\t\t\tif( slide ) {\n\t\t\t\t\tindices = this.Reveal.getIndices( slide );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif( !indices ) {\n\t\t\t// If the query uses \"horizontal.vertical\" format, convert to\n\t\t\t// \"horizontal/vertical\" so that our URL parser can understand\n\t\t\tif( /^\\d+\\.\\d+$/.test( query ) ) {\n\t\t\t\tquery = query.replace( '.', '/' );\n\t\t\t}\n\n\t\t\tindices = this.Reveal.location.getIndicesFromHash( query, { oneBasedIndex: true } );\n\t\t}\n\n\t\t// Still no valid index? Fall back on a text search\n\t\tif( !indices && /\\S+/i.test( query ) && query.length > 1 ) {\n\t\t\tindices = this.search( query );\n\t\t}\n\n\t\tif( indices && query !== '' ) {\n\t\t\tthis.Reveal.slide( indices.h, indices.v, indices.f );\n\t\t\treturn true;\n\t\t}\n\t\telse {\n\t\t\tthis.Reveal.slide( this.indicesOnShow.h, this.indicesOnShow.v, this.indicesOnShow.f );\n\t\t\treturn false;\n\t\t}\n\n\t}\n\n\tjumpAfter( delay ) {\n\n\t\tclearTimeout( this.jumpTimeout );\n\t\tthis.jumpTimeout = setTimeout( () => this.jump(), delay );\n\n\t}\n\n\t/**\n\t * A lofi search that looks for the given query in all\n\t * of our slides and returns the first match.\n\t */\n\tsearch( query ) {\n\n\t\tconst regex = new RegExp( '\\\\b' + query.trim() + '\\\\b', 'i' );\n\n\t\tconst slide = this.Reveal.getSlides().find( ( slide ) => {\n\t\t\treturn regex.test( slide.innerText );\n\t\t} );\n\n\t\tif( slide ) {\n\t\t\treturn this.Reveal.getIndices( slide );\n\t\t}\n\t\telse {\n\t\t\treturn null;\n\t\t}\n\n\t}\n\n\t/**\n\t * Reverts back to the slide we were on when jump to slide was\n\t * invoked.\n\t */\n\tcancel() {\n\n\t\tthis.Reveal.slide( this.indicesOnShow.h, this.indicesOnShow.v, this.indicesOnShow.f );\n\t\tthis.hide();\n\n\t}\n\n\tconfirm() {\n\n\t\tthis.jump();\n\t\tthis.hide();\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.jumpInput.removeEventListener( 'input', this.onInput );\n\t\tthis.jumpInput.removeEventListener( 'keydown', this.onKeyDown );\n\t\tthis.jumpInput.removeEventListener( 'blur', this.onBlur );\n\n\t\tthis.element.remove();\n\n\t}\n\n\tonKeyDown( event ) {\n\n\t\tif( event.keyCode === 13 ) {\n\t\t\tthis.confirm();\n\t\t}\n\t\telse if( event.keyCode === 27 ) {\n\t\t\tthis.cancel();\n\n\t\t\tevent.stopImmediatePropagation();\n\t\t}\n\n\t}\n\n\tonInput( event ) {\n\n\t\tthis.jumpAfter( 200 );\n\n\t}\n\n\tonBlur() {\n\n\t\tsetTimeout( () => this.hide(), 1 );\n\n\t}\n\n}", "/**\n * Converts various color input formats to an {r:0,g:0,b:0} object.\n *\n * @param {string} color The string representation of a color\n * @example\n * colorToRgb('#000');\n * @example\n * colorToRgb('#000000');\n * @example\n * colorToRgb('rgb(0,0,0)');\n * @example\n * colorToRgb('rgba(0,0,0)');\n *\n * @return {{r: number, g: number, b: number, [a]: number}|null}\n */\nexport const colorToRgb = ( color ) => {\n\n\tlet hex3 = color.match( /^#([0-9a-f]{3})$/i );\n\tif( hex3 && hex3[1] ) {\n\t\thex3 = hex3[1];\n\t\treturn {\n\t\t\tr: parseInt( hex3.charAt( 0 ), 16 ) * 0x11,\n\t\t\tg: parseInt( hex3.charAt( 1 ), 16 ) * 0x11,\n\t\t\tb: parseInt( hex3.charAt( 2 ), 16 ) * 0x11\n\t\t};\n\t}\n\n\tlet hex6 = color.match( /^#([0-9a-f]{6})$/i );\n\tif( hex6 && hex6[1] ) {\n\t\thex6 = hex6[1];\n\t\treturn {\n\t\t\tr: parseInt( hex6.slice( 0, 2 ), 16 ),\n\t\t\tg: parseInt( hex6.slice( 2, 4 ), 16 ),\n\t\t\tb: parseInt( hex6.slice( 4, 6 ), 16 )\n\t\t};\n\t}\n\n\tlet rgb = color.match( /^rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)$/i );\n\tif( rgb ) {\n\t\treturn {\n\t\t\tr: parseInt( rgb[1], 10 ),\n\t\t\tg: parseInt( rgb[2], 10 ),\n\t\t\tb: parseInt( rgb[3], 10 )\n\t\t};\n\t}\n\n\tlet rgba = color.match( /^rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\,\\s*([\\d]+|[\\d]*.[\\d]+)\\s*\\)$/i );\n\tif( rgba ) {\n\t\treturn {\n\t\t\tr: parseInt( rgba[1], 10 ),\n\t\t\tg: parseInt( rgba[2], 10 ),\n\t\t\tb: parseInt( rgba[3], 10 ),\n\t\t\ta: parseFloat( rgba[4] )\n\t\t};\n\t}\n\n\treturn null;\n\n}\n\n/**\n * Calculates brightness on a scale of 0-255.\n *\n * @param {string} color See colorToRgb for supported formats.\n * @see {@link colorToRgb}\n */\nexport const colorBrightness = ( color ) => {\n\n\tif( typeof color === 'string' ) color = colorToRgb( color );\n\n\tif( color ) {\n\t\treturn ( color.r * 299 + color.g * 587 + color.b * 114 ) / 1000;\n\t}\n\n\treturn null;\n\n}", "import { queryAll } from '../utils/util.js'\nimport { colorToRgb, colorBrightness } from '../utils/color.js'\n\n/**\n * Creates and updates slide backgrounds.\n */\nexport default class Backgrounds {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\trender() {\n\n\t\tthis.element = document.createElement( 'div' );\n\t\tthis.element.className = 'backgrounds';\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\n\t}\n\n\t/**\n\t * Creates the slide background elements and appends them\n\t * to the background container. One element is created per\n\t * slide no matter if the given slide has visible background.\n\t */\n\tcreate() {\n\n\t\t// Clear prior backgrounds\n\t\tthis.element.innerHTML = '';\n\t\tthis.element.classList.add( 'no-transition' );\n\n\t\t// Iterate over all horizontal slides\n\t\tthis.Reveal.getHorizontalSlides().forEach( slideh => {\n\n\t\t\tlet backgroundStack = this.createBackground( slideh, this.element );\n\n\t\t\t// Iterate over all vertical slides\n\t\t\tqueryAll( slideh, 'section' ).forEach( slidev => {\n\n\t\t\t\tthis.createBackground( slidev, backgroundStack );\n\n\t\t\t\tbackgroundStack.classList.add( 'stack' );\n\n\t\t\t} );\n\n\t\t} );\n\n\t\t// Add parallax background if specified\n\t\tif( this.Reveal.getConfig().parallaxBackgroundImage ) {\n\n\t\t\tthis.element.style.backgroundImage = 'url(\"' + this.Reveal.getConfig().parallaxBackgroundImage + '\")';\n\t\t\tthis.element.style.backgroundSize = this.Reveal.getConfig().parallaxBackgroundSize;\n\t\t\tthis.element.style.backgroundRepeat = this.Reveal.getConfig().parallaxBackgroundRepeat;\n\t\t\tthis.element.style.backgroundPosition = this.Reveal.getConfig().parallaxBackgroundPosition;\n\n\t\t\t// Make sure the below properties are set on the element - these properties are\n\t\t\t// needed for proper transitions to be set on the element via CSS. To remove\n\t\t\t// annoying background slide-in effect when the presentation starts, apply\n\t\t\t// these properties after short time delay\n\t\t\tsetTimeout( () => {\n\t\t\t\tthis.Reveal.getRevealElement().classList.add( 'has-parallax-background' );\n\t\t\t}, 1 );\n\n\t\t}\n\t\telse {\n\n\t\t\tthis.element.style.backgroundImage = '';\n\t\t\tthis.Reveal.getRevealElement().classList.remove( 'has-parallax-background' );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Creates a background for the given slide.\n\t *\n\t * @param {HTMLElement} slide\n\t * @param {HTMLElement} container The element that the background\n\t * should be appended to\n\t * @return {HTMLElement} New background div\n\t */\n\tcreateBackground( slide, container ) {\n\n\t\t// Main slide background element\n\t\tlet element = document.createElement( 'div' );\n\t\telement.className = 'slide-background ' + slide.className.replace( /present|past|future/, '' );\n\n\t\t// Inner background element that wraps images/videos/iframes\n\t\tlet contentElement = document.createElement( 'div' );\n\t\tcontentElement.className = 'slide-background-content';\n\n\t\telement.appendChild( contentElement );\n\t\tcontainer.appendChild( element );\n\n\t\tslide.slideBackgroundElement = element;\n\t\tslide.slideBackgroundContentElement = contentElement;\n\n\t\t// Syncs the background to reflect all current background settings\n\t\tthis.sync( slide );\n\n\t\treturn element;\n\n\t}\n\n\t/**\n\t * Renders all of the visual properties of a slide background\n\t * based on the various background attributes.\n\t *\n\t * @param {HTMLElement} slide\n\t */\n\tsync( slide ) {\n\n\t\tconst element = slide.slideBackgroundElement,\n\t\t\tcontentElement = slide.slideBackgroundContentElement;\n\n\t\tconst data = {\n\t\t\tbackground: slide.getAttribute( 'data-background' ),\n\t\t\tbackgroundSize: slide.getAttribute( 'data-background-size' ),\n\t\t\tbackgroundImage: slide.getAttribute( 'data-background-image' ),\n\t\t\tbackgroundVideo: slide.getAttribute( 'data-background-video' ),\n\t\t\tbackgroundIframe: slide.getAttribute( 'data-background-iframe' ),\n\t\t\tbackgroundColor: slide.getAttribute( 'data-background-color' ),\n\t\t\tbackgroundGradient: slide.getAttribute( 'data-background-gradient' ),\n\t\t\tbackgroundRepeat: slide.getAttribute( 'data-background-repeat' ),\n\t\t\tbackgroundPosition: slide.getAttribute( 'data-background-position' ),\n\t\t\tbackgroundTransition: slide.getAttribute( 'data-background-transition' ),\n\t\t\tbackgroundOpacity: slide.getAttribute( 'data-background-opacity' ),\n\t\t};\n\n\t\tconst dataPreload = slide.hasAttribute( 'data-preload' );\n\n\t\t// Reset the prior background state in case this is not the\n\t\t// initial sync\n\t\tslide.classList.remove( 'has-dark-background' );\n\t\tslide.classList.remove( 'has-light-background' );\n\n\t\telement.removeAttribute( 'data-loaded' );\n\t\telement.removeAttribute( 'data-background-hash' );\n\t\telement.removeAttribute( 'data-background-size' );\n\t\telement.removeAttribute( 'data-background-transition' );\n\t\telement.style.backgroundColor = '';\n\n\t\tcontentElement.style.backgroundSize = '';\n\t\tcontentElement.style.backgroundRepeat = '';\n\t\tcontentElement.style.backgroundPosition = '';\n\t\tcontentElement.style.backgroundImage = '';\n\t\tcontentElement.style.opacity = '';\n\t\tcontentElement.innerHTML = '';\n\n\t\tif( data.background ) {\n\t\t\t// Auto-wrap image urls in url(...)\n\t\t\tif( /^(http|file|\\/\\/)/gi.test( data.background ) || /\\.(svg|png|jpg|jpeg|gif|bmp|webp)([?#\\s]|$)/gi.test( data.background ) ) {\n\t\t\t\tslide.setAttribute( 'data-background-image', data.background );\n\t\t\t}\n\t\t\telse {\n\t\t\t\telement.style.background = data.background;\n\t\t\t}\n\t\t}\n\n\t\t// Create a hash for this combination of background settings.\n\t\t// This is used to determine when two slide backgrounds are\n\t\t// the same.\n\t\tif( data.background || data.backgroundColor || data.backgroundGradient || data.backgroundImage || data.backgroundVideo || data.backgroundIframe ) {\n\t\t\telement.setAttribute( 'data-background-hash', data.background +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundSize +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundImage +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundVideo +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundIframe +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundColor +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundGradient +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundRepeat +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundPosition +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundTransition +\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdata.backgroundOpacity );\n\t\t}\n\n\t\t// Additional and optional background properties\n\t\tif( data.backgroundSize ) element.setAttribute( 'data-background-size', data.backgroundSize );\n\t\tif( data.backgroundColor ) element.style.backgroundColor = data.backgroundColor;\n\t\tif( data.backgroundGradient ) element.style.backgroundImage = data.backgroundGradient;\n\t\tif( data.backgroundTransition ) element.setAttribute( 'data-background-transition', data.backgroundTransition );\n\n\t\tif( dataPreload ) element.setAttribute( 'data-preload', '' );\n\n\t\t// Background image options are set on the content wrapper\n\t\tif( data.backgroundSize ) contentElement.style.backgroundSize = data.backgroundSize;\n\t\tif( data.backgroundRepeat ) contentElement.style.backgroundRepeat = data.backgroundRepeat;\n\t\tif( data.backgroundPosition ) contentElement.style.backgroundPosition = data.backgroundPosition;\n\t\tif( data.backgroundOpacity ) contentElement.style.opacity = data.backgroundOpacity;\n\n\t\tconst contrastClass = this.getContrastClass( slide );\n\n\t\tif( typeof contrastClass === 'string' ) {\n\t\t\tslide.classList.add( contrastClass );\n\t\t}\n\n\t}\n\n\t/**\n\t * Returns a class name that can be applied to a slide to indicate\n\t * if it has a light or dark background.\n\t *\n\t * @param {*} slide\n\t *\n\t * @returns {string|null}\n\t */\n\tgetContrastClass( slide ) {\n\n\t\tconst element = slide.slideBackgroundElement;\n\n\t\t// If this slide has a background color, we add a class that\n\t\t// signals if it is light or dark. If the slide has no background\n\t\t// color, no class will be added\n\t\tlet contrastColor = slide.getAttribute( 'data-background-color' );\n\n\t\t// If no bg color was found, or it cannot be converted by colorToRgb, check the computed background\n\t\tif( !contrastColor || !colorToRgb( contrastColor ) ) {\n\t\t\tlet computedBackgroundStyle = window.getComputedStyle( element );\n\t\t\tif( computedBackgroundStyle && computedBackgroundStyle.backgroundColor ) {\n\t\t\t\tcontrastColor = computedBackgroundStyle.backgroundColor;\n\t\t\t}\n\t\t}\n\n\t\tif( contrastColor ) {\n\t\t\tconst rgb = colorToRgb( contrastColor );\n\n\t\t\t// Ignore fully transparent backgrounds. Some browsers return\n\t\t\t// rgba(0,0,0,0) when reading the computed background color of\n\t\t\t// an element with no background\n\t\t\tif( rgb && rgb.a !== 0 ) {\n\t\t\t\tif( colorBrightness( contrastColor ) < 128 ) {\n\t\t\t\t\treturn 'has-dark-background';\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 'has-light-background';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\n\t}\n\n\t/**\n\t * Bubble the 'has-light-background'/'has-dark-background' classes.\n\t */\n\tbubbleSlideContrastClassToElement( slide, target ) {\n\n\t\t[ 'has-light-background', 'has-dark-background' ].forEach( classToBubble => {\n\t\t\tif( slide.classList.contains( classToBubble ) ) {\n\t\t\t\ttarget.classList.add( classToBubble );\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttarget.classList.remove( classToBubble );\n\t\t\t}\n\t\t}, this );\n\n\t}\n\n\t/**\n\t * Updates the background elements to reflect the current\n\t * slide.\n\t *\n\t * @param {boolean} includeAll If true, the backgrounds of\n\t * all vertical slides (not just the present) will be updated.\n\t */\n\tupdate( includeAll = false ) {\n\n\t\tlet config = this.Reveal.getConfig();\n\t\tlet currentSlide = this.Reveal.getCurrentSlide();\n\t\tlet indices = this.Reveal.getIndices();\n\n\t\tlet currentBackground = null;\n\n\t\t// Reverse past/future classes when in RTL mode\n\t\tlet horizontalPast = config.rtl ? 'future' : 'past',\n\t\t\thorizontalFuture = config.rtl ? 'past' : 'future';\n\n\t\t// Update the classes of all backgrounds to match the\n\t\t// states of their slides (past/present/future)\n\t\tArray.from( this.element.childNodes ).forEach( ( backgroundh, h ) => {\n\n\t\t\tbackgroundh.classList.remove( 'past', 'present', 'future' );\n\n\t\t\tif( h < indices.h ) {\n\t\t\t\tbackgroundh.classList.add( horizontalPast );\n\t\t\t}\n\t\t\telse if ( h > indices.h ) {\n\t\t\t\tbackgroundh.classList.add( horizontalFuture );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbackgroundh.classList.add( 'present' );\n\n\t\t\t\t// Store a reference to the current background element\n\t\t\t\tcurrentBackground = backgroundh;\n\t\t\t}\n\n\t\t\tif( includeAll || h === indices.h ) {\n\t\t\t\tqueryAll( backgroundh, '.slide-background' ).forEach( ( backgroundv, v ) => {\n\n\t\t\t\t\tbackgroundv.classList.remove( 'past', 'present', 'future' );\n\n\t\t\t\t\tconst indexv = typeof indices.v === 'number' ? indices.v : 0;\n\n\t\t\t\t\tif( v < indexv ) {\n\t\t\t\t\t\tbackgroundv.classList.add( 'past' );\n\t\t\t\t\t}\n\t\t\t\t\telse if ( v > indexv ) {\n\t\t\t\t\t\tbackgroundv.classList.add( 'future' );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tbackgroundv.classList.add( 'present' );\n\n\t\t\t\t\t\t// Only if this is the present horizontal and vertical slide\n\t\t\t\t\t\tif( h === indices.h ) currentBackground = backgroundv;\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\t\t\t}\n\n\t\t} );\n\n\t\t// The previous background may refer to a DOM element that has\n\t\t// been removed after a presentation is synced & bgs are recreated\n\t\tif( this.previousBackground && !this.previousBackground.closest( 'body' ) ) {\n\t\t\tthis.previousBackground = null;\n\t\t}\n\n\t\tif( currentBackground && this.previousBackground ) {\n\n\t\t\t// Don't transition between identical backgrounds. This\n\t\t\t// prevents unwanted flicker.\n\t\t\tlet previousBackgroundHash = this.previousBackground.getAttribute( 'data-background-hash' );\n\t\t\tlet currentBackgroundHash = currentBackground.getAttribute( 'data-background-hash' );\n\n\t\t\tif( currentBackgroundHash && currentBackgroundHash === previousBackgroundHash && currentBackground !== this.previousBackground ) {\n\t\t\t\tthis.element.classList.add( 'no-transition' );\n\n\t\t\t\t// If multiple slides have the same background video, carry\n\t\t\t\t// the <video> element forward so that it plays continuously\n\t\t\t\t// across multiple slides\n\t\t\t\tconst currentVideo = currentBackground.querySelector( 'video' );\n\t\t\t\tconst previousVideo = this.previousBackground.querySelector( 'video' );\n\n\t\t\t\tif( currentVideo && previousVideo ) {\n\n\t\t\t\t\tconst currentVideoParent = currentVideo.parentNode;\n\t\t\t\t\tconst previousVideoParent = previousVideo.parentNode;\n\n\t\t\t\t\t// Swap the two videos\n\t\t\t\t\tpreviousVideoParent.appendChild( currentVideo );\n\t\t\t\t\tcurrentVideoParent.appendChild( previousVideo );\n\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// Stop content inside of previous backgrounds\n\t\tif( this.previousBackground ) {\n\n\t\t\tthis.Reveal.slideContent.stopEmbeddedContent( this.previousBackground, { unloadIframes: !this.Reveal.slideContent.shouldPreload( this.previousBackground ) } );\n\n\t\t}\n\n\t\t// Start content in the current background\n\t\tif( currentBackground ) {\n\n\t\t\tthis.Reveal.slideContent.startEmbeddedContent( currentBackground );\n\n\t\t\tlet currentBackgroundContent = currentBackground.querySelector( '.slide-background-content' );\n\t\t\tif( currentBackgroundContent ) {\n\n\t\t\t\tlet backgroundImageURL = currentBackgroundContent.style.backgroundImage || '';\n\n\t\t\t\t// Restart GIFs (doesn't work in Firefox)\n\t\t\t\tif( /\\.gif/i.test( backgroundImageURL ) ) {\n\t\t\t\t\tcurrentBackgroundContent.style.backgroundImage = '';\n\t\t\t\t\twindow.getComputedStyle( currentBackgroundContent ).opacity;\n\t\t\t\t\tcurrentBackgroundContent.style.backgroundImage = backgroundImageURL;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tthis.previousBackground = currentBackground;\n\n\t\t}\n\n\t\t// If there's a background brightness flag for this slide,\n\t\t// bubble it to the .reveal container\n\t\tif( currentSlide ) {\n\t\t\tthis.bubbleSlideContrastClassToElement( currentSlide, this.Reveal.getRevealElement() );\n\t\t}\n\n\t\t// Allow the first background to apply without transition\n\t\tsetTimeout( () => {\n\t\t\tthis.element.classList.remove( 'no-transition' );\n\t\t}, 10 );\n\n\t}\n\n\t/**\n\t * Updates the position of the parallax background based\n\t * on the current slide index.\n\t */\n\tupdateParallax() {\n\n\t\tlet indices = this.Reveal.getIndices();\n\n\t\tif( this.Reveal.getConfig().parallaxBackgroundImage ) {\n\n\t\t\tlet horizontalSlides = this.Reveal.getHorizontalSlides(),\n\t\t\t\tverticalSlides = this.Reveal.getVerticalSlides();\n\n\t\t\tlet backgroundSize = this.element.style.backgroundSize.split( ' ' ),\n\t\t\t\tbackgroundWidth, backgroundHeight;\n\n\t\t\tif( backgroundSize.length === 1 ) {\n\t\t\t\tbackgroundWidth = backgroundHeight = parseInt( backgroundSize[0], 10 );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbackgroundWidth = parseInt( backgroundSize[0], 10 );\n\t\t\t\tbackgroundHeight = parseInt( backgroundSize[1], 10 );\n\t\t\t}\n\n\t\t\tlet slideWidth = this.element.offsetWidth,\n\t\t\t\thorizontalSlideCount = horizontalSlides.length,\n\t\t\t\thorizontalOffsetMultiplier,\n\t\t\t\thorizontalOffset;\n\n\t\t\tif( typeof this.Reveal.getConfig().parallaxBackgroundHorizontal === 'number' ) {\n\t\t\t\thorizontalOffsetMultiplier = this.Reveal.getConfig().parallaxBackgroundHorizontal;\n\t\t\t}\n\t\t\telse {\n\t\t\t\thorizontalOffsetMultiplier = horizontalSlideCount > 1 ? ( backgroundWidth - slideWidth ) / ( horizontalSlideCount-1 ) : 0;\n\t\t\t}\n\n\t\t\thorizontalOffset = horizontalOffsetMultiplier * indices.h * -1;\n\n\t\t\tlet slideHeight = this.element.offsetHeight,\n\t\t\t\tverticalSlideCount = verticalSlides.length,\n\t\t\t\tverticalOffsetMultiplier,\n\t\t\t\tverticalOffset;\n\n\t\t\tif( typeof this.Reveal.getConfig().parallaxBackgroundVertical === 'number' ) {\n\t\t\t\tverticalOffsetMultiplier = this.Reveal.getConfig().parallaxBackgroundVertical;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tverticalOffsetMultiplier = ( backgroundHeight - slideHeight ) / ( verticalSlideCount-1 );\n\t\t\t}\n\n\t\t\tverticalOffset = verticalSlideCount > 0 ?  verticalOffsetMultiplier * indices.v : 0;\n\n\t\t\tthis.element.style.backgroundPosition = horizontalOffset + 'px ' + -verticalOffset + 'px';\n\n\t\t}\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.element.remove();\n\n\t}\n\n}\n", "import { queryAll, extend, createStyleSheet, matches, closest } from '../utils/util.js'\nimport { FRAGMENT_STYLE_REGEX } from '../utils/constants.js'\n\n// Counter used to generate unique IDs for auto-animated elements\nlet autoAnimateCounter = 0;\n\n/**\n * Automatically animates matching elements across\n * slides with the [data-auto-animate] attribute.\n */\nexport default class AutoAnimate {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\t/**\n\t * Runs an auto-animation between the given slides.\n\t *\n\t * @param  {HTMLElement} fromSlide\n\t * @param  {HTMLElement} toSlide\n\t */\n\trun( fromSlide, toSlide ) {\n\n\t\t// Clean up after prior animations\n\t\tthis.reset();\n\n\t\tlet allSlides = this.Reveal.getSlides();\n\t\tlet toSlideIndex = allSlides.indexOf( toSlide );\n\t\tlet fromSlideIndex = allSlides.indexOf( fromSlide );\n\n\t\t// Ensure that;\n\t\t// 1. Both slides exist.\n\t\t// 2. Both slides are auto-animate targets with the same\n\t\t//    data-auto-animate-id value (including null if absent on both).\n\t\t// 3. data-auto-animate-restart isn't set on the physically latter\n\t\t//    slide (independent of slide direction).\n\t\tif( fromSlide && toSlide && fromSlide.hasAttribute( 'data-auto-animate' ) && toSlide.hasAttribute( 'data-auto-animate' )\n\t\t\t\t&& fromSlide.getAttribute( 'data-auto-animate-id' ) === toSlide.getAttribute( 'data-auto-animate-id' ) \n\t\t\t\t&& !( toSlideIndex > fromSlideIndex ? toSlide : fromSlide ).hasAttribute( 'data-auto-animate-restart' ) ) {\n\n\t\t\t// Create a new auto-animate sheet\n\t\t\tthis.autoAnimateStyleSheet = this.autoAnimateStyleSheet || createStyleSheet();\n\n\t\t\tlet animationOptions = this.getAutoAnimateOptions( toSlide );\n\n\t\t\t// Set our starting state\n\t\t\tfromSlide.dataset.autoAnimate = 'pending';\n\t\t\ttoSlide.dataset.autoAnimate = 'pending';\n\n\t\t\t// Flag the navigation direction, needed for fragment buildup\n\t\t\tanimationOptions.slideDirection = toSlideIndex > fromSlideIndex ? 'forward' : 'backward';\n\n\t\t\t// If the from-slide is hidden because it has moved outside\n\t\t\t// the view distance, we need to temporarily show it while\n\t\t\t// measuring\n\t\t\tlet fromSlideIsHidden = fromSlide.style.display === 'none';\n\t\t\tif( fromSlideIsHidden ) fromSlide.style.display = this.Reveal.getConfig().display;\n\n\t\t\t// Inject our auto-animate styles for this transition\n\t\t\tlet css = this.getAutoAnimatableElements( fromSlide, toSlide ).map( elements => {\n\t\t\t\treturn this.autoAnimateElements( elements.from, elements.to, elements.options || {}, animationOptions, autoAnimateCounter++ );\n\t\t\t} );\n\n\t\t\tif( fromSlideIsHidden ) fromSlide.style.display = 'none';\n\n\t\t\t// Animate unmatched elements, if enabled\n\t\t\tif( toSlide.dataset.autoAnimateUnmatched !== 'false' && this.Reveal.getConfig().autoAnimateUnmatched === true ) {\n\n\t\t\t\t// Our default timings for unmatched elements\n\t\t\t\tlet defaultUnmatchedDuration = animationOptions.duration * 0.8,\n\t\t\t\t\tdefaultUnmatchedDelay = animationOptions.duration * 0.2;\n\n\t\t\t\tthis.getUnmatchedAutoAnimateElements( toSlide ).forEach( unmatchedElement => {\n\n\t\t\t\t\tlet unmatchedOptions = this.getAutoAnimateOptions( unmatchedElement, animationOptions );\n\t\t\t\t\tlet id = 'unmatched';\n\n\t\t\t\t\t// If there is a duration or delay set specifically for this\n\t\t\t\t\t// element our unmatched elements should adhere to those\n\t\t\t\t\tif( unmatchedOptions.duration !== animationOptions.duration || unmatchedOptions.delay !== animationOptions.delay ) {\n\t\t\t\t\t\tid = 'unmatched-' + autoAnimateCounter++;\n\t\t\t\t\t\tcss.push( `[data-auto-animate=\"running\"] [data-auto-animate-target=\"${id}\"] { transition: opacity ${unmatchedOptions.duration}s ease ${unmatchedOptions.delay}s; }` );\n\t\t\t\t\t}\n\n\t\t\t\t\tunmatchedElement.dataset.autoAnimateTarget = id;\n\n\t\t\t\t}, this );\n\n\t\t\t\t// Our default transition for unmatched elements\n\t\t\t\tcss.push( `[data-auto-animate=\"running\"] [data-auto-animate-target=\"unmatched\"] { transition: opacity ${defaultUnmatchedDuration}s ease ${defaultUnmatchedDelay}s; }` );\n\n\t\t\t}\n\n\t\t\t// Setting the whole chunk of CSS at once is the most\n\t\t\t// efficient way to do this. Using sheet.insertRule\n\t\t\t// is multiple factors slower.\n\t\t\tthis.autoAnimateStyleSheet.innerHTML = css.join( '' );\n\n\t\t\t// Start the animation next cycle\n\t\t\trequestAnimationFrame( () => {\n\t\t\t\tif( this.autoAnimateStyleSheet ) {\n\t\t\t\t\t// This forces our newly injected styles to be applied in Firefox\n\t\t\t\t\tgetComputedStyle( this.autoAnimateStyleSheet ).fontWeight;\n\n\t\t\t\t\ttoSlide.dataset.autoAnimate = 'running';\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\ttype: 'autoanimate',\n\t\t\t\tdata: {\n\t\t\t\t\tfromSlide,\n\t\t\t\t\ttoSlide,\n\t\t\t\t\tsheet: this.autoAnimateStyleSheet\n\t\t\t\t}\n\t\t\t});\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Rolls back all changes that we've made to the DOM so\n\t * that as part of animating.\n\t */\n\treset() {\n\n\t\t// Reset slides\n\t\tqueryAll( this.Reveal.getRevealElement(), '[data-auto-animate]:not([data-auto-animate=\"\"])' ).forEach( element => {\n\t\t\telement.dataset.autoAnimate = '';\n\t\t} );\n\n\t\t// Reset elements\n\t\tqueryAll( this.Reveal.getRevealElement(), '[data-auto-animate-target]' ).forEach( element => {\n\t\t\tdelete element.dataset.autoAnimateTarget;\n\t\t} );\n\n\t\t// Remove the animation sheet\n\t\tif( this.autoAnimateStyleSheet && this.autoAnimateStyleSheet.parentNode ) {\n\t\t\tthis.autoAnimateStyleSheet.parentNode.removeChild( this.autoAnimateStyleSheet );\n\t\t\tthis.autoAnimateStyleSheet = null;\n\t\t}\n\n\t}\n\n\t/**\n\t * Creates a FLIP animation where the `to` element starts out\n\t * in the `from` element position and animates to its original\n\t * state.\n\t *\n\t * @param {HTMLElement} from\n\t * @param {HTMLElement} to\n\t * @param {Object} elementOptions Options for this element pair\n\t * @param {Object} animationOptions Options set at the slide level\n\t * @param {String} id Unique ID that we can use to identify this\n\t * auto-animate element in the DOM\n\t */\n\tautoAnimateElements( from, to, elementOptions, animationOptions, id ) {\n\n\t\t// 'from' elements are given a data-auto-animate-target with no value,\n\t\t// 'to' elements are are given a data-auto-animate-target with an ID\n\t\tfrom.dataset.autoAnimateTarget = '';\n\t\tto.dataset.autoAnimateTarget = id;\n\n\t\t// Each element may override any of the auto-animate options\n\t\t// like transition easing, duration and delay via data-attributes\n\t\tlet options = this.getAutoAnimateOptions( to, animationOptions );\n\n\t\t// If we're using a custom element matcher the element options\n\t\t// may contain additional transition overrides\n\t\tif( typeof elementOptions.delay !== 'undefined' ) options.delay = elementOptions.delay;\n\t\tif( typeof elementOptions.duration !== 'undefined' ) options.duration = elementOptions.duration;\n\t\tif( typeof elementOptions.easing !== 'undefined' ) options.easing = elementOptions.easing;\n\n\t\tlet fromProps = this.getAutoAnimatableProperties( 'from', from, elementOptions ),\n\t\t\ttoProps = this.getAutoAnimatableProperties( 'to', to, elementOptions );\n\n\t\t// Maintain fragment visibility for matching elements when\n\t\t// we're navigating forwards, this way the viewer won't need\n\t\t// to step through the same fragments twice\n\t\tif( to.classList.contains( 'fragment' ) ) {\n\n\t\t\t// Don't auto-animate the opacity of fragments to avoid\n\t\t\t// conflicts with fragment animations\n\t\t\tdelete toProps.styles['opacity'];\n\n\t\t\tif( from.classList.contains( 'fragment' ) ) {\n\n\t\t\t\tlet fromFragmentStyle = ( from.className.match( FRAGMENT_STYLE_REGEX ) || [''] )[0];\n\t\t\t\tlet toFragmentStyle = ( to.className.match( FRAGMENT_STYLE_REGEX ) || [''] )[0];\n\n\t\t\t\t// Only skip the fragment if the fragment animation style\n\t\t\t\t// remains unchanged\n\t\t\t\tif( fromFragmentStyle === toFragmentStyle && animationOptions.slideDirection === 'forward' ) {\n\t\t\t\t\tto.classList.add( 'visible', 'disabled' );\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// If translation and/or scaling are enabled, css transform\n\t\t// the 'to' element so that it matches the position and size\n\t\t// of the 'from' element\n\t\tif( elementOptions.translate !== false || elementOptions.scale !== false ) {\n\n\t\t\tlet presentationScale = this.Reveal.getScale();\n\n\t\t\tlet delta = {\n\t\t\t\tx: ( fromProps.x - toProps.x ) / presentationScale,\n\t\t\t\ty: ( fromProps.y - toProps.y ) / presentationScale,\n\t\t\t\tscaleX: fromProps.width / toProps.width,\n\t\t\t\tscaleY: fromProps.height / toProps.height\n\t\t\t};\n\n\t\t\t// Limit decimal points to avoid 0.0001px blur and stutter\n\t\t\tdelta.x = Math.round( delta.x * 1000 ) / 1000;\n\t\t\tdelta.y = Math.round( delta.y * 1000 ) / 1000;\n\t\t\tdelta.scaleX = Math.round( delta.scaleX * 1000 ) / 1000;\n\t\t\tdelta.scaleX = Math.round( delta.scaleX * 1000 ) / 1000;\n\n\t\t\tlet translate = elementOptions.translate !== false && ( delta.x !== 0 || delta.y !== 0 ),\n\t\t\t\tscale = elementOptions.scale !== false && ( delta.scaleX !== 0 || delta.scaleY !== 0 );\n\n\t\t\t// No need to transform if nothing's changed\n\t\t\tif( translate || scale ) {\n\n\t\t\t\tlet transform = [];\n\n\t\t\t\tif( translate ) transform.push( `translate(${delta.x}px, ${delta.y}px)` );\n\t\t\t\tif( scale ) transform.push( `scale(${delta.scaleX}, ${delta.scaleY})` );\n\n\t\t\t\tfromProps.styles['transform'] = transform.join( ' ' );\n\t\t\t\tfromProps.styles['transform-origin'] = 'top left';\n\n\t\t\t\ttoProps.styles['transform'] = 'none';\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Delete all unchanged 'to' styles\n\t\tfor( let propertyName in toProps.styles ) {\n\t\t\tconst toValue = toProps.styles[propertyName];\n\t\t\tconst fromValue = fromProps.styles[propertyName];\n\n\t\t\tif( toValue === fromValue ) {\n\t\t\t\tdelete toProps.styles[propertyName];\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// If these property values were set via a custom matcher providing\n\t\t\t\t// an explicit 'from' and/or 'to' value, we always inject those values.\n\t\t\t\tif( toValue.explicitValue === true ) {\n\t\t\t\t\ttoProps.styles[propertyName] = toValue.value;\n\t\t\t\t}\n\n\t\t\t\tif( fromValue.explicitValue === true ) {\n\t\t\t\t\tfromProps.styles[propertyName] = fromValue.value;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tlet css = '';\n\n\t\tlet toStyleProperties = Object.keys( toProps.styles );\n\n\t\t// Only create animate this element IF at least one style\n\t\t// property has changed\n\t\tif( toStyleProperties.length > 0 ) {\n\n\t\t\t// Instantly move to the 'from' state\n\t\t\tfromProps.styles['transition'] = 'none';\n\n\t\t\t// Animate towards the 'to' state\n\t\t\ttoProps.styles['transition'] = `all ${options.duration}s ${options.easing} ${options.delay}s`;\n\t\t\ttoProps.styles['transition-property'] = toStyleProperties.join( ', ' );\n\t\t\ttoProps.styles['will-change'] = toStyleProperties.join( ', ' );\n\n\t\t\t// Build up our custom CSS. We need to override inline styles\n\t\t\t// so we need to make our styles vErY IMPORTANT!1!!\n\t\t\tlet fromCSS = Object.keys( fromProps.styles ).map( propertyName => {\n\t\t\t\treturn propertyName + ': ' + fromProps.styles[propertyName] + ' !important;';\n\t\t\t} ).join( '' );\n\n\t\t\tlet toCSS = Object.keys( toProps.styles ).map( propertyName => {\n\t\t\t\treturn propertyName + ': ' + toProps.styles[propertyName] + ' !important;';\n\t\t\t} ).join( '' );\n\n\t\t\tcss = \t'[data-auto-animate-target=\"'+ id +'\"] {'+ fromCSS +'}' +\n\t\t\t\t\t'[data-auto-animate=\"running\"] [data-auto-animate-target=\"'+ id +'\"] {'+ toCSS +'}';\n\n\t\t}\n\n\t\treturn css;\n\n\t}\n\n\t/**\n\t * Returns the auto-animate options for the given element.\n\t *\n\t * @param {HTMLElement} element Element to pick up options\n\t * from, either a slide or an animation target\n\t * @param {Object} [inheritedOptions] Optional set of existing\n\t * options\n\t */\n\tgetAutoAnimateOptions( element, inheritedOptions ) {\n\n\t\tlet options = {\n\t\t\teasing: this.Reveal.getConfig().autoAnimateEasing,\n\t\t\tduration: this.Reveal.getConfig().autoAnimateDuration,\n\t\t\tdelay: 0\n\t\t};\n\n\t\toptions = extend( options, inheritedOptions );\n\n\t\t// Inherit options from parent elements\n\t\tif( element.parentNode ) {\n\t\t\tlet autoAnimatedParent = closest( element.parentNode, '[data-auto-animate-target]' );\n\t\t\tif( autoAnimatedParent ) {\n\t\t\t\toptions = this.getAutoAnimateOptions( autoAnimatedParent, options );\n\t\t\t}\n\t\t}\n\n\t\tif( element.dataset.autoAnimateEasing ) {\n\t\t\toptions.easing = element.dataset.autoAnimateEasing;\n\t\t}\n\n\t\tif( element.dataset.autoAnimateDuration ) {\n\t\t\toptions.duration = parseFloat( element.dataset.autoAnimateDuration );\n\t\t}\n\n\t\tif( element.dataset.autoAnimateDelay ) {\n\t\t\toptions.delay = parseFloat( element.dataset.autoAnimateDelay );\n\t\t}\n\n\t\treturn options;\n\n\t}\n\n\t/**\n\t * Returns an object containing all of the properties\n\t * that can be auto-animated for the given element and\n\t * their current computed values.\n\t *\n\t * @param {String} direction 'from' or 'to'\n\t */\n\tgetAutoAnimatableProperties( direction, element, elementOptions ) {\n\n\t\tlet config = this.Reveal.getConfig();\n\n\t\tlet properties = { styles: [] };\n\n\t\t// Position and size\n\t\tif( elementOptions.translate !== false || elementOptions.scale !== false ) {\n\t\t\tlet bounds;\n\n\t\t\t// Custom auto-animate may optionally return a custom tailored\n\t\t\t// measurement function\n\t\t\tif( typeof elementOptions.measure === 'function' ) {\n\t\t\t\tbounds = elementOptions.measure( element );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif( config.center ) {\n\t\t\t\t\t// More precise, but breaks when used in combination\n\t\t\t\t\t// with zoom for scaling the deck ¯\\_(ツ)_/¯\n\t\t\t\t\tbounds = element.getBoundingClientRect();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tlet scale = this.Reveal.getScale();\n\t\t\t\t\tbounds = {\n\t\t\t\t\t\tx: element.offsetLeft * scale,\n\t\t\t\t\t\ty: element.offsetTop * scale,\n\t\t\t\t\t\twidth: element.offsetWidth * scale,\n\t\t\t\t\t\theight: element.offsetHeight * scale\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tproperties.x = bounds.x;\n\t\t\tproperties.y = bounds.y;\n\t\t\tproperties.width = bounds.width;\n\t\t\tproperties.height = bounds.height;\n\t\t}\n\n\t\tconst computedStyles = getComputedStyle( element );\n\n\t\t// CSS styles\n\t\t( elementOptions.styles || config.autoAnimateStyles ).forEach( style => {\n\t\t\tlet value;\n\n\t\t\t// `style` is either the property name directly, or an object\n\t\t\t// definition of a style property\n\t\t\tif( typeof style === 'string' ) style = { property: style };\n\n\t\t\tif( typeof style.from !== 'undefined' && direction === 'from' ) {\n\t\t\t\tvalue = { value: style.from, explicitValue: true };\n\t\t\t}\n\t\t\telse if( typeof style.to !== 'undefined' && direction === 'to' ) {\n\t\t\t\tvalue = { value: style.to, explicitValue: true };\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Use a unitless value for line-height so that it inherits properly\n\t\t\t\tif( style.property === 'line-height' ) {\n\t\t\t\t\tvalue = parseFloat( computedStyles['line-height'] ) / parseFloat( computedStyles['font-size'] );\n\t\t\t\t}\n\n\t\t\t\tif( isNaN(value) ) {\n\t\t\t\t\tvalue = computedStyles[style.property];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif( value !== '' ) {\n\t\t\t\tproperties.styles[style.property] = value;\n\t\t\t}\n\t\t} );\n\n\t\treturn properties;\n\n\t}\n\n\t/**\n\t * Get a list of all element pairs that we can animate\n\t * between the given slides.\n\t *\n\t * @param {HTMLElement} fromSlide\n\t * @param {HTMLElement} toSlide\n\t *\n\t * @return {Array} Each value is an array where [0] is\n\t * the element we're animating from and [1] is the\n\t * element we're animating to\n\t */\n\tgetAutoAnimatableElements( fromSlide, toSlide ) {\n\n\t\tlet matcher = typeof this.Reveal.getConfig().autoAnimateMatcher === 'function' ? this.Reveal.getConfig().autoAnimateMatcher : this.getAutoAnimatePairs;\n\n\t\tlet pairs = matcher.call( this, fromSlide, toSlide );\n\n\t\tlet reserved = [];\n\n\t\t// Remove duplicate pairs\n\t\treturn pairs.filter( ( pair, index ) => {\n\t\t\tif( reserved.indexOf( pair.to ) === -1 ) {\n\t\t\t\treserved.push( pair.to );\n\t\t\t\treturn true;\n\t\t\t}\n\t\t} );\n\n\t}\n\n\t/**\n\t * Identifies matching elements between slides.\n\t *\n\t * You can specify a custom matcher function by using\n\t * the `autoAnimateMatcher` config option.\n\t */\n\tgetAutoAnimatePairs( fromSlide, toSlide ) {\n\n\t\tlet pairs = [];\n\n\t\tconst codeNodes = 'pre';\n\t\tconst textNodes = 'h1, h2, h3, h4, h5, h6, p, li';\n\t\tconst mediaNodes = 'img, video, iframe';\n\n\t\t// Explicit matches via data-id\n\t\tthis.findAutoAnimateMatches( pairs, fromSlide, toSlide, '[data-id]', node => {\n\t\t\treturn node.nodeName + ':::' + node.getAttribute( 'data-id' );\n\t\t} );\n\n\t\t// Text\n\t\tthis.findAutoAnimateMatches( pairs, fromSlide, toSlide, textNodes, node => {\n\t\t\treturn node.nodeName + ':::' + node.innerText;\n\t\t} );\n\n\t\t// Media\n\t\tthis.findAutoAnimateMatches( pairs, fromSlide, toSlide, mediaNodes, node => {\n\t\t\treturn node.nodeName + ':::' + ( node.getAttribute( 'src' ) || node.getAttribute( 'data-src' ) );\n\t\t} );\n\n\t\t// Code\n\t\tthis.findAutoAnimateMatches( pairs, fromSlide, toSlide, codeNodes, node => {\n\t\t\treturn node.nodeName + ':::' + node.innerText;\n\t\t} );\n\n\t\tpairs.forEach( pair => {\n\t\t\t// Disable scale transformations on text nodes, we transition\n\t\t\t// each individual text property instead\n\t\t\tif( matches( pair.from, textNodes ) ) {\n\t\t\t\tpair.options = { scale: false };\n\t\t\t}\n\t\t\t// Animate individual lines of code\n\t\t\telse if( matches( pair.from, codeNodes ) ) {\n\n\t\t\t\t// Transition the code block's width and height instead of scaling\n\t\t\t\t// to prevent its content from being squished\n\t\t\t\tpair.options = { scale: false, styles: [ 'width', 'height' ] };\n\n\t\t\t\t// Lines of code\n\t\t\t\tthis.findAutoAnimateMatches( pairs, pair.from, pair.to, '.hljs .hljs-ln-code', node => {\n\t\t\t\t\treturn node.textContent;\n\t\t\t\t}, {\n\t\t\t\t\tscale: false,\n\t\t\t\t\tstyles: [],\n\t\t\t\t\tmeasure: this.getLocalBoundingBox.bind( this )\n\t\t\t\t} );\n\n\t\t\t\t// Line numbers\n\t\t\t\tthis.findAutoAnimateMatches( pairs, pair.from, pair.to, '.hljs .hljs-ln-numbers[data-line-number]', node => {\n\t\t\t\t\treturn node.getAttribute( 'data-line-number' );\n\t\t\t\t}, {\n\t\t\t\t\tscale: false,\n\t\t\t\t\tstyles: [ 'width' ],\n\t\t\t\t\tmeasure: this.getLocalBoundingBox.bind( this )\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t}, this );\n\n\t\treturn pairs;\n\n\t}\n\n\t/**\n\t * Helper method which returns a bounding box based on\n\t * the given elements offset coordinates.\n\t *\n\t * @param {HTMLElement} element\n\t * @return {Object} x, y, width, height\n\t */\n\tgetLocalBoundingBox( element ) {\n\n\t\tconst presentationScale = this.Reveal.getScale();\n\n\t\treturn {\n\t\t\tx: Math.round( ( element.offsetLeft * presentationScale ) * 100 ) / 100,\n\t\t\ty: Math.round( ( element.offsetTop * presentationScale ) * 100 ) / 100,\n\t\t\twidth: Math.round( ( element.offsetWidth * presentationScale ) * 100 ) / 100,\n\t\t\theight: Math.round( ( element.offsetHeight * presentationScale ) * 100 ) / 100\n\t\t};\n\n\t}\n\n\t/**\n\t * Finds matching elements between two slides.\n\t *\n\t * @param {Array} pairs            \tList of pairs to push matches to\n\t * @param {HTMLElement} fromScope   Scope within the from element exists\n\t * @param {HTMLElement} toScope     Scope within the to element exists\n\t * @param {String} selector         CSS selector of the element to match\n\t * @param {Function} serializer     A function that accepts an element and returns\n\t *                                  a stringified ID based on its contents\n\t * @param {Object} animationOptions Optional config options for this pair\n\t */\n\tfindAutoAnimateMatches( pairs, fromScope, toScope, selector, serializer, animationOptions ) {\n\n\t\tlet fromMatches = {};\n\t\tlet toMatches = {};\n\n\t\t[].slice.call( fromScope.querySelectorAll( selector ) ).forEach( ( element, i ) => {\n\t\t\tconst key = serializer( element );\n\t\t\tif( typeof key === 'string' && key.length ) {\n\t\t\t\tfromMatches[key] = fromMatches[key] || [];\n\t\t\t\tfromMatches[key].push( element );\n\t\t\t}\n\t\t} );\n\n\t\t[].slice.call( toScope.querySelectorAll( selector ) ).forEach( ( element, i ) => {\n\t\t\tconst key = serializer( element );\n\t\t\ttoMatches[key] = toMatches[key] || [];\n\t\t\ttoMatches[key].push( element );\n\n\t\t\tlet fromElement;\n\n\t\t\t// Retrieve the 'from' element\n\t\t\tif( fromMatches[key] ) {\n\t\t\t\tconst primaryIndex = toMatches[key].length - 1;\n\t\t\t\tconst secondaryIndex = fromMatches[key].length - 1;\n\n\t\t\t\t// If there are multiple identical from elements, retrieve\n\t\t\t\t// the one at the same index as our to-element.\n\t\t\t\tif( fromMatches[key][ primaryIndex ] ) {\n\t\t\t\t\tfromElement = fromMatches[key][ primaryIndex ];\n\t\t\t\t\tfromMatches[key][ primaryIndex ] = null;\n\t\t\t\t}\n\t\t\t\t// If there are no matching from-elements at the same index,\n\t\t\t\t// use the last one.\n\t\t\t\telse if( fromMatches[key][ secondaryIndex ] ) {\n\t\t\t\t\tfromElement = fromMatches[key][ secondaryIndex ];\n\t\t\t\t\tfromMatches[key][ secondaryIndex ] = null;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we've got a matching pair, push it to the list of pairs\n\t\t\tif( fromElement ) {\n\t\t\t\tpairs.push({\n\t\t\t\t\tfrom: fromElement,\n\t\t\t\t\tto: element,\n\t\t\t\t\toptions: animationOptions\n\t\t\t\t});\n\t\t\t}\n\t\t} );\n\n\t}\n\n\t/**\n\t * Returns a all elements within the given scope that should\n\t * be considered unmatched in an auto-animate transition. If\n\t * fading of unmatched elements is turned on, these elements\n\t * will fade when going between auto-animate slides.\n\t *\n\t * Note that parents of auto-animate targets are NOT considered\n\t * unmatched since fading them would break the auto-animation.\n\t *\n\t * @param {HTMLElement} rootElement\n\t * @return {Array}\n\t */\n\tgetUnmatchedAutoAnimateElements( rootElement ) {\n\n\t\treturn [].slice.call( rootElement.children ).reduce( ( result, element ) => {\n\n\t\t\tconst containsAnimatedElements = element.querySelector( '[data-auto-animate-target]' );\n\n\t\t\t// The element is unmatched if\n\t\t\t// - It is not an auto-animate target\n\t\t\t// - It does not contain any auto-animate targets\n\t\t\tif( !element.hasAttribute( 'data-auto-animate-target' ) && !containsAnimatedElements ) {\n\t\t\t\tresult.push( element );\n\t\t\t}\n\n\t\t\tif( element.querySelector( '[data-auto-animate-target]' ) ) {\n\t\t\t\tresult = result.concat( this.getUnmatchedAutoAnimateElements( element ) );\n\t\t\t}\n\n\t\t\treturn result;\n\n\t\t}, [] );\n\n\t}\n\n}\n", "import { HOR<PERSON><PERSON><PERSON><PERSON><PERSON>_SLIDES_SELECTOR, HOR<PERSON><PERSON><PERSON><PERSON>L_BACKGROUNDS_SELECTOR } from '../utils/constants.js'\nimport { queryAll } from '../utils/util.js'\n\nconst HIDE_SCROLLBAR_TIMEOUT = 500;\nconst MAX_PROGRESS_SPACING = 4;\nconst MIN_PROGRESS_SEGMENT_HEIGHT = 6;\nconst MIN_PLAYHEAD_HEIGHT = 8;\n\n/**\n * The scroll view lets you read a reveal.js presentation\n * as a linear scrollable page.\n */\nexport default class ScrollView {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.active = false;\n\t\tthis.activatedCallbacks = [];\n\n\t\tthis.onScroll = this.onScroll.bind( this );\n\n\t}\n\n\t/**\n\t * Activates the scroll view. This rearranges the presentation DOM\n\t * by—among other things—wrapping each slide in a page element.\n\t */\n\tactivate() {\n\n\t\tif( this.active ) return;\n\n\t\tconst stateBeforeActivation = this.Reveal.getState();\n\n\t\tthis.active = true;\n\n\t\t// Store the full presentation HTML so that we can restore it\n\t\t// when/if the scroll view is deactivated\n\t\tthis.slideHTMLBeforeActivation = this.Reveal.getSlidesElement().innerHTML;\n\n\t\tconst horizontalSlides = queryAll( this.Reveal.getRevealElement(), HORIZONTAL_SLIDES_SELECTOR );\n\t\tconst horizontalBackgrounds = queryAll( this.Reveal.getRevealElement(), HORIZONTAL_BACKGROUNDS_SELECTOR );\n\n\t\tthis.viewportElement.classList.add( 'loading-scroll-mode', 'reveal-scroll' );\n\n\t\tlet presentationBackground;\n\n\t\tconst viewportStyles = window.getComputedStyle( this.viewportElement );\n\t\tif( viewportStyles && viewportStyles.background ) {\n\t\t\tpresentationBackground = viewportStyles.background;\n\t\t}\n\n\t\tconst pageElements = [];\n\t\tconst pageContainer = horizontalSlides[0].parentNode;\n\n\t\tlet previousSlide;\n\n\t\t// Creates a new page element and appends the given slide/bg\n\t\t// to it.\n\t\tconst createPageElement = ( slide, h, v, isVertical ) => {\n\n\t\t\tlet contentContainer;\n\n\t\t\t// If this slide is part of an auto-animation sequence, we\n\t\t\t// group it under the same page element as the previous slide\n\t\t\tif( previousSlide && this.Reveal.shouldAutoAnimateBetween( previousSlide, slide ) ) {\n\t\t\t\tcontentContainer = document.createElement( 'div' );\n\t\t\t\tcontentContainer.className = 'scroll-page-content scroll-auto-animate-page';\n\t\t\t\tcontentContainer.style.display = 'none';\n\t\t\t\tpreviousSlide.closest( '.scroll-page-content' ).parentNode.appendChild( contentContainer );\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Wrap the slide in a page element and hide its overflow\n\t\t\t\t// so that no page ever flows onto another\n\t\t\t\tconst page = document.createElement( 'div' );\n\t\t\t\tpage.className = 'scroll-page';\n\t\t\t\tpageElements.push( page );\n\n\t\t\t\t// This transfers over the background of the vertical stack containing\n\t\t\t\t// the slide if it exists. Otherwise, it uses the presentation-wide\n\t\t\t\t// background.\n\t\t\t\tif( isVertical && horizontalBackgrounds.length > h ) {\n\t\t\t\t\tconst slideBackground = horizontalBackgrounds[h];\n\t\t\t\t\tconst pageBackground = window.getComputedStyle( slideBackground );\n\n\t\t\t\t\tif( pageBackground && pageBackground.background ) {\n\t\t\t\t\t\tpage.style.background = pageBackground.background;\n\t\t\t\t\t}\n\t\t\t\t\telse if( presentationBackground ) {\n\t\t\t\t\t\tpage.style.background = presentationBackground;\n\t\t\t\t\t}\n\t\t\t\t} else if( presentationBackground ) {\n\t\t\t\t\tpage.style.background = presentationBackground;\n\t\t\t\t}\n\n\t\t\t\tconst stickyContainer = document.createElement( 'div' );\n\t\t\t\tstickyContainer.className = 'scroll-page-sticky';\n\t\t\t\tpage.appendChild( stickyContainer );\n\n\t\t\t\tcontentContainer = document.createElement( 'div' );\n\t\t\t\tcontentContainer.className = 'scroll-page-content';\n\t\t\t\tstickyContainer.appendChild( contentContainer );\n\t\t\t}\n\n\t\t\tcontentContainer.appendChild( slide );\n\n\t\t\tslide.classList.remove( 'past', 'future' );\n\t\t\tslide.setAttribute( 'data-index-h', h );\n\t\t\tslide.setAttribute( 'data-index-v', v );\n\n\t\t\tif( slide.slideBackgroundElement ) {\n\t\t\t\tslide.slideBackgroundElement.remove( 'past', 'future' );\n\t\t\t\tcontentContainer.insertBefore( slide.slideBackgroundElement, slide );\n\t\t\t}\n\n\t\t\tpreviousSlide = slide;\n\n\t\t}\n\n\t\t// Slide and slide background layout\n\t\thorizontalSlides.forEach( ( horizontalSlide, h ) => {\n\n\t\t\tif( this.Reveal.isVerticalStack( horizontalSlide ) ) {\n\t\t\t\thorizontalSlide.querySelectorAll( 'section' ).forEach( ( verticalSlide, v ) => {\n\t\t\t\t\tcreatePageElement( verticalSlide, h, v, true );\n\t\t\t\t});\n\t\t\t}\n\t\t\telse {\n\t\t\t\tcreatePageElement( horizontalSlide, h, 0 );\n\t\t\t}\n\n\t\t}, this );\n\n\t\tthis.createProgressBar();\n\n\t\t// Remove leftover stacks\n\t\tqueryAll( this.Reveal.getRevealElement(), '.stack' ).forEach( stack => stack.remove() );\n\n\t\t// Add our newly created pages to the DOM\n\t\tpageElements.forEach( page => pageContainer.appendChild( page ) );\n\n\t\t// Re-run JS-based content layout after the slide is added to page DOM\n\t\tthis.Reveal.slideContent.layout( this.Reveal.getSlidesElement() );\n\n\t\tthis.Reveal.layout();\n\t\tthis.Reveal.setState( stateBeforeActivation );\n\n\t\tthis.activatedCallbacks.forEach( callback => callback() );\n\t\tthis.activatedCallbacks = [];\n\n\t\tthis.restoreScrollPosition();\n\n\t\tthis.viewportElement.classList.remove( 'loading-scroll-mode' );\n\t\tthis.viewportElement.addEventListener( 'scroll', this.onScroll, { passive: true } );\n\n\t}\n\n\t/**\n\t * Deactivates the scroll view and restores the standard slide-based\n\t * presentation.\n\t */\n\tdeactivate() {\n\n\t\tif( !this.active ) return;\n\n\t\tconst stateBeforeDeactivation = this.Reveal.getState();\n\n\t\tthis.active = false;\n\n\t\tthis.viewportElement.removeEventListener( 'scroll', this.onScroll );\n\t\tthis.viewportElement.classList.remove( 'reveal-scroll' );\n\n\t\tthis.removeProgressBar();\n\n\t\tthis.Reveal.getSlidesElement().innerHTML = this.slideHTMLBeforeActivation;\n\t\tthis.Reveal.sync();\n\t\tthis.Reveal.setState( stateBeforeDeactivation );\n\n\t\tthis.slideHTMLBeforeActivation = null;\n\n\t}\n\n\ttoggle( override ) {\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? this.activate() : this.deactivate();\n\t\t}\n\t\telse {\n\t\t\tthis.isActive() ? this.deactivate() : this.activate();\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if the scroll view is currently active.\n\t */\n\tisActive() {\n\n\t\treturn this.active;\n\n\t}\n\n\t/**\n\t * Renders the progress bar component.\n\t */\n\tcreateProgressBar() {\n\n\t\tthis.progressBar = document.createElement( 'div' );\n\t\tthis.progressBar.className = 'scrollbar';\n\n\t\tthis.progressBarInner = document.createElement( 'div' );\n\t\tthis.progressBarInner.className = 'scrollbar-inner';\n\t\tthis.progressBar.appendChild( this.progressBarInner );\n\n\t\tthis.progressBarPlayhead = document.createElement( 'div' );\n\t\tthis.progressBarPlayhead.className = 'scrollbar-playhead';\n\t\tthis.progressBarInner.appendChild( this.progressBarPlayhead );\n\n\t\tthis.viewportElement.insertBefore( this.progressBar, this.viewportElement.firstChild );\n\n\t\tconst handleDocumentMouseMove\t= ( event ) => {\n\n\t\t\tlet progress = ( event.clientY - this.progressBarInner.getBoundingClientRect().top ) / this.progressBarHeight;\n\t\t\tprogress = Math.max( Math.min( progress, 1 ), 0 );\n\n\t\t\tthis.viewportElement.scrollTop = progress * ( this.viewportElement.scrollHeight - this.viewportElement.offsetHeight );\n\n\t\t};\n\n\t\tconst handleDocumentMouseUp = ( event ) => {\n\n\t\t\tthis.draggingProgressBar = false;\n\t\t\tthis.showProgressBar();\n\n\t\t\tdocument.removeEventListener( 'mousemove', handleDocumentMouseMove );\n\t\t\tdocument.removeEventListener( 'mouseup', handleDocumentMouseUp );\n\n\t\t};\n\n\t\tconst handleMouseDown = ( event ) => {\n\n\t\t\tevent.preventDefault();\n\n\t\t\tthis.draggingProgressBar = true;\n\n\t\t\tdocument.addEventListener( 'mousemove', handleDocumentMouseMove );\n\t\t\tdocument.addEventListener( 'mouseup', handleDocumentMouseUp );\n\n\t\t\thandleDocumentMouseMove( event );\n\n\t\t};\n\n\t\tthis.progressBarInner.addEventListener( 'mousedown', handleMouseDown );\n\n\t}\n\n\tremoveProgressBar() {\n\n\t\tif( this.progressBar ) {\n\t\t\tthis.progressBar.remove();\n\t\t\tthis.progressBar = null;\n\t\t}\n\n\t}\n\n\tlayout() {\n\n\t\tif( this.isActive() ) {\n\t\t\tthis.syncPages();\n\t\t\tthis.syncScrollPosition();\n\t\t}\n\n\t}\n\n\t/**\n\t * Updates our pages to match the latest configuration and\n\t * presentation size.\n\t */\n\tsyncPages() {\n\n\t\tconst config = this.Reveal.getConfig();\n\n\t\tconst slideSize = this.Reveal.getComputedSlideSize( window.innerWidth, window.innerHeight );\n\t\tconst scale = this.Reveal.getScale();\n\t\tconst useCompactLayout = config.scrollLayout === 'compact';\n\n\t\tconst viewportHeight = this.viewportElement.offsetHeight;\n\t\tconst compactHeight = slideSize.height * scale;\n\t\tconst pageHeight = useCompactLayout ? compactHeight : viewportHeight;\n\n\t\t// The height that needs to be scrolled between scroll triggers\n\t\tthis.scrollTriggerHeight = useCompactLayout ? compactHeight : viewportHeight;\n\n\t\tthis.viewportElement.style.setProperty( '--page-height', pageHeight + 'px' );\n\t\tthis.viewportElement.style.scrollSnapType = typeof config.scrollSnap === 'string' ? `y ${config.scrollSnap}` : '';\n\n\t\t// This will hold all scroll triggers used to show/hide slides\n\t\tthis.slideTriggers = [];\n\n\t\tconst pageElements = Array.from( this.Reveal.getRevealElement().querySelectorAll( '.scroll-page' ) );\n\n\t\tthis.pages = pageElements.map( pageElement => {\n\t\t\tconst page = this.createPage({\n\t\t\t\tpageElement,\n\t\t\t\tslideElement: pageElement.querySelector( 'section' ),\n\t\t\t\tstickyElement: pageElement.querySelector( '.scroll-page-sticky' ),\n\t\t\t\tcontentElement: pageElement.querySelector( '.scroll-page-content' ),\n\t\t\t\tbackgroundElement: pageElement.querySelector( '.slide-background' ),\n\t\t\t\tautoAnimateElements: pageElement.querySelectorAll( '.scroll-auto-animate-page' ),\n\t\t\t\tautoAnimatePages: []\n\t\t\t});\n\n\t\t\tpage.pageElement.style.setProperty( '--slide-height', config.center === true ? 'auto' : slideSize.height + 'px' );\n\n\t\t\tthis.slideTriggers.push({\n\t\t\t\tpage: page,\n\t\t\t\tactivate: () => this.activatePage( page ),\n\t\t\t\tdeactivate: () => this.deactivatePage( page )\n\t\t\t});\n\n\t\t\t// Create scroll triggers that show/hide fragments\n\t\t\tthis.createFragmentTriggersForPage( page );\n\n\t\t\t// Create scroll triggers for triggering auto-animate steps\n\t\t\tif( page.autoAnimateElements.length > 0 ) {\n\t\t\t\tthis.createAutoAnimateTriggersForPage( page );\n\t\t\t}\n\n\t\t\tlet totalScrollTriggerCount = Math.max( page.scrollTriggers.length - 1, 0 );\n\n\t\t\t// Each auto-animate step may include its own scroll triggers\n\t\t\t// for fragments, ensure we count those as well\n\t\t\ttotalScrollTriggerCount += page.autoAnimatePages.reduce( ( total, page ) => {\n\t\t\t\treturn total + Math.max( page.scrollTriggers.length - 1, 0 );\n\t\t\t}, page.autoAnimatePages.length );\n\n\t\t\t// Clean up from previous renders\n\t\t\tpage.pageElement.querySelectorAll( '.scroll-snap-point' ).forEach( el => el.remove() );\n\n\t\t\t// Create snap points for all scroll triggers\n\t\t\t// - Can't be absolute in FF\n\t\t\t// - Can't be 0-height in Safari\n\t\t\t// - Can't use snap-align on parent in Safari because then\n\t\t\t//   inner triggers won't work\n\t\t\tfor( let i = 0; i < totalScrollTriggerCount + 1; i++ ) {\n\t\t\t\tconst triggerStick = document.createElement( 'div' );\n\t\t\t\ttriggerStick.className = 'scroll-snap-point';\n\t\t\t\ttriggerStick.style.height = this.scrollTriggerHeight + 'px';\n\t\t\t\ttriggerStick.style.scrollSnapAlign = useCompactLayout ? 'center' : 'start';\n\t\t\t\tpage.pageElement.appendChild( triggerStick );\n\n\t\t\t\tif( i === 0 ) {\n\t\t\t\t\ttriggerStick.style.marginTop = -this.scrollTriggerHeight + 'px';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// In the compact layout, only slides with scroll triggers cover the\n\t\t\t// full viewport height. This helps avoid empty gaps before or after\n\t\t\t// a sticky slide.\n\t\t\tif( useCompactLayout && page.scrollTriggers.length > 0 ) {\n\t\t\t\tpage.pageHeight = viewportHeight;\n\t\t\t\tpage.pageElement.style.setProperty( '--page-height', viewportHeight + 'px' );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tpage.pageHeight = pageHeight;\n\t\t\t\tpage.pageElement.style.removeProperty( '--page-height' );\n\t\t\t}\n\n\t\t\t// Add scroll padding based on how many scroll triggers we have\n\t\t\tpage.scrollPadding = this.scrollTriggerHeight * totalScrollTriggerCount;\n\n\t\t\t// The total height including scrollable space\n\t\t\tpage.totalHeight = page.pageHeight + page.scrollPadding;\n\n\t\t\t// This is used to pad the height of our page in CSS\n\t\t\tpage.pageElement.style.setProperty( '--page-scroll-padding', page.scrollPadding + 'px' );\n\n\t\t\t// If this is a sticky page, stick it to the vertical center\n\t\t\tif( totalScrollTriggerCount > 0 ) {\n\t\t\t\tpage.stickyElement.style.position = 'sticky';\n\t\t\t\tpage.stickyElement.style.top = Math.max( ( viewportHeight - page.pageHeight ) / 2, 0 ) + 'px';\n\t\t\t}\n\t\t\telse {\n\t\t\t\tpage.stickyElement.style.position = 'relative';\n\t\t\t\tpage.pageElement.style.scrollSnapAlign = page.pageHeight < viewportHeight ? 'center' : 'start';\n\t\t\t}\n\n\t\t\treturn page;\n\t\t} );\n\n\t\tthis.setTriggerRanges();\n\n\t\t/*\n\t\tconsole.log(this.slideTriggers.map( t => {\n\t\t\treturn {\n\t\t\t\trange: `${t.range[0].toFixed(2)}-${t.range[1].toFixed(2)}`,\n\t\t\t\ttriggers: t.page.scrollTriggers.map( t => {\n\t\t\t\t\treturn `${t.range[0].toFixed(2)}-${t.range[1].toFixed(2)}`\n\t\t\t\t}).join( ', ' ),\n\t\t\t}\n\t\t}))\n\t\t*/\n\n\t\tthis.viewportElement.setAttribute( 'data-scrollbar', config.scrollProgress );\n\n\t\tif( config.scrollProgress && this.totalScrollTriggerCount > 1 ) {\n\t\t\t// Create the progress bar if it doesn't already exist\n\t\t\tif( !this.progressBar ) this.createProgressBar();\n\n\t\t\tthis.syncProgressBar();\n\t\t}\n\t\telse {\n\t\t\tthis.removeProgressBar();\n\t\t}\n\n\t}\n\n\t/**\n\t * Calculates and sets the scroll range for all of our scroll\n\t * triggers.\n\t */\n\tsetTriggerRanges() {\n\n\t\t// Calculate the total number of scroll triggers\n\t\tthis.totalScrollTriggerCount = this.slideTriggers.reduce( ( total, trigger ) => {\n\t\t\treturn total + Math.max( trigger.page.scrollTriggers.length, 1 );\n\t\t}, 0 );\n\n\t\tlet rangeStart = 0;\n\n\t\t// Calculate the scroll range of each scroll trigger on a scale\n\t\t// of 0-1\n\t\tthis.slideTriggers.forEach( ( trigger, i ) => {\n\t\t\ttrigger.range = [\n\t\t\t\trangeStart,\n\t\t\t\trangeStart + Math.max( trigger.page.scrollTriggers.length, 1 ) / this.totalScrollTriggerCount\n\t\t\t];\n\n\t\t\tconst scrollTriggerSegmentSize = ( trigger.range[1] - trigger.range[0] ) / trigger.page.scrollTriggers.length;\n\t\t\t// Set the range for each inner scroll trigger\n\t\t\ttrigger.page.scrollTriggers.forEach( ( scrollTrigger, i ) => {\n\t\t\t\tscrollTrigger.range = [\n\t\t\t\t\trangeStart + i * scrollTriggerSegmentSize,\n\t\t\t\t\trangeStart + ( i + 1 ) * scrollTriggerSegmentSize\n\t\t\t\t];\n\t\t\t} );\n\n\t\t\trangeStart = trigger.range[1];\n\t\t} );\n\n\t}\n\n\t/**\n\t * Creates one scroll trigger for each fragments in the given page.\n\t *\n\t * @param {*} page\n\t */\n\tcreateFragmentTriggersForPage( page, slideElement ) {\n\n\t\tslideElement = slideElement || page.slideElement;\n\n\t\t// Each fragment 'group' is an array containing one or more\n\t\t// fragments. Multiple fragments that appear at the same time\n\t\t// are part of the same group.\n\t\tconst fragmentGroups = this.Reveal.fragments.sort( slideElement.querySelectorAll( '.fragment' ), true );\n\n\t\t// Create scroll triggers that show/hide fragments\n\t\tif( fragmentGroups.length ) {\n\t\t\tpage.fragments = this.Reveal.fragments.sort( slideElement.querySelectorAll( '.fragment:not(.disabled)' ) );\n\t\t\tpage.scrollTriggers.push(\n\t\t\t\t// Trigger for the initial state with no fragments visible\n\t\t\t\t{\n\t\t\t\t\tactivate: () => {\n\t\t\t\t\t\tthis.Reveal.fragments.update( -1, page.fragments, slideElement );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\n\t\t\t// Triggers for each fragment group\n\t\t\tfragmentGroups.forEach( ( fragments, i ) => {\n\t\t\t\tpage.scrollTriggers.push({\n\t\t\t\t\tactivate: () => {\n\t\t\t\t\t\tthis.Reveal.fragments.update( i, page.fragments, slideElement );\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} );\n\t\t}\n\n\n\t\treturn page.scrollTriggers.length;\n\n\t}\n\n\t/**\n\t * Creates scroll triggers for the auto-animate steps in the\n\t * given page.\n\t *\n\t * @param {*} page\n\t */\n\tcreateAutoAnimateTriggersForPage( page ) {\n\n\t\tif( page.autoAnimateElements.length > 0 ) {\n\n\t\t\t// Triggers for each subsequent auto-animate slide\n\t\t\tthis.slideTriggers.push( ...Array.from( page.autoAnimateElements ).map( ( autoAnimateElement, i ) => {\n\t\t\t\tlet autoAnimatePage = this.createPage({\n\t\t\t\t\tslideElement: autoAnimateElement.querySelector( 'section' ),\n\t\t\t\t\tcontentElement: autoAnimateElement,\n\t\t\t\t\tbackgroundElement: autoAnimateElement.querySelector( '.slide-background' )\n\t\t\t\t});\n\n\t\t\t\t// Create fragment scroll triggers for the auto-animate slide\n\t\t\t\tthis.createFragmentTriggersForPage( autoAnimatePage, autoAnimatePage.slideElement );\n\n\t\t\t\tpage.autoAnimatePages.push( autoAnimatePage );\n\n\t\t\t\t// Return our slide trigger\n\t\t\t\treturn {\n\t\t\t\t\tpage: autoAnimatePage,\n\t\t\t\t\tactivate: () => this.activatePage( autoAnimatePage ),\n\t\t\t\t\tdeactivate: () => this.deactivatePage( autoAnimatePage )\n\t\t\t\t};\n\t\t\t}));\n\t\t}\n\n\t}\n\n\t/**\n\t * Helper method for creating a page definition and adding\n\t * required fields. A \"page\" is a slide or auto-animate step.\n\t */\n\tcreatePage( page ) {\n\n\t\tpage.scrollTriggers = [];\n\t\tpage.indexh = parseInt( page.slideElement.getAttribute( 'data-index-h' ), 10 );\n\t\tpage.indexv = parseInt( page.slideElement.getAttribute( 'data-index-v' ), 10 );\n\n\t\treturn page;\n\n\t}\n\n\t/**\n\t * Rerenders progress bar segments so that they match the current\n\t * reveal.js config and size.\n\t */\n\tsyncProgressBar() {\n\n\t\tthis.progressBarInner.querySelectorAll( '.scrollbar-slide' ).forEach( slide => slide.remove() );\n\n\t\tconst scrollHeight = this.viewportElement.scrollHeight;\n\t\tconst viewportHeight = this.viewportElement.offsetHeight;\n\t\tconst viewportHeightFactor = viewportHeight / scrollHeight;\n\n\t\tthis.progressBarHeight = this.progressBarInner.offsetHeight;\n\t\tthis.playheadHeight = Math.max( viewportHeightFactor * this.progressBarHeight, MIN_PLAYHEAD_HEIGHT );\n\t\tthis.progressBarScrollableHeight = this.progressBarHeight - this.playheadHeight;\n\n\t\tconst progressSegmentHeight = viewportHeight / scrollHeight * this.progressBarHeight;\n\t\tconst spacing = Math.min( progressSegmentHeight / 8, MAX_PROGRESS_SPACING );\n\n\t\tthis.progressBarPlayhead.style.height = this.playheadHeight - spacing + 'px';\n\n\t\t// Don't show individual segments if they're too small\n\t\tif( progressSegmentHeight > MIN_PROGRESS_SEGMENT_HEIGHT ) {\n\n\t\t\tthis.slideTriggers.forEach( slideTrigger => {\n\n\t\t\t\tconst { page } = slideTrigger;\n\n\t\t\t\t// Visual representation of a slide\n\t\t\t\tpage.progressBarSlide = document.createElement( 'div' );\n\t\t\t\tpage.progressBarSlide.className = 'scrollbar-slide';\n\t\t\t\tpage.progressBarSlide.style.top = slideTrigger.range[0] * this.progressBarHeight + 'px';\n\t\t\t\tpage.progressBarSlide.style.height = ( slideTrigger.range[1] - slideTrigger.range[0] ) * this.progressBarHeight - spacing + 'px';\n\t\t\t\tpage.progressBarSlide.classList.toggle( 'has-triggers', page.scrollTriggers.length > 0 );\n\t\t\t\tthis.progressBarInner.appendChild( page.progressBarSlide );\n\n\t\t\t\t// Visual representations of each scroll trigger\n\t\t\t\tpage.scrollTriggerElements = page.scrollTriggers.map( ( trigger, i ) => {\n\n\t\t\t\t\tconst triggerElement = document.createElement( 'div' );\n\t\t\t\t\ttriggerElement.className = 'scrollbar-trigger';\n\t\t\t\t\ttriggerElement.style.top = ( trigger.range[0] - slideTrigger.range[0] ) * this.progressBarHeight + 'px';\n\t\t\t\t\ttriggerElement.style.height = ( trigger.range[1] - trigger.range[0] ) * this.progressBarHeight - spacing + 'px';\n\t\t\t\t\tpage.progressBarSlide.appendChild( triggerElement );\n\n\t\t\t\t\tif( i === 0 ) triggerElement.style.display = 'none';\n\n\t\t\t\t\treturn triggerElement;\n\n\t\t\t\t} );\n\n\t\t\t} );\n\n\t\t}\n\t\telse {\n\n\t\t\tthis.pages.forEach( page => page.progressBarSlide = null );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Reads the current scroll position and updates our active\n\t * trigger states accordingly.\n\t */\n\tsyncScrollPosition() {\n\n\t\tconst viewportHeight = this.viewportElement.offsetHeight;\n\t\tconst viewportHeightFactor = viewportHeight / this.viewportElement.scrollHeight;\n\n\t\tconst scrollTop = this.viewportElement.scrollTop;\n\t\tconst scrollHeight = this.viewportElement.scrollHeight - viewportHeight\n\t\tconst scrollProgress = Math.max( Math.min( scrollTop / scrollHeight, 1 ), 0 );\n\t\tconst scrollProgressMid = Math.max( Math.min( ( scrollTop + viewportHeight / 2 ) / this.viewportElement.scrollHeight, 1 ), 0 );\n\n\t\tlet activePage;\n\n\t\tthis.slideTriggers.forEach( ( trigger ) => {\n\t\t\tconst { page } = trigger;\n\n\t\t\tconst shouldPreload = scrollProgress >= trigger.range[0] - viewportHeightFactor*2 &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tscrollProgress <= trigger.range[1] + viewportHeightFactor*2;\n\n\t\t\t// Load slides that are within the preload range\n\t\t\tif( shouldPreload && !page.loaded ) {\n\t\t\t\tpage.loaded = true;\n\t\t\t\tthis.Reveal.slideContent.load( page.slideElement );\n\t\t\t}\n\t\t\telse if( page.loaded ) {\n\t\t\t\tpage.loaded = false;\n\t\t\t\tthis.Reveal.slideContent.unload( page.slideElement );\n\t\t\t}\n\n\t\t\t// If we're within this trigger range, activate it\n\t\t\tif( scrollProgress >= trigger.range[0] && scrollProgress <= trigger.range[1] ) {\n\t\t\t\tthis.activateTrigger( trigger );\n\t\t\t\tactivePage = trigger.page;\n\t\t\t}\n\t\t\t// .. otherwise deactivate\n\t\t\telse if( trigger.active ) {\n\t\t\t\tthis.deactivateTrigger( trigger );\n\t\t\t}\n\t\t} );\n\n\t\t// Each page can have its own scroll triggers, check if any of those\n\t\t// need to be activated/deactivated\n\t\tif( activePage ) {\n\t\t\tactivePage.scrollTriggers.forEach( ( trigger ) => {\n\t\t\t\tif( scrollProgressMid >= trigger.range[0] && scrollProgressMid <= trigger.range[1] ) {\n\t\t\t\t\tthis.activateTrigger( trigger );\n\t\t\t\t}\n\t\t\t\telse if( trigger.active ) {\n\t\t\t\t\tthis.deactivateTrigger( trigger );\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\t// Update our visual progress indication\n\t\tthis.setProgressBarValue( scrollTop / ( this.viewportElement.scrollHeight - viewportHeight ) );\n\n\t}\n\n\t/**\n\t * Moves the progress bar playhead to the specified position.\n\t *\n\t * @param {number} progress 0-1\n\t */\n\tsetProgressBarValue( progress ) {\n\n\t\tif( this.progressBar ) {\n\n\t\t\tthis.progressBarPlayhead.style.transform = `translateY(${progress * this.progressBarScrollableHeight}px)`;\n\n\t\t\tthis.getAllPages()\n\t\t\t\t.filter( page => page.progressBarSlide )\n\t\t\t\t.forEach( ( page ) => {\n\t\t\t\t\tpage.progressBarSlide.classList.toggle( 'active', page.active === true );\n\n\t\t\t\t\tpage.scrollTriggers.forEach( ( trigger, i ) => {\n\t\t\t\t\t\tpage.scrollTriggerElements[i].classList.toggle( 'active', page.active === true && trigger.active === true );\n\t\t\t\t\t} );\n\t\t\t\t} );\n\n\t\t\tthis.showProgressBar();\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Show the progress bar and, if configured, automatically hide\n\t * it after a delay.\n\t */\n\tshowProgressBar() {\n\n\t\tthis.progressBar.classList.add( 'visible' );\n\n\t\tclearTimeout( this.hideProgressBarTimeout );\n\n\t\tif( this.Reveal.getConfig().scrollProgress === 'auto' && !this.draggingProgressBar ) {\n\n\t\t\tthis.hideProgressBarTimeout = setTimeout( () => {\n\t\t\t\tif( this.progressBar ) {\n\t\t\t\t\tthis.progressBar.classList.remove( 'visible' );\n\t\t\t\t}\n\t\t\t}, HIDE_SCROLLBAR_TIMEOUT );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Scroll to the previous page.\n\t */\n\tprev() {\n\n\t\tthis.viewportElement.scrollTop -= this.scrollTriggerHeight;\n\n\t}\n\n\t/**\n\t * Scroll to the next page.\n\t */\n\tnext() {\n\n\t\tthis.viewportElement.scrollTop += this.scrollTriggerHeight;\n\n\t}\n\n\t/**\n\t * Scrolls the given slide element into view.\n\t *\n\t * @param {HTMLElement} slideElement\n\t */\n\tscrollToSlide( slideElement ) {\n\n\t\t// If the scroll view isn't active yet, queue this action\n\t\tif( !this.active ) {\n\t\t\tthis.activatedCallbacks.push( () => this.scrollToSlide( slideElement ) );\n\t\t}\n\t\telse {\n\t\t\t// Find the trigger for this slide\n\t\t\tconst trigger = this.getScrollTriggerBySlide( slideElement );\n\n\t\t\tif( trigger ) {\n\t\t\t\t// Use the trigger's range to calculate the scroll position\n\t\t\t\tthis.viewportElement.scrollTop = trigger.range[0] * ( this.viewportElement.scrollHeight - this.viewportElement.offsetHeight );\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Persists the current scroll position to session storage\n\t * so that it can be restored.\n\t */\n\tstoreScrollPosition() {\n\n\t\tclearTimeout( this.storeScrollPositionTimeout );\n\n\t\tthis.storeScrollPositionTimeout = setTimeout( () => {\n\t\t\tsessionStorage.setItem( 'reveal-scroll-top', this.viewportElement.scrollTop );\n\t\t\tsessionStorage.setItem( 'reveal-scroll-origin', location.origin + location.pathname );\n\n\t\t\tthis.storeScrollPositionTimeout = null;\n\t\t}, 50 );\n\n\t}\n\n\t/**\n\t * Restores the scroll position when a deck is reloader.\n\t */\n\trestoreScrollPosition() {\n\n\t\tconst scrollPosition = sessionStorage.getItem( 'reveal-scroll-top' );\n\t\tconst scrollOrigin = sessionStorage.getItem( 'reveal-scroll-origin' );\n\n\t\tif( scrollPosition && scrollOrigin === location.origin + location.pathname ) {\n\t\t\tthis.viewportElement.scrollTop = parseInt( scrollPosition, 10 );\n\t\t}\n\n\t}\n\n\t/**\n\t * Activates the given page and starts its embedded content\n\t * if there is any.\n\t *\n\t * @param {object} page\n\t */\n\tactivatePage( page ) {\n\n\t\tif( !page.active ) {\n\n\t\t\tpage.active = true;\n\n\t\t\tconst { slideElement, backgroundElement, contentElement, indexh, indexv } = page;\n\n\t\t\tcontentElement.style.display = 'block';\n\n\t\t\tslideElement.classList.add( 'present' );\n\n\t\t\tif( backgroundElement ) {\n\t\t\t\tbackgroundElement.classList.add( 'present' );\n\t\t\t}\n\n\t\t\tthis.Reveal.setCurrentScrollPage( slideElement, indexh, indexv );\n\t\t\tthis.Reveal.backgrounds.bubbleSlideContrastClassToElement( slideElement, this.viewportElement );\n\n\t\t\t// If this page is part of an auto-animation there will be one\n\t\t\t// content element per auto-animated page. We need to show the\n\t\t\t// current page and hide all others.\n\t\t\tArray.from( contentElement.parentNode.querySelectorAll( '.scroll-page-content' ) ).forEach( sibling => {\n\t\t\t\tif( sibling !== contentElement ) {\n\t\t\t\t\tsibling.style.display = 'none';\n\t\t\t\t}\n\t\t\t});\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Deactivates the page after it has been visible.\n\t *\n\t * @param {object} page\n\t */\n\tdeactivatePage( page ) {\n\n\t\tif( page.active ) {\n\n\t\t\tpage.active = false;\n\t\t\tif( page.slideElement ) page.slideElement.classList.remove( 'present' );\n\t\t\tif( page.backgroundElement ) page.backgroundElement.classList.remove( 'present' );\n\n\t\t}\n\n\t}\n\n\tactivateTrigger( trigger ) {\n\n\t\tif( !trigger.active ) {\n\t\t\ttrigger.active = true;\n\t\t\ttrigger.activate();\n\t\t}\n\n\t}\n\n\tdeactivateTrigger( trigger ) {\n\n\t\tif( trigger.active ) {\n\t\t\ttrigger.active = false;\n\n\t\t\tif( trigger.deactivate ) {\n\t\t\t\ttrigger.deactivate();\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Retrieve a slide by its original h/v index (i.e. the indices the\n\t * slide had before being linearized).\n\t *\n\t * @param {number} h\n\t * @param {number} v\n\t * @returns {HTMLElement}\n\t */\n\tgetSlideByIndices( h, v ) {\n\n\t\tconst page = this.getAllPages().find( page => {\n\t\t\treturn page.indexh === h && page.indexv === v;\n\t\t} );\n\n\t\treturn page ? page.slideElement : null;\n\n\t}\n\n\t/**\n\t * Retrieve a list of all scroll triggers for the given slide\n\t * DOM element.\n\t *\n\t * @param {HTMLElement} slide\n\t * @returns {Array}\n\t */\n\tgetScrollTriggerBySlide( slide ) {\n\n\t\treturn this.slideTriggers.find( trigger => trigger.page.slideElement === slide );\n\n\t}\n\n\t/**\n\t * Get a list of all pages in the scroll view. This includes\n\t * both top-level slides and auto-animate steps.\n\t *\n\t * @returns {Array}\n\t */\n\tgetAllPages() {\n\n\t\treturn this.pages.flatMap( page => [page, ...(page.autoAnimatePages || [])] );\n\n\t}\n\n\tonScroll() {\n\n\t\tthis.syncScrollPosition();\n\t\tthis.storeScrollPosition();\n\n\t}\n\n\tget viewportElement() {\n\n\t\treturn this.Reveal.getViewportElement();\n\n\t}\n\n}\n", "import { SLIDES_SELECTOR } from '../utils/constants.js'\nimport { queryAll, createStyleSheet } from '../utils/util.js'\n\n/**\n * Setups up our presentation for printing/exporting to PDF.\n */\nexport default class PrintView {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\t/**\n\t * Configures the presentation for printing to a static\n\t * PDF.\n\t */\n\tasync activate() {\n\n\t\tconst config = this.Reveal.getConfig();\n\t\tconst slides = queryAll( this.Reveal.getRevealElement(), SLIDES_SELECTOR )\n\n\t\t// Compute slide numbers now, before we start duplicating slides\n\t\tconst injectPageNumbers = config.slideNumber && /all|print/i.test( config.showSlideNumber );\n\n\t\tconst slideSize = this.Reveal.getComputedSlideSize( window.innerWidth, window.innerHeight );\n\n\t\t// Dimensions of the PDF pages\n\t\tconst pageWidth = Math.floor( slideSize.width * ( 1 + config.margin ) ),\n\t\t\tpageHeight = Math.floor( slideSize.height * ( 1 + config.margin ) );\n\n\t\t// Dimensions of slides within the pages\n\t\tconst slideWidth = slideSize.width,\n\t\t\tslideHeight = slideSize.height;\n\n\t\tawait new Promise( requestAnimationFrame );\n\n\t\t// Let the browser know what page size we want to print\n\t\tcreateStyleSheet( '@page{size:'+ pageWidth +'px '+ pageHeight +'px; margin: 0px;}' );\n\n\t\t// Limit the size of certain elements to the dimensions of the slide\n\t\tcreateStyleSheet( '.reveal section>img, .reveal section>video, .reveal section>iframe{max-width: '+ slideWidth +'px; max-height:'+ slideHeight +'px}' );\n\n\t\tdocument.documentElement.classList.add( 'reveal-print', 'print-pdf' );\n\t\tdocument.body.style.width = pageWidth + 'px';\n\t\tdocument.body.style.height = pageHeight + 'px';\n\n\t\tconst viewportElement = this.Reveal.getViewportElement();\n\t\tlet presentationBackground;\n\t\tif( viewportElement ) {\n\t\t\tconst viewportStyles = window.getComputedStyle( viewportElement );\n\t\t\tif( viewportStyles && viewportStyles.background ) {\n\t\t\t\tpresentationBackground = viewportStyles.background;\n\t\t\t}\n\t\t}\n\n\t\t// Make sure stretch elements fit on slide\n\t\tawait new Promise( requestAnimationFrame );\n\t\tthis.Reveal.layoutSlideContents( slideWidth, slideHeight );\n\n\t\t// Batch scrollHeight access to prevent layout thrashing\n\t\tawait new Promise( requestAnimationFrame );\n\n\t\tconst slideScrollHeights = slides.map( slide => slide.scrollHeight );\n\n\t\tconst pages = [];\n\t\tconst pageContainer = slides[0].parentNode;\n\t\tlet slideNumber = 1;\n\n\t\t// Slide and slide background layout\n\t\tslides.forEach( function( slide, index ) {\n\n\t\t\t// Vertical stacks are not centred since their section\n\t\t\t// children will be\n\t\t\tif( slide.classList.contains( 'stack' ) === false ) {\n\t\t\t\t// Center the slide inside of the page, giving the slide some margin\n\t\t\t\tlet left = ( pageWidth - slideWidth ) / 2;\n\t\t\t\tlet top = ( pageHeight - slideHeight ) / 2;\n\n\t\t\t\tconst contentHeight = slideScrollHeights[ index ];\n\t\t\t\tlet numberOfPages = Math.max( Math.ceil( contentHeight / pageHeight ), 1 );\n\n\t\t\t\t// Adhere to configured pages per slide limit\n\t\t\t\tnumberOfPages = Math.min( numberOfPages, config.pdfMaxPagesPerSlide );\n\n\t\t\t\t// Center slides vertically\n\t\t\t\tif( numberOfPages === 1 && config.center || slide.classList.contains( 'center' ) ) {\n\t\t\t\t\ttop = Math.max( ( pageHeight - contentHeight ) / 2, 0 );\n\t\t\t\t}\n\n\t\t\t\t// Wrap the slide in a page element and hide its overflow\n\t\t\t\t// so that no page ever flows onto another\n\t\t\t\tconst page = document.createElement( 'div' );\n\t\t\t\tpages.push( page );\n\n\t\t\t\tpage.className = 'pdf-page';\n\t\t\t\tpage.style.height = ( ( pageHeight + config.pdfPageHeightOffset ) * numberOfPages ) + 'px';\n\n\t\t\t\t// Copy the presentation-wide background to each individual\n\t\t\t\t// page when printing\n\t\t\t\tif( presentationBackground ) {\n\t\t\t\t\tpage.style.background = presentationBackground;\n\t\t\t\t}\n\n\t\t\t\tpage.appendChild( slide );\n\n\t\t\t\t// Position the slide inside of the page\n\t\t\t\tslide.style.left = left + 'px';\n\t\t\t\tslide.style.top = top + 'px';\n\t\t\t\tslide.style.width = slideWidth + 'px';\n\n\t\t\t\tthis.Reveal.slideContent.layout( slide );\n\n\t\t\t\tif( slide.slideBackgroundElement ) {\n\t\t\t\t\tpage.insertBefore( slide.slideBackgroundElement, slide );\n\t\t\t\t}\n\n\t\t\t\t// Inject notes if `showNotes` is enabled\n\t\t\t\tif( config.showNotes ) {\n\n\t\t\t\t\t// Are there notes for this slide?\n\t\t\t\t\tconst notes = this.Reveal.getSlideNotes( slide );\n\t\t\t\t\tif( notes ) {\n\n\t\t\t\t\t\tconst notesSpacing = 8;\n\t\t\t\t\t\tconst notesLayout = typeof config.showNotes === 'string' ? config.showNotes : 'inline';\n\t\t\t\t\t\tconst notesElement = document.createElement( 'div' );\n\t\t\t\t\t\tnotesElement.classList.add( 'speaker-notes' );\n\t\t\t\t\t\tnotesElement.classList.add( 'speaker-notes-pdf' );\n\t\t\t\t\t\tnotesElement.setAttribute( 'data-layout', notesLayout );\n\t\t\t\t\t\tnotesElement.innerHTML = notes;\n\n\t\t\t\t\t\tif( notesLayout === 'separate-page' ) {\n\t\t\t\t\t\t\tpages.push( notesElement );\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tnotesElement.style.left = notesSpacing + 'px';\n\t\t\t\t\t\t\tnotesElement.style.bottom = notesSpacing + 'px';\n\t\t\t\t\t\t\tnotesElement.style.width = ( pageWidth - notesSpacing*2 ) + 'px';\n\t\t\t\t\t\t\tpage.appendChild( notesElement );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Inject page numbers if `slideNumbers` are enabled\n\t\t\t\tif( injectPageNumbers ) {\n\t\t\t\t\tconst numberElement = document.createElement( 'div' );\n\t\t\t\t\tnumberElement.classList.add( 'slide-number' );\n\t\t\t\t\tnumberElement.classList.add( 'slide-number-pdf' );\n\t\t\t\t\tnumberElement.innerHTML = slideNumber++;\n\t\t\t\t\tpage.appendChild( numberElement );\n\t\t\t\t}\n\n\t\t\t\t// Copy page and show fragments one after another\n\t\t\t\tif( config.pdfSeparateFragments ) {\n\n\t\t\t\t\t// Each fragment 'group' is an array containing one or more\n\t\t\t\t\t// fragments. Multiple fragments that appear at the same time\n\t\t\t\t\t// are part of the same group.\n\t\t\t\t\tconst fragmentGroups = this.Reveal.fragments.sort( page.querySelectorAll( '.fragment' ), true );\n\n\t\t\t\t\tlet previousFragmentStep;\n\n\t\t\t\t\tfragmentGroups.forEach( function( fragments, index ) {\n\n\t\t\t\t\t\t// Remove 'current-fragment' from the previous group\n\t\t\t\t\t\tif( previousFragmentStep ) {\n\t\t\t\t\t\t\tpreviousFragmentStep.forEach( function( fragment ) {\n\t\t\t\t\t\t\t\tfragment.classList.remove( 'current-fragment' );\n\t\t\t\t\t\t\t} );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Show the fragments for the current index\n\t\t\t\t\t\tfragments.forEach( function( fragment ) {\n\t\t\t\t\t\t\tfragment.classList.add( 'visible', 'current-fragment' );\n\t\t\t\t\t\t}, this );\n\n\t\t\t\t\t\t// Create a separate page for the current fragment state\n\t\t\t\t\t\tconst clonedPage = page.cloneNode( true );\n\n\t\t\t\t\t\t// Inject unique page numbers for fragments\n\t\t\t\t\t\tif( injectPageNumbers ) {\n\t\t\t\t\t\t\tconst numberElement = clonedPage.querySelector( '.slide-number-pdf' );\n\t\t\t\t\t\t\tconst fragmentNumber = index + 1;\n\t\t\t\t\t\t\tnumberElement.innerHTML += '.' + fragmentNumber;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tpages.push( clonedPage );\n\n\t\t\t\t\t\tpreviousFragmentStep = fragments;\n\n\t\t\t\t\t}, this );\n\n\t\t\t\t\t// Reset the first/original page so that all fragments are hidden\n\t\t\t\t\tfragmentGroups.forEach( function( fragments ) {\n\t\t\t\t\t\tfragments.forEach( function( fragment ) {\n\t\t\t\t\t\t\tfragment.classList.remove( 'visible', 'current-fragment' );\n\t\t\t\t\t\t} );\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\t\t\t\t// Show all fragments\n\t\t\t\telse {\n\t\t\t\t\tqueryAll( page, '.fragment:not(.fade-out)' ).forEach( function( fragment ) {\n\t\t\t\t\t\tfragment.classList.add( 'visible' );\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}, this );\n\n\t\tawait new Promise( requestAnimationFrame );\n\n\t\tpages.forEach( page => pageContainer.appendChild( page ) );\n\n\t\t// Re-run JS-based content layout after the slide is added to page DOM\n\t\tthis.Reveal.slideContent.layout( this.Reveal.getSlidesElement() );\n\n\t\t// Notify subscribers that the PDF layout is good to go\n\t\tthis.Reveal.dispatchEvent({ type: 'pdf-ready' });\n\n\t\tviewportElement.classList.remove( 'loading-scroll-mode' );\n\n\t}\n\n\t/**\n\t * Checks if the print mode is/should be activated.\n\t */\n\tisActive() {\n\n\t\treturn this.Reveal.getConfig().view === 'print';\n\n\t}\n\n}", "import { extend, queryAll } from '../utils/util.js'\n\n/**\n * Handles sorting and navigation of slide fragments.\n * Fragments are elements within a slide that are\n * revealed/animated incrementally.\n */\nexport default class Fragments {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tif( config.fragments === false ) {\n\t\t\tthis.disable();\n\t\t}\n\t\telse if( oldConfig.fragments === false ) {\n\t\t\tthis.enable();\n\t\t}\n\n\t}\n\n\t/**\n\t * If fragments are disabled in the deck, they should all be\n\t * visible rather than stepped through.\n\t */\n\tdisable() {\n\n\t\tqueryAll( this.Reveal.getSlidesElement(), '.fragment' ).forEach( element => {\n\t\t\telement.classList.add( 'visible' );\n\t\t\telement.classList.remove( 'current-fragment' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Reverse of #disable(). Only called if fragments have\n\t * previously been disabled.\n\t */\n\tenable() {\n\n\t\tqueryAll( this.Reveal.getSlidesElement(), '.fragment' ).forEach( element => {\n\t\t\telement.classList.remove( 'visible' );\n\t\t\telement.classList.remove( 'current-fragment' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Returns an object describing the available fragment\n\t * directions.\n\t *\n\t * @return {{prev: boolean, next: boolean}}\n\t */\n\tavailableRoutes() {\n\n\t\tlet currentSlide = this.Reveal.getCurrentSlide();\n\t\tif( currentSlide && this.Reveal.getConfig().fragments ) {\n\t\t\tlet fragments = currentSlide.querySelectorAll( '.fragment:not(.disabled)' );\n\t\t\tlet hiddenFragments = currentSlide.querySelectorAll( '.fragment:not(.disabled):not(.visible)' );\n\n\t\t\treturn {\n\t\t\t\tprev: fragments.length - hiddenFragments.length > 0,\n\t\t\t\tnext: !!hiddenFragments.length\n\t\t\t};\n\t\t}\n\t\telse {\n\t\t\treturn { prev: false, next: false };\n\t\t}\n\n\t}\n\n\t/**\n\t * Return a sorted fragments list, ordered by an increasing\n\t * \"data-fragment-index\" attribute.\n\t *\n\t * Fragments will be revealed in the order that they are returned by\n\t * this function, so you can use the index attributes to control the\n\t * order of fragment appearance.\n\t *\n\t * To maintain a sensible default fragment order, fragments are presumed\n\t * to be passed in document order. This function adds a \"fragment-index\"\n\t * attribute to each node if such an attribute is not already present,\n\t * and sets that attribute to an integer value which is the position of\n\t * the fragment within the fragments list.\n\t *\n\t * @param {object[]|*} fragments\n\t * @param {boolean} grouped If true the returned array will contain\n\t * nested arrays for all fragments with the same index\n\t * @return {object[]} sorted Sorted array of fragments\n\t */\n\tsort( fragments, grouped = false ) {\n\n\t\tfragments = Array.from( fragments );\n\n\t\tlet ordered = [],\n\t\t\tunordered = [],\n\t\t\tsorted = [];\n\n\t\t// Group ordered and unordered elements\n\t\tfragments.forEach( fragment => {\n\t\t\tif( fragment.hasAttribute( 'data-fragment-index' ) ) {\n\t\t\t\tlet index = parseInt( fragment.getAttribute( 'data-fragment-index' ), 10 );\n\n\t\t\t\tif( !ordered[index] ) {\n\t\t\t\t\tordered[index] = [];\n\t\t\t\t}\n\n\t\t\t\tordered[index].push( fragment );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tunordered.push( [ fragment ] );\n\t\t\t}\n\t\t} );\n\n\t\t// Append fragments without explicit indices in their\n\t\t// DOM order\n\t\tordered = ordered.concat( unordered );\n\n\t\t// Manually count the index up per group to ensure there\n\t\t// are no gaps\n\t\tlet index = 0;\n\n\t\t// Push all fragments in their sorted order to an array,\n\t\t// this flattens the groups\n\t\tordered.forEach( group => {\n\t\t\tgroup.forEach( fragment => {\n\t\t\t\tsorted.push( fragment );\n\t\t\t\tfragment.setAttribute( 'data-fragment-index', index );\n\t\t\t} );\n\n\t\t\tindex ++;\n\t\t} );\n\n\t\treturn grouped === true ? ordered : sorted;\n\n\t}\n\n\t/**\n\t * Sorts and formats all of fragments in the\n\t * presentation.\n\t */\n\tsortAll() {\n\n\t\tthis.Reveal.getHorizontalSlides().forEach( horizontalSlide => {\n\n\t\t\tlet verticalSlides = queryAll( horizontalSlide, 'section' );\n\t\t\tverticalSlides.forEach( ( verticalSlide, y ) => {\n\n\t\t\t\tthis.sort( verticalSlide.querySelectorAll( '.fragment' ) );\n\n\t\t\t}, this );\n\n\t\t\tif( verticalSlides.length === 0 ) this.sort( horizontalSlide.querySelectorAll( '.fragment' ) );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Refreshes the fragments on the current slide so that they\n\t * have the appropriate classes (.visible + .current-fragment).\n\t *\n\t * @param {number} [index] The index of the current fragment\n\t * @param {array} [fragments] Array containing all fragments\n\t * in the current slide\n\t *\n\t * @return {{shown: array, hidden: array}}\n\t */\n\tupdate( index, fragments, slide = this.Reveal.getCurrentSlide() ) {\n\n\t\tlet changedFragments = {\n\t\t\tshown: [],\n\t\t\thidden: []\n\t\t};\n\n\t\tif( slide && this.Reveal.getConfig().fragments ) {\n\n\t\t\tfragments = fragments || this.sort( slide.querySelectorAll( '.fragment' ) );\n\n\t\t\tif( fragments.length ) {\n\n\t\t\t\tlet maxIndex = 0;\n\n\t\t\t\tif( typeof index !== 'number' ) {\n\t\t\t\t\tlet currentFragment = this.sort( slide.querySelectorAll( '.fragment.visible' ) ).pop();\n\t\t\t\t\tif( currentFragment ) {\n\t\t\t\t\t\tindex = parseInt( currentFragment.getAttribute( 'data-fragment-index' ) || 0, 10 );\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tArray.from( fragments ).forEach( ( el, i ) => {\n\n\t\t\t\t\tif( el.hasAttribute( 'data-fragment-index' ) ) {\n\t\t\t\t\t\ti = parseInt( el.getAttribute( 'data-fragment-index' ), 10 );\n\t\t\t\t\t}\n\n\t\t\t\t\tmaxIndex = Math.max( maxIndex, i );\n\n\t\t\t\t\t// Visible fragments\n\t\t\t\t\tif( i <= index ) {\n\t\t\t\t\t\tlet wasVisible = el.classList.contains( 'visible' )\n\t\t\t\t\t\tel.classList.add( 'visible' );\n\t\t\t\t\t\tel.classList.remove( 'current-fragment' );\n\n\t\t\t\t\t\tif( i === index ) {\n\t\t\t\t\t\t\t// Announce the fragments one by one to the Screen Reader\n\t\t\t\t\t\t\tthis.Reveal.announceStatus( this.Reveal.getStatusText( el ) );\n\n\t\t\t\t\t\t\tel.classList.add( 'current-fragment' );\n\t\t\t\t\t\t\tthis.Reveal.slideContent.startEmbeddedContent( el );\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif( !wasVisible ) {\n\t\t\t\t\t\t\tchangedFragments.shown.push( el )\n\t\t\t\t\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\t\t\t\t\ttarget: el,\n\t\t\t\t\t\t\t\ttype: 'visible',\n\t\t\t\t\t\t\t\tbubbles: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// Hidden fragments\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet wasVisible = el.classList.contains( 'visible' )\n\t\t\t\t\t\tel.classList.remove( 'visible' );\n\t\t\t\t\t\tel.classList.remove( 'current-fragment' );\n\n\t\t\t\t\t\tif( wasVisible ) {\n\t\t\t\t\t\t\tthis.Reveal.slideContent.stopEmbeddedContent( el );\n\t\t\t\t\t\t\tchangedFragments.hidden.push( el );\n\t\t\t\t\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\t\t\t\t\ttarget: el,\n\t\t\t\t\t\t\t\ttype: 'hidden',\n\t\t\t\t\t\t\t\tbubbles: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\t// Write the current fragment index to the slide <section>.\n\t\t\t\t// This can be used by end users to apply styles based on\n\t\t\t\t// the current fragment index.\n\t\t\t\tindex = typeof index === 'number' ? index : -1;\n\t\t\t\tindex = Math.max( Math.min( index, maxIndex ), -1 );\n\t\t\t\tslide.setAttribute( 'data-fragment', index );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif( changedFragments.hidden.length ) {\n\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\ttype: 'fragmenthidden',\n\t\t\t\tdata: {\n\t\t\t\t\tfragment: changedFragments.hidden[0],\n\t\t\t\t\tfragments: changedFragments.hidden\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tif( changedFragments.shown.length ) {\n\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\ttype: 'fragmentshown',\n\t\t\t\tdata: {\n\t\t\t\t\tfragment: changedFragments.shown[0],\n\t\t\t\t\tfragments: changedFragments.shown\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\treturn changedFragments;\n\n\t}\n\n\t/**\n\t * Formats the fragments on the given slide so that they have\n\t * valid indices. Call this if fragments are changed in the DOM\n\t * after reveal.js has already initialized.\n\t *\n\t * @param {HTMLElement} slide\n\t * @return {Array} a list of the HTML fragments that were synced\n\t */\n\tsync( slide = this.Reveal.getCurrentSlide() ) {\n\n\t\treturn this.sort( slide.querySelectorAll( '.fragment' ) );\n\n\t}\n\n\t/**\n\t * Navigate to the specified slide fragment.\n\t *\n\t * @param {?number} index The index of the fragment that\n\t * should be shown, -1 means all are invisible\n\t * @param {number} offset Integer offset to apply to the\n\t * fragment index\n\t *\n\t * @return {boolean} true if a change was made in any\n\t * fragments visibility as part of this call\n\t */\n\tgoto( index, offset = 0 ) {\n\n\t\tlet currentSlide = this.Reveal.getCurrentSlide();\n\t\tif( currentSlide && this.Reveal.getConfig().fragments ) {\n\n\t\t\tlet fragments = this.sort( currentSlide.querySelectorAll( '.fragment:not(.disabled)' ) );\n\t\t\tif( fragments.length ) {\n\n\t\t\t\t// If no index is specified, find the current\n\t\t\t\tif( typeof index !== 'number' ) {\n\t\t\t\t\tlet lastVisibleFragment = this.sort( currentSlide.querySelectorAll( '.fragment:not(.disabled).visible' ) ).pop();\n\n\t\t\t\t\tif( lastVisibleFragment ) {\n\t\t\t\t\t\tindex = parseInt( lastVisibleFragment.getAttribute( 'data-fragment-index' ) || 0, 10 );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tindex = -1;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Apply the offset if there is one\n\t\t\t\tindex += offset;\n\n\t\t\t\tlet changedFragments = this.update( index, fragments );\n\n\t\t\t\tthis.Reveal.controls.update();\n\t\t\t\tthis.Reveal.progress.update();\n\n\t\t\t\tif( this.Reveal.getConfig().fragmentInURL ) {\n\t\t\t\t\tthis.Reveal.location.writeURL();\n\t\t\t\t}\n\n\t\t\t\treturn !!( changedFragments.shown.length || changedFragments.hidden.length );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t/**\n\t * Navigate to the next slide fragment.\n\t *\n\t * @return {boolean} true if there was a next fragment,\n\t * false otherwise\n\t */\n\tnext() {\n\n\t\treturn this.goto( null, 1 );\n\n\t}\n\n\t/**\n\t * Navigate to the previous slide fragment.\n\t *\n\t * @return {boolean} true if there was a previous fragment,\n\t * false otherwise\n\t */\n\tprev() {\n\n\t\treturn this.goto( null, -1 );\n\n\t}\n\n}", "import { SLIDES_SELECTOR } from '../utils/constants.js'\nimport { extend, queryAll, transformElement } from '../utils/util.js'\n\n/**\n * Handles all logic related to the overview mode\n * (birds-eye view of all slides).\n */\nexport default class Overview {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.active = false;\n\n\t\tthis.onSlideClicked = this.onSlideClicked.bind( this );\n\n\t}\n\n\t/**\n\t * Displays the overview of slides (quick nav) by scaling\n\t * down and arranging all slide elements.\n\t */\n\tactivate() {\n\n\t\t// Only proceed if enabled in config\n\t\tif( this.Reveal.getConfig().overview && !this.Reveal.isScrollView() && !this.isActive() ) {\n\n\t\t\tthis.active = true;\n\n\t\t\tthis.Reveal.getRevealElement().classList.add( 'overview' );\n\n\t\t\t// Don't auto-slide while in overview mode\n\t\t\tthis.Reveal.cancelAutoSlide();\n\n\t\t\t// Move the backgrounds element into the slide container to\n\t\t\t// that the same scaling is applied\n\t\t\tthis.Reveal.getSlidesElement().appendChild( this.Reveal.getBackgroundsElement() );\n\n\t\t\t// Clicking on an overview slide navigates to it\n\t\t\tqueryAll( this.Reveal.getRevealElement(), SLIDES_SELECTOR ).forEach( slide => {\n\t\t\t\tif( !slide.classList.contains( 'stack' ) ) {\n\t\t\t\t\tslide.addEventListener( 'click', this.onSlideClicked, true );\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Calculate slide sizes\n\t\t\tconst margin = 70;\n\t\t\tconst slideSize = this.Reveal.getComputedSlideSize();\n\t\t\tthis.overviewSlideWidth = slideSize.width + margin;\n\t\t\tthis.overviewSlideHeight = slideSize.height + margin;\n\n\t\t\t// Reverse in RTL mode\n\t\t\tif( this.Reveal.getConfig().rtl ) {\n\t\t\t\tthis.overviewSlideWidth = -this.overviewSlideWidth;\n\t\t\t}\n\n\t\t\tthis.Reveal.updateSlidesVisibility();\n\n\t\t\tthis.layout();\n\t\t\tthis.update();\n\n\t\t\tthis.Reveal.layout();\n\n\t\t\tconst indices = this.Reveal.getIndices();\n\n\t\t\t// Notify observers of the overview showing\n\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\ttype: 'overviewshown',\n\t\t\t\tdata: {\n\t\t\t\t\t'indexh': indices.h,\n\t\t\t\t\t'indexv': indices.v,\n\t\t\t\t\t'currentSlide': this.Reveal.getCurrentSlide()\n\t\t\t\t}\n\t\t\t});\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Uses CSS transforms to position all slides in a grid for\n\t * display inside of the overview mode.\n\t */\n\tlayout() {\n\n\t\t// Layout slides\n\t\tthis.Reveal.getHorizontalSlides().forEach( ( hslide, h ) => {\n\t\t\thslide.setAttribute( 'data-index-h', h );\n\t\t\ttransformElement( hslide, 'translate3d(' + ( h * this.overviewSlideWidth ) + 'px, 0, 0)' );\n\n\t\t\tif( hslide.classList.contains( 'stack' ) ) {\n\n\t\t\t\tqueryAll( hslide, 'section' ).forEach( ( vslide, v ) => {\n\t\t\t\t\tvslide.setAttribute( 'data-index-h', h );\n\t\t\t\t\tvslide.setAttribute( 'data-index-v', v );\n\n\t\t\t\t\ttransformElement( vslide, 'translate3d(0, ' + ( v * this.overviewSlideHeight ) + 'px, 0)' );\n\t\t\t\t} );\n\n\t\t\t}\n\t\t} );\n\n\t\t// Layout slide backgrounds\n\t\tArray.from( this.Reveal.getBackgroundsElement().childNodes ).forEach( ( hbackground, h ) => {\n\t\t\ttransformElement( hbackground, 'translate3d(' + ( h * this.overviewSlideWidth ) + 'px, 0, 0)' );\n\n\t\t\tqueryAll( hbackground, '.slide-background' ).forEach( ( vbackground, v ) => {\n\t\t\t\ttransformElement( vbackground, 'translate3d(0, ' + ( v * this.overviewSlideHeight ) + 'px, 0)' );\n\t\t\t} );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Moves the overview viewport to the current slides.\n\t * Called each time the current slide changes.\n\t */\n\tupdate() {\n\n\t\tconst vmin = Math.min( window.innerWidth, window.innerHeight );\n\t\tconst scale = Math.max( vmin / 5, 150 ) / vmin;\n\t\tconst indices = this.Reveal.getIndices();\n\n\t\tthis.Reveal.transformSlides( {\n\t\t\toverview: [\n\t\t\t\t'scale('+ scale +')',\n\t\t\t\t'translateX('+ ( -indices.h * this.overviewSlideWidth ) +'px)',\n\t\t\t\t'translateY('+ ( -indices.v * this.overviewSlideHeight ) +'px)'\n\t\t\t].join( ' ' )\n\t\t} );\n\n\t}\n\n\t/**\n\t * Exits the slide overview and enters the currently\n\t * active slide.\n\t */\n\tdeactivate() {\n\n\t\t// Only proceed if enabled in config\n\t\tif( this.Reveal.getConfig().overview ) {\n\n\t\t\tthis.active = false;\n\n\t\t\tthis.Reveal.getRevealElement().classList.remove( 'overview' );\n\n\t\t\t// Temporarily add a class so that transitions can do different things\n\t\t\t// depending on whether they are exiting/entering overview, or just\n\t\t\t// moving from slide to slide\n\t\t\tthis.Reveal.getRevealElement().classList.add( 'overview-deactivating' );\n\n\t\t\tsetTimeout( () => {\n\t\t\t\tthis.Reveal.getRevealElement().classList.remove( 'overview-deactivating' );\n\t\t\t}, 1 );\n\n\t\t\t// Move the background element back out\n\t\t\tthis.Reveal.getRevealElement().appendChild( this.Reveal.getBackgroundsElement() );\n\n\t\t\t// Clean up changes made to slides\n\t\t\tqueryAll( this.Reveal.getRevealElement(), SLIDES_SELECTOR ).forEach( slide => {\n\t\t\t\ttransformElement( slide, '' );\n\n\t\t\t\tslide.removeEventListener( 'click', this.onSlideClicked, true );\n\t\t\t} );\n\n\t\t\t// Clean up changes made to backgrounds\n\t\t\tqueryAll( this.Reveal.getBackgroundsElement(), '.slide-background' ).forEach( background => {\n\t\t\t\ttransformElement( background, '' );\n\t\t\t} );\n\n\t\t\tthis.Reveal.transformSlides( { overview: '' } );\n\n\t\t\tconst indices = this.Reveal.getIndices();\n\n\t\t\tthis.Reveal.slide( indices.h, indices.v );\n\t\t\tthis.Reveal.layout();\n\t\t\tthis.Reveal.cueAutoSlide();\n\n\t\t\t// Notify observers of the overview hiding\n\t\t\tthis.Reveal.dispatchEvent({\n\t\t\t\ttype: 'overviewhidden',\n\t\t\t\tdata: {\n\t\t\t\t\t'indexh': indices.h,\n\t\t\t\t\t'indexv': indices.v,\n\t\t\t\t\t'currentSlide': this.Reveal.getCurrentSlide()\n\t\t\t\t}\n\t\t\t});\n\n\t\t}\n\t}\n\n\t/**\n\t * Toggles the slide overview mode on and off.\n\t *\n\t * @param {Boolean} [override] Flag which overrides the\n\t * toggle logic and forcibly sets the desired state. True means\n\t * overview is open, false means it's closed.\n\t */\n\ttoggle( override ) {\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? this.activate() : this.deactivate();\n\t\t}\n\t\telse {\n\t\t\tthis.isActive() ? this.deactivate() : this.activate();\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if the overview is currently active.\n\t *\n\t * @return {Boolean} true if the overview is active,\n\t * false otherwise\n\t */\n\tisActive() {\n\n\t\treturn this.active;\n\n\t}\n\n\t/**\n\t * Invoked when a slide is and we're in the overview.\n\t *\n\t * @param {object} event\n\t */\n\tonSlideClicked( event ) {\n\n\t\tif( this.isActive() ) {\n\t\t\tevent.preventDefault();\n\n\t\t\tlet element = event.target;\n\n\t\t\twhile( element && !element.nodeName.match( /section/gi ) ) {\n\t\t\t\telement = element.parentNode;\n\t\t\t}\n\n\t\t\tif( element && !element.classList.contains( 'disabled' ) ) {\n\n\t\t\t\tthis.deactivate();\n\n\t\t\t\tif( element.nodeName.match( /section/gi ) ) {\n\t\t\t\t\tlet h = parseInt( element.getAttribute( 'data-index-h' ), 10 ),\n\t\t\t\t\t\tv = parseInt( element.getAttribute( 'data-index-v' ), 10 );\n\n\t\t\t\t\tthis.Reveal.slide( h, v );\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t}\n\n}", "import { enterFullscreen } from '../utils/util.js'\n\n/**\n * Handles all reveal.js keyboard interactions.\n */\nexport default class Keyboard {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\t// A key:value map of keyboard keys and descriptions of\n\t\t// the actions they trigger\n\t\tthis.shortcuts = {};\n\n\t\t// Holds custom key code mappings\n\t\tthis.bindings = {};\n\n\t\tthis.onDocumentKeyDown = this.onDocumentKeyDown.bind( this );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tif( config.navigationMode === 'linear' ) {\n\t\t\tthis.shortcuts['&#8594;  ,  &#8595;  ,  SPACE  ,  N  ,  L  ,  J'] = 'Next slide';\n\t\t\tthis.shortcuts['&#8592;  ,  &#8593;  ,  P  ,  H  ,  K']           = 'Previous slide';\n\t\t}\n\t\telse {\n\t\t\tthis.shortcuts['N  ,  SPACE']   = 'Next slide';\n\t\t\tthis.shortcuts['P  ,  Shift SPACE']             = 'Previous slide';\n\t\t\tthis.shortcuts['&#8592;  ,  H'] = 'Navigate left';\n\t\t\tthis.shortcuts['&#8594;  ,  <PERSON>'] = 'Navigate right';\n\t\t\tthis.shortcuts['&#8593;  ,  K'] = 'Navigate up';\n\t\t\tthis.shortcuts['&#8595;  ,  J'] = 'Navigate down';\n\t\t}\n\n\t\tthis.shortcuts['Alt + &#8592;/&#8593/&#8594;/&#8595;']        = 'Navigate without fragments';\n\t\tthis.shortcuts['Shift + &#8592;/&#8593/&#8594;/&#8595;']      = 'Jump to first/last slide';\n\t\tthis.shortcuts['B  ,  .']                       = 'Pause';\n\t\tthis.shortcuts['F']                             = 'Fullscreen';\n\t\tthis.shortcuts['G']                             = 'Jump to slide';\n\t\tthis.shortcuts['ESC, O']                        = 'Slide overview';\n\n\t}\n\n\t/**\n\t * Starts listening for keyboard events.\n\t */\n\tbind() {\n\n\t\tdocument.addEventListener( 'keydown', this.onDocumentKeyDown, false );\n\n\t}\n\n\t/**\n\t * Stops listening for keyboard events.\n\t */\n\tunbind() {\n\n\t\tdocument.removeEventListener( 'keydown', this.onDocumentKeyDown, false );\n\n\t}\n\n\t/**\n\t * Add a custom key binding with optional description to\n\t * be added to the help screen.\n\t */\n\taddKeyBinding( binding, callback ) {\n\n\t\tif( typeof binding === 'object' && binding.keyCode ) {\n\t\t\tthis.bindings[binding.keyCode] = {\n\t\t\t\tcallback: callback,\n\t\t\t\tkey: binding.key,\n\t\t\t\tdescription: binding.description\n\t\t\t};\n\t\t}\n\t\telse {\n\t\t\tthis.bindings[binding] = {\n\t\t\t\tcallback: callback,\n\t\t\t\tkey: null,\n\t\t\t\tdescription: null\n\t\t\t};\n\t\t}\n\n\t}\n\n\t/**\n\t * Removes the specified custom key binding.\n\t */\n\tremoveKeyBinding( keyCode ) {\n\n\t\tdelete this.bindings[keyCode];\n\n\t}\n\n\t/**\n\t * Programmatically triggers a keyboard event\n\t *\n\t * @param {int} keyCode\n\t */\n\ttriggerKey( keyCode ) {\n\n\t\tthis.onDocumentKeyDown( { keyCode } );\n\n\t}\n\n\t/**\n\t * Registers a new shortcut to include in the help overlay\n\t *\n\t * @param {String} key\n\t * @param {String} value\n\t */\n\tregisterKeyboardShortcut( key, value ) {\n\n\t\tthis.shortcuts[key] = value;\n\n\t}\n\n\tgetShortcuts() {\n\n\t\treturn this.shortcuts;\n\n\t}\n\n\tgetBindings() {\n\n\t\treturn this.bindings;\n\n\t}\n\n\t/**\n\t * Handler for the document level 'keydown' event.\n\t *\n\t * @param {object} event\n\t */\n\tonDocumentKeyDown( event ) {\n\n\t\tlet config = this.Reveal.getConfig();\n\n\t\t// If there's a condition specified and it returns false,\n\t\t// ignore this event\n\t\tif( typeof config.keyboardCondition === 'function' && config.keyboardCondition(event) === false ) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// If keyboardCondition is set, only capture keyboard events\n\t\t// for embedded decks when they are focused\n\t\tif( config.keyboardCondition === 'focused' && !this.Reveal.isFocused() ) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Shorthand\n\t\tlet keyCode = event.keyCode;\n\n\t\t// Remember if auto-sliding was paused so we can toggle it\n\t\tlet autoSlideWasPaused = !this.Reveal.isAutoSliding();\n\n\t\tthis.Reveal.onUserInput( event );\n\n\t\t// Is there a focused element that could be using the keyboard?\n\t\tlet activeElementIsCE = document.activeElement && document.activeElement.isContentEditable === true;\n\t\tlet activeElementIsInput = document.activeElement && document.activeElement.tagName && /input|textarea/i.test( document.activeElement.tagName );\n\t\tlet activeElementIsNotes = document.activeElement && document.activeElement.className && /speaker-notes/i.test( document.activeElement.className);\n\n\t\t// Whitelist certain modifiers for slide navigation shortcuts\n\t\tlet keyCodeUsesModifier = [32, 37, 38, 39, 40, 63, 78, 80, 191].indexOf( event.keyCode ) !== -1;\n\n\t\t// Prevent all other events when a modifier is pressed\n\t\tlet unusedModifier = \t!( keyCodeUsesModifier && event.shiftKey || event.altKey ) &&\n\t\t\t\t\t\t\t\t( event.shiftKey || event.altKey || event.ctrlKey || event.metaKey );\n\n\t\t// Disregard the event if there's a focused element or a\n\t\t// keyboard modifier key is present\n\t\tif( activeElementIsCE || activeElementIsInput || activeElementIsNotes || unusedModifier ) return;\n\n\t\t// While paused only allow resume keyboard events; 'b', 'v', '.'\n\t\tlet resumeKeyCodes = [66,86,190,191,112];\n\t\tlet key;\n\n\t\t// Custom key bindings for togglePause should be able to resume\n\t\tif( typeof config.keyboard === 'object' ) {\n\t\t\tfor( key in config.keyboard ) {\n\t\t\t\tif( config.keyboard[key] === 'togglePause' ) {\n\t\t\t\t\tresumeKeyCodes.push( parseInt( key, 10 ) );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif( this.Reveal.isPaused() && resumeKeyCodes.indexOf( keyCode ) === -1 ) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Use linear navigation if we're configured to OR if\n\t\t// the presentation is one-dimensional\n\t\tlet useLinearMode = config.navigationMode === 'linear' || !this.Reveal.hasHorizontalSlides() || !this.Reveal.hasVerticalSlides();\n\n\t\tlet triggered = false;\n\n\t\t// 1. User defined key bindings\n\t\tif( typeof config.keyboard === 'object' ) {\n\n\t\t\tfor( key in config.keyboard ) {\n\n\t\t\t\t// Check if this binding matches the pressed key\n\t\t\t\tif( parseInt( key, 10 ) === keyCode ) {\n\n\t\t\t\t\tlet value = config.keyboard[ key ];\n\n\t\t\t\t\t// Callback function\n\t\t\t\t\tif( typeof value === 'function' ) {\n\t\t\t\t\t\tvalue.apply( null, [ event ] );\n\t\t\t\t\t}\n\t\t\t\t\t// String shortcuts to reveal.js API\n\t\t\t\t\telse if( typeof value === 'string' && typeof this.Reveal[ value ] === 'function' ) {\n\t\t\t\t\t\tthis.Reveal[ value ].call();\n\t\t\t\t\t}\n\n\t\t\t\t\ttriggered = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// 2. Registered custom key bindings\n\t\tif( triggered === false ) {\n\n\t\t\tfor( key in this.bindings ) {\n\n\t\t\t\t// Check if this binding matches the pressed key\n\t\t\t\tif( parseInt( key, 10 ) === keyCode ) {\n\n\t\t\t\t\tlet action = this.bindings[ key ].callback;\n\n\t\t\t\t\t// Callback function\n\t\t\t\t\tif( typeof action === 'function' ) {\n\t\t\t\t\t\taction.apply( null, [ event ] );\n\t\t\t\t\t}\n\t\t\t\t\t// String shortcuts to reveal.js API\n\t\t\t\t\telse if( typeof action === 'string' && typeof this.Reveal[ action ] === 'function' ) {\n\t\t\t\t\t\tthis.Reveal[ action ].call();\n\t\t\t\t\t}\n\n\t\t\t\t\ttriggered = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// 3. System defined key bindings\n\t\tif( triggered === false ) {\n\n\t\t\t// Assume true and try to prove false\n\t\t\ttriggered = true;\n\n\t\t\t// P, PAGE UP\n\t\t\tif( keyCode === 80 || keyCode === 33 ) {\n\t\t\t\tthis.Reveal.prev({skipFragments: event.altKey});\n\t\t\t}\n\t\t\t// N, PAGE DOWN\n\t\t\telse if( keyCode === 78 || keyCode === 34 ) {\n\t\t\t\tthis.Reveal.next({skipFragments: event.altKey});\n\t\t\t}\n\t\t\t// H, LEFT\n\t\t\telse if( keyCode === 72 || keyCode === 37 ) {\n\t\t\t\tif( event.shiftKey ) {\n\t\t\t\t\tthis.Reveal.slide( 0 );\n\t\t\t\t}\n\t\t\t\telse if( !this.Reveal.overview.isActive() && useLinearMode ) {\n\t\t\t\t\tif( config.rtl ) {\n\t\t\t\t\t\tthis.Reveal.next({skipFragments: event.altKey});\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.prev({skipFragments: event.altKey});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.Reveal.left({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// L, RIGHT\n\t\t\telse if( keyCode === 76 || keyCode === 39 ) {\n\t\t\t\tif( event.shiftKey ) {\n\t\t\t\t\tthis.Reveal.slide( this.Reveal.getHorizontalSlides().length - 1 );\n\t\t\t\t}\n\t\t\t\telse if( !this.Reveal.overview.isActive() && useLinearMode ) {\n\t\t\t\t\tif( config.rtl ) {\n\t\t\t\t\t\tthis.Reveal.prev({skipFragments: event.altKey});\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.next({skipFragments: event.altKey});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.Reveal.right({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// K, UP\n\t\t\telse if( keyCode === 75 || keyCode === 38 ) {\n\t\t\t\tif( event.shiftKey ) {\n\t\t\t\t\tthis.Reveal.slide( undefined, 0 );\n\t\t\t\t}\n\t\t\t\telse if( !this.Reveal.overview.isActive() && useLinearMode ) {\n\t\t\t\t\tthis.Reveal.prev({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.Reveal.up({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// J, DOWN\n\t\t\telse if( keyCode === 74 || keyCode === 40 ) {\n\t\t\t\tif( event.shiftKey ) {\n\t\t\t\t\tthis.Reveal.slide( undefined, Number.MAX_VALUE );\n\t\t\t\t}\n\t\t\t\telse if( !this.Reveal.overview.isActive() && useLinearMode ) {\n\t\t\t\t\tthis.Reveal.next({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.Reveal.down({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// HOME\n\t\t\telse if( keyCode === 36 ) {\n\t\t\t\tthis.Reveal.slide( 0 );\n\t\t\t}\n\t\t\t// END\n\t\t\telse if( keyCode === 35 ) {\n\t\t\t\tthis.Reveal.slide( this.Reveal.getHorizontalSlides().length - 1 );\n\t\t\t}\n\t\t\t// SPACE\n\t\t\telse if( keyCode === 32 ) {\n\t\t\t\tif( this.Reveal.overview.isActive() ) {\n\t\t\t\t\tthis.Reveal.overview.deactivate();\n\t\t\t\t}\n\t\t\t\tif( event.shiftKey ) {\n\t\t\t\t\tthis.Reveal.prev({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.Reveal.next({skipFragments: event.altKey});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// TWO-SPOT, SEMICOLON, B, V, PERIOD, LOGITECH PRESENTER TOOLS \"BLACK SCREEN\" BUTTON\n\t\t\telse if( [58, 59, 66, 86, 190].includes( keyCode ) || ( keyCode === 191 && !event.shiftKey ) ) {\n\t\t\t\tthis.Reveal.togglePause();\n\t\t\t}\n\t\t\t// F\n\t\t\telse if( keyCode === 70 ) {\n\t\t\t\tenterFullscreen( config.embedded ? this.Reveal.getViewportElement() : document.documentElement );\n\t\t\t}\n\t\t\t// A\n\t\t\telse if( keyCode === 65 ) {\n\t\t\t\tif( config.autoSlideStoppable ) {\n\t\t\t\t\tthis.Reveal.toggleAutoSlide( autoSlideWasPaused );\n\t\t\t\t}\n\t\t\t}\n\t\t\t// G\n\t\t\telse if( keyCode === 71 ) {\n\t\t\t\tif( config.jumpToSlide ) {\n\t\t\t\t\tthis.Reveal.toggleJumpToSlide();\n\t\t\t\t}\n\t\t\t}\n\t\t\t// ?\n\t\t\telse if( ( keyCode === 63 || keyCode === 191 ) && event.shiftKey ) {\n\t\t\t\tthis.Reveal.toggleHelp();\n\t\t\t}\n\t\t\t// F1\n\t\t\telse if( keyCode === 112 ) {\n\t\t\t\tthis.Reveal.toggleHelp();\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttriggered = false;\n\t\t\t}\n\n\t\t}\n\n\t\t// If the input resulted in a triggered action we should prevent\n\t\t// the browsers default behavior\n\t\tif( triggered ) {\n\t\t\tevent.preventDefault && event.preventDefault();\n\t\t}\n\t\t// ESC or O key\n\t\telse if( keyCode === 27 || keyCode === 79 ) {\n\t\t\tif( this.Reveal.closeOverlay() === false ) {\n\t\t\t\tthis.Reveal.overview.toggle();\n\t\t\t}\n\n\t\t\tevent.preventDefault && event.preventDefault();\n\t\t}\n\n\t\t// If auto-sliding is enabled we need to cue up\n\t\t// another timeout\n\t\tthis.Reveal.cueAutoSlide();\n\n\t}\n\n}\n", "/**\n * Reads and writes the URL based on reveal.js' current state.\n */\nexport default class Location {\n\n\t// The minimum number of milliseconds that must pass between\n\t// calls to history.replaceState\n\tMAX_REPLACE_STATE_FREQUENCY = 1000\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\t// Delays updates to the URL due to a Chrome thumbnailer bug\n\t\tthis.writeURLTimeout = 0;\n\n\t\tthis.replaceStateTimestamp = 0;\n\n\t\tthis.onWindowHashChange = this.onWindowHashChange.bind( this );\n\n\t}\n\n\tbind() {\n\n\t\twindow.addEventListener( 'hashchange', this.onWindowHashChange, false );\n\n\t}\n\n\tunbind() {\n\n\t\twindow.removeEventListener( 'hashchange', this.onWindowHashChange, false );\n\n\t}\n\n\t/**\n\t * Returns the slide indices for the given hash link.\n\t *\n\t * @param {string} [hash] the hash string that we want to\n\t * find the indices for\n\t *\n\t * @returns slide indices or null\n\t */\n\tgetIndicesFromHash( hash=window.location.hash, options={} ) {\n\n\t\t// Attempt to parse the hash as either an index or name\n\t\tlet name = hash.replace( /^#\\/?/, '' );\n\t\tlet bits = name.split( '/' );\n\n\t\t// If the first bit is not fully numeric and there is a name we\n\t\t// can assume that this is a named link\n\t\tif( !/^[0-9]*$/.test( bits[0] ) && name.length ) {\n\t\t\tlet slide;\n\n\t\t\tlet f;\n\n\t\t\t// Parse named links with fragments (#/named-link/2)\n\t\t\tif( /\\/[-\\d]+$/g.test( name ) ) {\n\t\t\t\tf = parseInt( name.split( '/' ).pop(), 10 );\n\t\t\t\tf = isNaN(f) ? undefined : f;\n\t\t\t\tname = name.split( '/' ).shift();\n\t\t\t}\n\n\t\t\t// Ensure the named link is a valid HTML ID attribute\n\t\t\ttry {\n\t\t\t\tslide = document\n\t\t\t\t\t.getElementById( decodeURIComponent( name ) )\n\t\t\t\t\t.closest('.slides section');\n\t\t\t}\n\t\t\tcatch ( error ) { }\n\n\t\t\tif( slide ) {\n\t\t\t\treturn { ...this.Reveal.getIndices( slide ), f };\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tconst config = this.Reveal.getConfig();\n\t\t\tlet hashIndexBase = config.hashOneBasedIndex || options.oneBasedIndex ? 1 : 0;\n\n\t\t\t// Read the index components of the hash\n\t\t\tlet h = ( parseInt( bits[0], 10 ) - hashIndexBase ) || 0,\n\t\t\t\tv = ( parseInt( bits[1], 10 ) - hashIndexBase ) || 0,\n\t\t\t\tf;\n\n\t\t\tif( config.fragmentInURL ) {\n\t\t\t\tf = parseInt( bits[2], 10 );\n\t\t\t\tif( isNaN( f ) ) {\n\t\t\t\t\tf = undefined;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn { h, v, f };\n\t\t}\n\n\t\t// The hash couldn't be parsed or no matching named link was found\n\t\treturn null\n\n\t}\n\n\t/**\n\t * Reads the current URL (hash) and navigates accordingly.\n\t */\n\treadURL() {\n\n\t\tconst currentIndices = this.Reveal.getIndices();\n\t\tconst newIndices = this.getIndicesFromHash();\n\n\t\tif( newIndices ) {\n\t\t\tif( ( newIndices.h !== currentIndices.h || newIndices.v !== currentIndices.v || newIndices.f !== undefined ) ) {\n\t\t\t\t\tthis.Reveal.slide( newIndices.h, newIndices.v, newIndices.f );\n\t\t\t}\n\t\t}\n\t\t// If no new indices are available, we're trying to navigate to\n\t\t// a slide hash that does not exist\n\t\telse {\n\t\t\tthis.Reveal.slide( currentIndices.h || 0, currentIndices.v || 0 );\n\t\t}\n\n\t}\n\n\t/**\n\t * Updates the page URL (hash) to reflect the current\n\t * state.\n\t *\n\t * @param {number} delay The time in ms to wait before\n\t * writing the hash\n\t */\n\twriteURL( delay ) {\n\n\t\tlet config = this.Reveal.getConfig();\n\t\tlet currentSlide = this.Reveal.getCurrentSlide();\n\n\t\t// Make sure there's never more than one timeout running\n\t\tclearTimeout( this.writeURLTimeout );\n\n\t\t// If a delay is specified, timeout this call\n\t\tif( typeof delay === 'number' ) {\n\t\t\tthis.writeURLTimeout = setTimeout( this.writeURL, delay );\n\t\t}\n\t\telse if( currentSlide ) {\n\n\t\t\tlet hash = this.getHash();\n\n\t\t\t// If we're configured to push to history OR the history\n\t\t\t// API is not available.\n\t\t\tif( config.history ) {\n\t\t\t\twindow.location.hash = hash;\n\t\t\t}\n\t\t\t// If we're configured to reflect the current slide in the\n\t\t\t// URL without pushing to history.\n\t\t\telse if( config.hash ) {\n\t\t\t\t// If the hash is empty, don't add it to the URL\n\t\t\t\tif( hash === '/' ) {\n\t\t\t\t\tthis.debouncedReplaceState( window.location.pathname + window.location.search );\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.debouncedReplaceState( '#' + hash );\n\t\t\t\t}\n\t\t\t}\n\t\t\t// UPDATE: The below nuking of all hash changes breaks\n\t\t\t// anchors on pages where reveal.js is running. Removed\n\t\t\t// in 4.0. Why was it here in the first place? ¯\\_(ツ)_/¯\n\t\t\t//\n\t\t\t// If history and hash are both disabled, a hash may still\n\t\t\t// be added to the URL by clicking on a href with a hash\n\t\t\t// target. Counter this by always removing the hash.\n\t\t\t// else {\n\t\t\t// \twindow.history.replaceState( null, null, window.location.pathname + window.location.search );\n\t\t\t// }\n\n\t\t}\n\n\t}\n\n\treplaceState( url ) {\n\n\t\twindow.history.replaceState( null, null, url );\n\t\tthis.replaceStateTimestamp = Date.now();\n\n\t}\n\n\tdebouncedReplaceState( url ) {\n\n\t\tclearTimeout( this.replaceStateTimeout );\n\n\t\tif( Date.now() - this.replaceStateTimestamp > this.MAX_REPLACE_STATE_FREQUENCY ) {\n\t\t\tthis.replaceState( url );\n\t\t}\n\t\telse {\n\t\t\tthis.replaceStateTimeout = setTimeout( () => this.replaceState( url ), this.MAX_REPLACE_STATE_FREQUENCY );\n\t\t}\n\n\t}\n\n\t/**\n\t * Return a hash URL that will resolve to the given slide location.\n\t *\n\t * @param {HTMLElement} [slide=currentSlide] The slide to link to\n\t */\n\tgetHash( slide ) {\n\n\t\tlet url = '/';\n\n\t\t// Attempt to create a named link based on the slide's ID\n\t\tlet s = slide || this.Reveal.getCurrentSlide();\n\t\tlet id = s ? s.getAttribute( 'id' ) : null;\n\t\tif( id ) {\n\t\t\tid = encodeURIComponent( id );\n\t\t}\n\n\t\tlet index = this.Reveal.getIndices( slide );\n\t\tif( !this.Reveal.getConfig().fragmentInURL ) {\n\t\t\tindex.f = undefined;\n\t\t}\n\n\t\t// If the current slide has an ID, use that as a named link,\n\t\t// but we don't support named links with a fragment index\n\t\tif( typeof id === 'string' && id.length ) {\n\t\t\turl = '/' + id;\n\n\t\t\t// If there is also a fragment, append that at the end\n\t\t\t// of the named link, like: #/named-link/2\n\t\t\tif( index.f >= 0 ) url += '/' + index.f;\n\t\t}\n\t\t// Otherwise use the /h/v index\n\t\telse {\n\t\t\tlet hashIndexBase = this.Reveal.getConfig().hashOneBasedIndex ? 1 : 0;\n\t\t\tif( index.h > 0 || index.v > 0 || index.f >= 0 ) url += index.h + hashIndexBase;\n\t\t\tif( index.v > 0 || index.f >= 0 ) url += '/' + (index.v + hashIndexBase );\n\t\t\tif( index.f >= 0 ) url += '/' + index.f;\n\t\t}\n\n\t\treturn url;\n\n\t}\n\n\t/**\n\t * Handler for the window level 'hashchange' event.\n\t *\n\t * @param {object} [event]\n\t */\n\tonWindowHashChange( event ) {\n\n\t\tthis.readURL();\n\n\t}\n\n}", "import { queryAll, enterFullscreen } from '../utils/util.js'\nimport { isAndroid } from '../utils/device.js'\n\n/**\n * Manages our presentation controls. This includes both\n * the built-in control arrows as well as event monitoring\n * of any elements within the presentation with either of the\n * following helper classes:\n * - .navigate-up\n * - .navigate-right\n * - .navigate-down\n * - .navigate-left\n * - .navigate-next\n * - .navigate-prev\n * - .enter-fullscreen\n */\nexport default class Controls {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.onNavigateLeftClicked = this.onNavigateLeftClicked.bind( this );\n\t\tthis.onNavigateRightClicked = this.onNavigateRightClicked.bind( this );\n\t\tthis.onNavigateUpClicked = this.onNavigateUpClicked.bind( this );\n\t\tthis.onNavigateDownClicked = this.onNavigateDownClicked.bind( this );\n\t\tthis.onNavigatePrevClicked = this.onNavigatePrevClicked.bind( this );\n\t\tthis.onNavigateNextClicked = this.onNavigateNextClicked.bind( this );\n\t\tthis.onEnterFullscreen = this.onEnterFullscreen.bind( this );\n\n\t}\n\n\trender() {\n\n\t\tconst rtl = this.Reveal.getConfig().rtl;\n\t\tconst revealElement = this.Reveal.getRevealElement();\n\n\t\tthis.element = document.createElement( 'aside' );\n\t\tthis.element.className = 'controls';\n\t\tthis.element.innerHTML =\n\t\t\t`<button class=\"navigate-left\" aria-label=\"${ rtl ? 'next slide' : 'previous slide' }\"><div class=\"controls-arrow\"></div></button>\n\t\t\t<button class=\"navigate-right\" aria-label=\"${ rtl ? 'previous slide' : 'next slide' }\"><div class=\"controls-arrow\"></div></button>\n\t\t\t<button class=\"navigate-up\" aria-label=\"above slide\"><div class=\"controls-arrow\"></div></button>\n\t\t\t<button class=\"navigate-down\" aria-label=\"below slide\"><div class=\"controls-arrow\"></div></button>`;\n\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\n\t\t// There can be multiple instances of controls throughout the page\n\t\tthis.controlsLeft = queryAll( revealElement, '.navigate-left' );\n\t\tthis.controlsRight = queryAll( revealElement, '.navigate-right' );\n\t\tthis.controlsUp = queryAll( revealElement, '.navigate-up' );\n\t\tthis.controlsDown = queryAll( revealElement, '.navigate-down' );\n\t\tthis.controlsPrev = queryAll( revealElement, '.navigate-prev' );\n\t\tthis.controlsNext = queryAll( revealElement, '.navigate-next' );\n\t\tthis.controlsFullscreen = queryAll( revealElement, '.enter-fullscreen' );\n\n\t\t// The left, right and down arrows in the standard reveal.js controls\n\t\tthis.controlsRightArrow = this.element.querySelector( '.navigate-right' );\n\t\tthis.controlsLeftArrow = this.element.querySelector( '.navigate-left' );\n\t\tthis.controlsDownArrow = this.element.querySelector( '.navigate-down' );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tthis.element.style.display = config.controls ? 'block' : 'none';\n\n\t\tthis.element.setAttribute( 'data-controls-layout', config.controlsLayout );\n\t\tthis.element.setAttribute( 'data-controls-back-arrows', config.controlsBackArrows );\n\n\t}\n\n\tbind() {\n\n\t\t// Listen to both touch and click events, in case the device\n\t\t// supports both\n\t\tlet pointerEvents = [ 'touchstart', 'click' ];\n\n\t\t// Only support touch for Android, fixes double navigations in\n\t\t// stock browser\n\t\tif( isAndroid ) {\n\t\t\tpointerEvents = [ 'touchstart' ];\n\t\t}\n\n\t\tpointerEvents.forEach( eventName => {\n\t\t\tthis.controlsLeft.forEach( el => el.addEventListener( eventName, this.onNavigateLeftClicked, false ) );\n\t\t\tthis.controlsRight.forEach( el => el.addEventListener( eventName, this.onNavigateRightClicked, false ) );\n\t\t\tthis.controlsUp.forEach( el => el.addEventListener( eventName, this.onNavigateUpClicked, false ) );\n\t\t\tthis.controlsDown.forEach( el => el.addEventListener( eventName, this.onNavigateDownClicked, false ) );\n\t\t\tthis.controlsPrev.forEach( el => el.addEventListener( eventName, this.onNavigatePrevClicked, false ) );\n\t\t\tthis.controlsNext.forEach( el => el.addEventListener( eventName, this.onNavigateNextClicked, false ) );\n\t\t\tthis.controlsFullscreen.forEach( el => el.addEventListener( eventName, this.onEnterFullscreen, false ) );\n\t\t} );\n\n\t}\n\n\tunbind() {\n\n\t\t[ 'touchstart', 'click' ].forEach( eventName => {\n\t\t\tthis.controlsLeft.forEach( el => el.removeEventListener( eventName, this.onNavigateLeftClicked, false ) );\n\t\t\tthis.controlsRight.forEach( el => el.removeEventListener( eventName, this.onNavigateRightClicked, false ) );\n\t\t\tthis.controlsUp.forEach( el => el.removeEventListener( eventName, this.onNavigateUpClicked, false ) );\n\t\t\tthis.controlsDown.forEach( el => el.removeEventListener( eventName, this.onNavigateDownClicked, false ) );\n\t\t\tthis.controlsPrev.forEach( el => el.removeEventListener( eventName, this.onNavigatePrevClicked, false ) );\n\t\t\tthis.controlsNext.forEach( el => el.removeEventListener( eventName, this.onNavigateNextClicked, false ) );\n\t\t\tthis.controlsFullscreen.forEach( el => el.removeEventListener( eventName, this.onEnterFullscreen, false ) );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Updates the state of all control/navigation arrows.\n\t */\n\tupdate() {\n\n\t\tlet routes = this.Reveal.availableRoutes();\n\n\t\t// Remove the 'enabled' class from all directions\n\t\t[...this.controlsLeft, ...this.controlsRight, ...this.controlsUp, ...this.controlsDown, ...this.controlsPrev, ...this.controlsNext].forEach( node => {\n\t\t\tnode.classList.remove( 'enabled', 'fragmented' );\n\n\t\t\t// Set 'disabled' attribute on all directions\n\t\t\tnode.setAttribute( 'disabled', 'disabled' );\n\t\t} );\n\n\t\t// Add the 'enabled' class to the available routes; remove 'disabled' attribute to enable buttons\n\t\tif( routes.left ) this.controlsLeft.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\tif( routes.right ) this.controlsRight.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\tif( routes.up ) this.controlsUp.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\tif( routes.down ) this.controlsDown.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\n\t\t// Prev/next buttons\n\t\tif( routes.left || routes.up ) this.controlsPrev.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\tif( routes.right || routes.down ) this.controlsNext.forEach( el => { el.classList.add( 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\n\t\t// Highlight fragment directions\n\t\tlet currentSlide = this.Reveal.getCurrentSlide();\n\t\tif( currentSlide ) {\n\n\t\t\tlet fragmentsRoutes = this.Reveal.fragments.availableRoutes();\n\n\t\t\t// Always apply fragment decorator to prev/next buttons\n\t\t\tif( fragmentsRoutes.prev ) this.controlsPrev.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\t\tif( fragmentsRoutes.next ) this.controlsNext.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\n\t\t\t// Apply fragment decorators to directional buttons based on\n\t\t\t// what slide axis they are in\n\t\t\tif( this.Reveal.isVerticalSlide( currentSlide ) ) {\n\t\t\t\tif( fragmentsRoutes.prev ) this.controlsUp.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\t\t\tif( fragmentsRoutes.next ) this.controlsDown.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif( fragmentsRoutes.prev ) this.controlsLeft.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\t\t\tif( fragmentsRoutes.next ) this.controlsRight.forEach( el => { el.classList.add( 'fragmented', 'enabled' ); el.removeAttribute( 'disabled' ); } );\n\t\t\t}\n\n\t\t}\n\n\t\tif( this.Reveal.getConfig().controlsTutorial ) {\n\n\t\t\tlet indices = this.Reveal.getIndices();\n\n\t\t\t// Highlight control arrows with an animation to ensure\n\t\t\t// that the viewer knows how to navigate\n\t\t\tif( !this.Reveal.hasNavigatedVertically() && routes.down ) {\n\t\t\t\tthis.controlsDownArrow.classList.add( 'highlight' );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.controlsDownArrow.classList.remove( 'highlight' );\n\n\t\t\t\tif( this.Reveal.getConfig().rtl ) {\n\n\t\t\t\t\tif( !this.Reveal.hasNavigatedHorizontally() && routes.left && indices.v === 0 ) {\n\t\t\t\t\t\tthis.controlsLeftArrow.classList.add( 'highlight' );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.controlsLeftArrow.classList.remove( 'highlight' );\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif( !this.Reveal.hasNavigatedHorizontally() && routes.right && indices.v === 0 ) {\n\t\t\t\t\t\tthis.controlsRightArrow.classList.add( 'highlight' );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.controlsRightArrow.classList.remove( 'highlight' );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tdestroy() {\n\n\t\tthis.unbind();\n\t\tthis.element.remove();\n\n\t}\n\n\t/**\n\t * Event handlers for navigation control buttons.\n\t */\n\tonNavigateLeftClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tif( this.Reveal.getConfig().navigationMode === 'linear' ) {\n\t\t\tthis.Reveal.prev();\n\t\t}\n\t\telse {\n\t\t\tthis.Reveal.left();\n\t\t}\n\n\t}\n\n\tonNavigateRightClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tif( this.Reveal.getConfig().navigationMode === 'linear' ) {\n\t\t\tthis.Reveal.next();\n\t\t}\n\t\telse {\n\t\t\tthis.Reveal.right();\n\t\t}\n\n\t}\n\n\tonNavigateUpClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tthis.Reveal.up();\n\n\t}\n\n\tonNavigateDownClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tthis.Reveal.down();\n\n\t}\n\n\tonNavigatePrevClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tthis.Reveal.prev();\n\n\t}\n\n\tonNavigateNextClicked( event ) {\n\n\t\tevent.preventDefault();\n\t\tthis.Reveal.onUserInput();\n\n\t\tthis.Reveal.next();\n\n\t}\n\n\tonEnterFullscreen( event ) {\n\n\t\tconst config = this.Reveal.getConfig();\n\t\tconst viewport = this.Reveal.getViewportElement();\n\n\t\tenterFullscreen( config.embedded ? viewport : viewport.parentElement );\n\n\t}\n\n}", "/**\n * Creates a visual progress bar for the presentation.\n */\nexport default class Progress {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.onProgressClicked = this.onProgressClicked.bind( this );\n\n\t}\n\n\trender() {\n\n\t\tthis.element = document.createElement( 'div' );\n\t\tthis.element.className = 'progress';\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\n\t\tthis.bar = document.createElement( 'span' );\n\t\tthis.element.appendChild( this.bar );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tthis.element.style.display = config.progress ? 'block' : 'none';\n\n\t}\n\n\tbind() {\n\n\t\tif( this.Reveal.getConfig().progress && this.element ) {\n\t\t\tthis.element.addEventListener( 'click', this.onProgressClicked, false );\n\t\t}\n\n\t}\n\n\tunbind() {\n\n\t\tif ( this.Reveal.getConfig().progress && this.element ) {\n\t\t\tthis.element.removeEventListener( 'click', this.onProgressClicked, false );\n\t\t}\n\n\t}\n\n\t/**\n\t * Updates the progress bar to reflect the current slide.\n\t */\n\tupdate() {\n\n\t\t// Update progress if enabled\n\t\tif( this.Reveal.getConfig().progress && this.bar ) {\n\n\t\t\tlet scale = this.Reveal.getProgress();\n\n\t\t\t// Don't fill the progress bar if there's only one slide\n\t\t\tif( this.Reveal.getTotalSlides() < 2 ) {\n\t\t\t\tscale = 0;\n\t\t\t}\n\n\t\t\tthis.bar.style.transform = 'scaleX('+ scale +')';\n\n\t\t}\n\n\t}\n\n\tgetMaxWidth() {\n\n\t\treturn this.Reveal.getRevealElement().offsetWidth;\n\n\t}\n\n\t/**\n\t * Clicking on the progress bar results in a navigation to the\n\t * closest approximate horizontal slide using this equation:\n\t *\n\t * ( clickX / presentationWidth ) * numberOfSlides\n\t *\n\t * @param {object} event\n\t */\n\tonProgressClicked( event ) {\n\n\t\tthis.Reveal.onUserInput( event );\n\n\t\tevent.preventDefault();\n\n\t\tlet slides = this.Reveal.getSlides();\n\t\tlet slidesTotal = slides.length;\n\t\tlet slideIndex = Math.floor( ( event.clientX / this.getMaxWidth() ) * slidesTotal );\n\n\t\tif( this.Reveal.getConfig().rtl ) {\n\t\t\tslideIndex = slidesTotal - slideIndex;\n\t\t}\n\n\t\tlet targetIndices = this.Reveal.getIndices(slides[slideIndex]);\n\t\tthis.Reveal.slide( targetIndices.h, targetIndices.v );\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.element.remove();\n\n\t}\n\n}", "/**\n * Handles hiding of the pointer/cursor when inactive.\n */\nexport default class Pointer {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\t// Throttles mouse wheel navigation\n\t\tthis.lastMouseWheelStep = 0;\n\n\t\t// Is the mouse pointer currently hidden from view\n\t\tthis.cursorHidden = false;\n\n\t\t// Timeout used to determine when the cursor is inactive\n\t\tthis.cursorInactiveTimeout = 0;\n\n\t\tthis.onDocumentCursorActive = this.onDocumentCursorActive.bind( this );\n\t\tthis.onDocumentMouseScroll = this.onDocumentMouseScroll.bind( this );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tif( config.mouseWheel ) {\n\t\t\tdocument.addEventListener( 'wheel', this.onDocumentMouseScroll, false );\n\t\t}\n\t\telse {\n\t\t\tdocument.removeEventListener( 'wheel', this.onDocumentMouseScroll, false );\n\t\t}\n\n\t\t// Auto-hide the mouse pointer when its inactive\n\t\tif( config.hideInactiveCursor ) {\n\t\t\tdocument.addEventListener( 'mousemove', this.onDocumentCursorActive, false );\n\t\t\tdocument.addEventListener( 'mousedown', this.onDocumentCursorActive, false );\n\t\t}\n\t\telse {\n\t\t\tthis.showCursor();\n\n\t\t\tdocument.removeEventListener( 'mousemove', this.onDocumentCursorActive, false );\n\t\t\tdocument.removeEventListener( 'mousedown', this.onDocumentCursorActive, false );\n\t\t}\n\n\t}\n\n\t/**\n\t * Shows the mouse pointer after it has been hidden with\n\t * #hideCursor.\n\t */\n\tshowCursor() {\n\n\t\tif( this.cursorHidden ) {\n\t\t\tthis.cursorHidden = false;\n\t\t\tthis.Reveal.getRevealElement().style.cursor = '';\n\t\t}\n\n\t}\n\n\t/**\n\t * Hides the mouse pointer when it's on top of the .reveal\n\t * container.\n\t */\n\thideCursor() {\n\n\t\tif( this.cursorHidden === false ) {\n\t\t\tthis.cursorHidden = true;\n\t\t\tthis.Reveal.getRevealElement().style.cursor = 'none';\n\t\t}\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.showCursor();\n\n\t\tdocument.removeEventListener( 'wheel', this.onDocumentMouseScroll, false );\n\t\tdocument.removeEventListener( 'mousemove', this.onDocumentCursorActive, false );\n\t\tdocument.removeEventListener( 'mousedown', this.onDocumentCursorActive, false );\n\n\t}\n\n\t/**\n\t * Called whenever there is mouse input at the document level\n\t * to determine if the cursor is active or not.\n\t *\n\t * @param {object} event\n\t */\n\tonDocumentCursorActive( event ) {\n\n\t\tthis.showCursor();\n\n\t\tclearTimeout( this.cursorInactiveTimeout );\n\n\t\tthis.cursorInactiveTimeout = setTimeout( this.hideCursor.bind( this ), this.Reveal.getConfig().hideCursorTime );\n\n\t}\n\n\t/**\n\t * Handles mouse wheel scrolling, throttled to avoid skipping\n\t * multiple slides.\n\t *\n\t * @param {object} event\n\t */\n\tonDocumentMouseScroll( event ) {\n\n\t\tif( Date.now() - this.lastMouseWheelStep > 1000 ) {\n\n\t\t\tthis.lastMouseWheelStep = Date.now();\n\n\t\t\tlet delta = event.detail || -event.wheelDelta;\n\t\t\tif( delta > 0 ) {\n\t\t\t\tthis.Reveal.next();\n\t\t\t}\n\t\t\telse if( delta < 0 ) {\n\t\t\t\tthis.Reveal.prev();\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n", "/**\n * Loads a JavaScript file from the given URL and executes it.\n *\n * @param {string} url Address of the .js file to load\n * @param {function} callback Method to invoke when the script\n * has loaded and executed\n */\nexport const loadScript = ( url, callback ) => {\n\n\tconst script = document.createElement( 'script' );\n\tscript.type = 'text/javascript';\n\tscript.async = false;\n\tscript.defer = false;\n\tscript.src = url;\n\n\tif( typeof callback === 'function' ) {\n\n\t\t// Success callback\n\t\tscript.onload = script.onreadystatechange = event => {\n\t\t\tif( event.type === 'load' || /loaded|complete/.test( script.readyState ) ) {\n\n\t\t\t\t// Kill event listeners\n\t\t\t\tscript.onload = script.onreadystatechange = script.onerror = null;\n\n\t\t\t\tcallback();\n\n\t\t\t}\n\t\t};\n\n\t\t// Error callback\n\t\tscript.onerror = err => {\n\n\t\t\t// Kill event listeners\n\t\t\tscript.onload = script.onreadystatechange = script.onerror = null;\n\n\t\t\tcallback( new Error( 'Failed loading script: ' + script.src + '\\n' + err ) );\n\n\t\t};\n\n\t}\n\n\t// Append the script at the end of <head>\n\tconst head = document.querySelector( 'head' );\n\thead.insertBefore( script, head.lastChild );\n\n}", "import { loadScript } from '../utils/loader.js'\n\n/**\n * Manages loading and registering of reveal.js plugins.\n */\nexport default class Plugins {\n\n\tconstructor( reveal ) {\n\n\t\tthis.Reveal = reveal;\n\n\t\t// Flags our current state (idle -> loading -> loaded)\n\t\tthis.state = 'idle';\n\n\t\t// An id:instance map of currently registered plugins\n\t\tthis.registeredPlugins = {};\n\n\t\tthis.asyncDependencies = [];\n\n\t}\n\n\t/**\n\t * Loads reveal.js dependencies, registers and\n\t * initializes plugins.\n\t *\n\t * Plugins are direct references to a reveal.js plugin\n\t * object that we register and initialize after any\n\t * synchronous dependencies have loaded.\n\t *\n\t * Dependencies are defined via the 'dependencies' config\n\t * option and will be loaded prior to starting reveal.js.\n\t * Some dependencies may have an 'async' flag, if so they\n\t * will load after reveal.js has been started up.\n\t */\n\tload( plugins, dependencies ) {\n\n\t\tthis.state = 'loading';\n\n\t\tplugins.forEach( this.registerPlugin.bind( this ) );\n\n\t\treturn new Promise( resolve => {\n\n\t\t\tlet scripts = [],\n\t\t\t\tscriptsToLoad = 0;\n\n\t\t\tdependencies.forEach( s => {\n\t\t\t\t// Load if there's no condition or the condition is truthy\n\t\t\t\tif( !s.condition || s.condition() ) {\n\t\t\t\t\tif( s.async ) {\n\t\t\t\t\t\tthis.asyncDependencies.push( s );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tscripts.push( s );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\tif( scripts.length ) {\n\t\t\t\tscriptsToLoad = scripts.length;\n\n\t\t\t\tconst scriptLoadedCallback = (s) => {\n\t\t\t\t\tif( s && typeof s.callback === 'function' ) s.callback();\n\n\t\t\t\t\tif( --scriptsToLoad === 0 ) {\n\t\t\t\t\t\tthis.initPlugins().then( resolve );\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\t// Load synchronous scripts\n\t\t\t\tscripts.forEach( s => {\n\t\t\t\t\tif( typeof s.id === 'string' ) {\n\t\t\t\t\t\tthis.registerPlugin( s );\n\t\t\t\t\t\tscriptLoadedCallback( s );\n\t\t\t\t\t}\n\t\t\t\t\telse if( typeof s.src === 'string' ) {\n\t\t\t\t\t\tloadScript( s.src, () => scriptLoadedCallback(s) );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tconsole.warn( 'Unrecognized plugin format', s );\n\t\t\t\t\t\tscriptLoadedCallback();\n\t\t\t\t\t}\n\t\t\t\t} );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.initPlugins().then( resolve );\n\t\t\t}\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Initializes our plugins and waits for them to be ready\n\t * before proceeding.\n\t */\n\tinitPlugins() {\n\n\t\treturn new Promise( resolve => {\n\n\t\t\tlet pluginValues = Object.values( this.registeredPlugins );\n\t\t\tlet pluginsToInitialize = pluginValues.length;\n\n\t\t\t// If there are no plugins, skip this step\n\t\t\tif( pluginsToInitialize === 0 ) {\n\t\t\t\tthis.loadAsync().then( resolve );\n\t\t\t}\n\t\t\t// ... otherwise initialize plugins\n\t\t\telse {\n\n\t\t\t\tlet initNextPlugin;\n\n\t\t\t\tlet afterPlugInitialized = () => {\n\t\t\t\t\tif( --pluginsToInitialize === 0 ) {\n\t\t\t\t\t\tthis.loadAsync().then( resolve );\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tinitNextPlugin();\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tlet i = 0;\n\n\t\t\t\t// Initialize plugins serially\n\t\t\t\tinitNextPlugin = () => {\n\n\t\t\t\t\tlet plugin = pluginValues[i++];\n\n\t\t\t\t\t// If the plugin has an 'init' method, invoke it\n\t\t\t\t\tif( typeof plugin.init === 'function' ) {\n\t\t\t\t\t\tlet promise = plugin.init( this.Reveal );\n\n\t\t\t\t\t\t// If the plugin returned a Promise, wait for it\n\t\t\t\t\t\tif( promise && typeof promise.then === 'function' ) {\n\t\t\t\t\t\t\tpromise.then( afterPlugInitialized );\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tafterPlugInitialized();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tafterPlugInitialized();\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tinitNextPlugin();\n\n\t\t\t}\n\n\t\t} )\n\n\t}\n\n\t/**\n\t * Loads all async reveal.js dependencies.\n\t */\n\tloadAsync() {\n\n\t\tthis.state = 'loaded';\n\n\t\tif( this.asyncDependencies.length ) {\n\t\t\tthis.asyncDependencies.forEach( s => {\n\t\t\t\tloadScript( s.src, s.callback );\n\t\t\t} );\n\t\t}\n\n\t\treturn Promise.resolve();\n\n\t}\n\n\t/**\n\t * Registers a new plugin with this reveal.js instance.\n\t *\n\t * reveal.js waits for all registered plugins to initialize\n\t * before considering itself ready, as long as the plugin\n\t * is registered before calling `Reveal.initialize()`.\n\t */\n\tregisterPlugin( plugin ) {\n\n\t\t// Backwards compatibility to make reveal.js ~3.9.0\n\t\t// plugins work with reveal.js 4.0.0\n\t\tif( arguments.length === 2 && typeof arguments[0] === 'string' ) {\n\t\t\tplugin = arguments[1];\n\t\t\tplugin.id = arguments[0];\n\t\t}\n\t\t// Plugin can optionally be a function which we call\n\t\t// to create an instance of the plugin\n\t\telse if( typeof plugin === 'function' ) {\n\t\t\tplugin = plugin();\n\t\t}\n\n\t\tlet id = plugin.id;\n\n\t\tif( typeof id !== 'string' ) {\n\t\t\tconsole.warn( 'Unrecognized plugin format; can\\'t find plugin.id', plugin );\n\t\t}\n\t\telse if( this.registeredPlugins[id] === undefined ) {\n\t\t\tthis.registeredPlugins[id] = plugin;\n\n\t\t\t// If a plugin is registered after reveal.js is loaded,\n\t\t\t// initialize it right away\n\t\t\tif( this.state === 'loaded' && typeof plugin.init === 'function' ) {\n\t\t\t\tplugin.init( this.Reveal );\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tconsole.warn( 'reveal.js: \"'+ id +'\" plugin has already been registered' );\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if a specific plugin has been registered.\n\t *\n\t * @param {String} id Unique plugin identifier\n\t */\n\thasPlugin( id ) {\n\n\t\treturn !!this.registeredPlugins[id];\n\n\t}\n\n\t/**\n\t * Returns the specific plugin instance, if a plugin\n\t * with the given ID has been registered.\n\t *\n\t * @param {String} id Unique plugin identifier\n\t */\n\tgetPlugin( id ) {\n\n\t\treturn this.registeredPlugins[id];\n\n\t}\n\n\tgetRegisteredPlugins() {\n\n\t\treturn this.registeredPlugins;\n\n\t}\n\n\tdestroy() {\n\n\t\tObject.values( this.registeredPlugins ).forEach( plugin => {\n\t\t\tif( typeof plugin.destroy === 'function' ) {\n\t\t\t\tplugin.destroy();\n\t\t\t}\n\t\t} );\n\n\t\tthis.registeredPlugins = {};\n\t\tthis.asyncDependencies = [];\n\n\t}\n\n}\n", "import { isAndroid } from '../utils/device.js'\nimport { matches } from '../utils/util.js'\n\nconst SWIPE_THRESHOLD = 40;\n\n/**\n * Controls all touch interactions and navigations for\n * a presentation.\n */\nexport default class Touch {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\t// Holds information about the currently ongoing touch interaction\n\t\tthis.touchStartX = 0;\n\t\tthis.touchStartY = 0;\n\t\tthis.touchStartCount = 0;\n\t\tthis.touchCaptured = false;\n\n\t\tthis.onPointerDown = this.onPointerDown.bind( this );\n\t\tthis.onPointerMove = this.onPointerMove.bind( this );\n\t\tthis.onPointerUp = this.onPointerUp.bind( this );\n\t\tthis.onTouchStart = this.onTouchStart.bind( this );\n\t\tthis.onTouchMove = this.onTouchMove.bind( this );\n\t\tthis.onTouchEnd = this.onTouchEnd.bind( this );\n\n\t}\n\n\t/**\n\t *\n\t */\n\tbind() {\n\n\t\tlet revealElement = this.Reveal.getRevealElement();\n\n\t\tif( 'onpointerdown' in window ) {\n\t\t\t// Use W3C pointer events\n\t\t\trevealElement.addEventListener( 'pointerdown', this.onPointerDown, false );\n\t\t\trevealElement.addEventListener( 'pointermove', this.onPointerMove, false );\n\t\t\trevealElement.addEventListener( 'pointerup', this.onPointerUp, false );\n\t\t}\n\t\telse if( window.navigator.msPointerEnabled ) {\n\t\t\t// IE 10 uses prefixed version of pointer events\n\t\t\trevealElement.addEventListener( 'MSPointerDown', this.onPointerDown, false );\n\t\t\trevealElement.addEventListener( 'MSPointerMove', this.onPointerMove, false );\n\t\t\trevealElement.addEventListener( 'MSPointerUp', this.onPointerUp, false );\n\t\t}\n\t\telse {\n\t\t\t// Fall back to touch events\n\t\t\trevealElement.addEventListener( 'touchstart', this.onTouchStart, false );\n\t\t\trevealElement.addEventListener( 'touchmove', this.onTouchMove, false );\n\t\t\trevealElement.addEventListener( 'touchend', this.onTouchEnd, false );\n\t\t}\n\n\t}\n\n\t/**\n\t *\n\t */\n\tunbind() {\n\n\t\tlet revealElement = this.Reveal.getRevealElement();\n\n\t\trevealElement.removeEventListener( 'pointerdown', this.onPointerDown, false );\n\t\trevealElement.removeEventListener( 'pointermove', this.onPointerMove, false );\n\t\trevealElement.removeEventListener( 'pointerup', this.onPointerUp, false );\n\n\t\trevealElement.removeEventListener( 'MSPointerDown', this.onPointerDown, false );\n\t\trevealElement.removeEventListener( 'MSPointerMove', this.onPointerMove, false );\n\t\trevealElement.removeEventListener( 'MSPointerUp', this.onPointerUp, false );\n\n\t\trevealElement.removeEventListener( 'touchstart', this.onTouchStart, false );\n\t\trevealElement.removeEventListener( 'touchmove', this.onTouchMove, false );\n\t\trevealElement.removeEventListener( 'touchend', this.onTouchEnd, false );\n\n\t}\n\n\t/**\n\t * Checks if the target element prevents the triggering of\n\t * swipe navigation.\n\t */\n\tisSwipePrevented( target ) {\n\n\t\t// Prevent accidental swipes when scrubbing timelines\n\t\tif( matches( target, 'video[controls], audio[controls]' ) ) return true;\n\n\t\twhile( target && typeof target.hasAttribute === 'function' ) {\n\t\t\tif( target.hasAttribute( 'data-prevent-swipe' ) ) return true;\n\t\t\ttarget = target.parentNode;\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t/**\n\t * Handler for the 'touchstart' event, enables support for\n\t * swipe and pinch gestures.\n\t *\n\t * @param {object} event\n\t */\n\tonTouchStart( event ) {\n\n\t\tthis.touchCaptured = false;\n\n\t\tif( this.isSwipePrevented( event.target ) ) return true;\n\n\t\tthis.touchStartX = event.touches[0].clientX;\n\t\tthis.touchStartY = event.touches[0].clientY;\n\t\tthis.touchStartCount = event.touches.length;\n\n\t}\n\n\t/**\n\t * Handler for the 'touchmove' event.\n\t *\n\t * @param {object} event\n\t */\n\tonTouchMove( event ) {\n\n\t\tif( this.isSwipePrevented( event.target ) ) return true;\n\n\t\tlet config = this.Reveal.getConfig();\n\n\t\t// Each touch should only trigger one action\n\t\tif( !this.touchCaptured ) {\n\t\t\tthis.Reveal.onUserInput( event );\n\n\t\t\tlet currentX = event.touches[0].clientX;\n\t\t\tlet currentY = event.touches[0].clientY;\n\n\t\t\t// There was only one touch point, look for a swipe\n\t\t\tif( event.touches.length === 1 && this.touchStartCount !== 2 ) {\n\n\t\t\t\tlet availableRoutes = this.Reveal.availableRoutes({ includeFragments: true });\n\n\t\t\t\tlet deltaX = currentX - this.touchStartX,\n\t\t\t\t\tdeltaY = currentY - this.touchStartY;\n\n\t\t\t\tif( deltaX > SWIPE_THRESHOLD && Math.abs( deltaX ) > Math.abs( deltaY ) ) {\n\t\t\t\t\tthis.touchCaptured = true;\n\t\t\t\t\tif( config.navigationMode === 'linear' ) {\n\t\t\t\t\t\tif( config.rtl ) {\n\t\t\t\t\t\t\tthis.Reveal.next();\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.Reveal.prev();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.left();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse if( deltaX < -SWIPE_THRESHOLD && Math.abs( deltaX ) > Math.abs( deltaY ) ) {\n\t\t\t\t\tthis.touchCaptured = true;\n\t\t\t\t\tif( config.navigationMode === 'linear' ) {\n\t\t\t\t\t\tif( config.rtl ) {\n\t\t\t\t\t\t\tthis.Reveal.prev();\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.Reveal.next();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.right();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse if( deltaY > SWIPE_THRESHOLD && availableRoutes.up ) {\n\t\t\t\t\tthis.touchCaptured = true;\n\t\t\t\t\tif( config.navigationMode === 'linear' ) {\n\t\t\t\t\t\tthis.Reveal.prev();\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.up();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse if( deltaY < -SWIPE_THRESHOLD && availableRoutes.down ) {\n\t\t\t\t\tthis.touchCaptured = true;\n\t\t\t\t\tif( config.navigationMode === 'linear' ) {\n\t\t\t\t\t\tthis.Reveal.next();\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.Reveal.down();\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// If we're embedded, only block touch events if they have\n\t\t\t\t// triggered an action\n\t\t\t\tif( config.embedded ) {\n\t\t\t\t\tif( this.touchCaptured || this.Reveal.isVerticalSlide() ) {\n\t\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Not embedded? Block them all to avoid needless tossing\n\t\t\t\t// around of the viewport in iOS\n\t\t\t\telse {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\t\t// There's a bug with swiping on some Android devices unless\n\t\t// the default action is always prevented\n\t\telse if( isAndroid ) {\n\t\t\tevent.preventDefault();\n\t\t}\n\n\t}\n\n\t/**\n\t * Handler for the 'touchend' event.\n\t *\n\t * @param {object} event\n\t */\n\tonTouchEnd( event ) {\n\n\t\tthis.touchCaptured = false;\n\n\t}\n\n\t/**\n\t * Convert pointer down to touch start.\n\t *\n\t * @param {object} event\n\t */\n\tonPointerDown( event ) {\n\n\t\tif( event.pointerType === event.MSPOINTER_TYPE_TOUCH || event.pointerType === \"touch\" ) {\n\t\t\tevent.touches = [{ clientX: event.clientX, clientY: event.clientY }];\n\t\t\tthis.onTouchStart( event );\n\t\t}\n\n\t}\n\n\t/**\n\t * Convert pointer move to touch move.\n\t *\n\t * @param {object} event\n\t */\n\tonPointerMove( event ) {\n\n\t\tif( event.pointerType === event.MSPOINTER_TYPE_TOUCH || event.pointerType === \"touch\" )  {\n\t\t\tevent.touches = [{ clientX: event.clientX, clientY: event.clientY }];\n\t\t\tthis.onTouchMove( event );\n\t\t}\n\n\t}\n\n\t/**\n\t * Convert pointer up to touch end.\n\t *\n\t * @param {object} event\n\t */\n\tonPointerUp( event ) {\n\n\t\tif( event.pointerType === event.MSPOINTER_TYPE_TOUCH || event.pointerType === \"touch\" )  {\n\t\t\tevent.touches = [{ clientX: event.clientX, clientY: event.clientY }];\n\t\t\tthis.onTouchEnd( event );\n\t\t}\n\n\t}\n\n}", "import { closest } from '../utils/util.js'\n\n/**\n * Manages focus when a presentation is embedded. This\n * helps us only capture keyboard from the presentation\n * a user is currently interacting with in a page where\n * multiple presentations are embedded.\n */\n\nconst STATE_FOCUS = 'focus';\nconst STATE_BLUR = 'blur';\n\nexport default class Focus {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t\tthis.onRevealPointerDown = this.onRevealPointerDown.bind( this );\n\t\tthis.onDocumentPointerDown = this.onDocumentPointerDown.bind( this );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tif( config.embedded ) {\n\t\t\tthis.blur();\n\t\t}\n\t\telse {\n\t\t\tthis.focus();\n\t\t\tthis.unbind();\n\t\t}\n\n\t}\n\n\tbind() {\n\n\t\tif( this.Reveal.getConfig().embedded ) {\n\t\t\tthis.Reveal.getRevealElement().addEventListener( 'pointerdown', this.onRevealPointerDown, false );\n\t\t}\n\n\t}\n\n\tunbind() {\n\n\t\tthis.Reveal.getRevealElement().removeEventListener( 'pointerdown', this.onRevealPointerDown, false );\n\t\tdocument.removeEventListener( 'pointerdown', this.onDocumentPointerDown, false );\n\n\t}\n\n\tfocus() {\n\n\t\tif( this.state !== STATE_FOCUS ) {\n\t\t\tthis.Reveal.getRevealElement().classList.add( 'focused' );\n\t\t\tdocument.addEventListener( 'pointerdown', this.onDocumentPointerDown, false );\n\t\t}\n\n\t\tthis.state = STATE_FOCUS;\n\n\t}\n\n\tblur() {\n\n\t\tif( this.state !== STATE_BLUR ) {\n\t\t\tthis.Reveal.getRevealElement().classList.remove( 'focused' );\n\t\t\tdocument.removeEventListener( 'pointerdown', this.onDocumentPointerDown, false );\n\t\t}\n\n\t\tthis.state = STATE_BLUR;\n\n\t}\n\n\tisFocused() {\n\n\t\treturn this.state === STATE_FOCUS;\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.Reveal.getRevealElement().classList.remove( 'focused' );\n\n\t}\n\n\tonRevealPointerDown( event ) {\n\n\t\tthis.focus();\n\n\t}\n\n\tonDocumentPointerDown( event ) {\n\n\t\tlet revealElement = closest( event.target, '.reveal' );\n\t\tif( !revealElement || revealElement !== this.Reveal.getRevealElement() ) {\n\t\t\tthis.blur();\n\t\t}\n\n\t}\n\n}", "/**\n * Handles the showing of speaker notes\n */\nexport default class Notes {\n\n\tconstructor( Reveal ) {\n\n\t\tthis.Reveal = Reveal;\n\n\t}\n\n\trender() {\n\n\t\tthis.element = document.createElement( 'div' );\n\t\tthis.element.className = 'speaker-notes';\n\t\tthis.element.setAttribute( 'data-prevent-swipe', '' );\n\t\tthis.element.setAttribute( 'tabindex', '0' );\n\t\tthis.Reveal.getRevealElement().appendChild( this.element );\n\n\t}\n\n\t/**\n\t * Called when the reveal.js config is updated.\n\t */\n\tconfigure( config, oldConfig ) {\n\n\t\tif( config.showNotes ) {\n\t\t\tthis.element.setAttribute( 'data-layout', typeof config.showNotes === 'string' ? config.showNotes : 'inline' );\n\t\t}\n\n\t}\n\n\t/**\n\t * Pick up notes from the current slide and display them\n\t * to the viewer.\n\t *\n\t * @see {@link config.showNotes}\n\t */\n\tupdate() {\n\n\t\tif( this.Reveal.getConfig().showNotes &&\n\t\t\tthis.element && this.Reveal.getCurrentSlide() &&\n\t\t\t!this.Reveal.isScrollView() &&\n\t\t\t!this.Reveal.isPrintView()\n\t\t) {\n\t\t\tthis.element.innerHTML = this.getSlideNotes() || '<span class=\"notes-placeholder\">No notes on this slide.</span>';\n\t\t}\n\n\t}\n\n\t/**\n\t * Updates the visibility of the speaker notes sidebar that\n\t * is used to share annotated slides. The notes sidebar is\n\t * only visible if showNotes is true and there are notes on\n\t * one or more slides in the deck.\n\t */\n\tupdateVisibility() {\n\n\t\tif( this.Reveal.getConfig().showNotes &&\n\t\t\tthis.hasNotes() &&\n\t\t\t!this.Reveal.isScrollView() &&\n\t\t\t!this.Reveal.isPrintView()\n\t\t) {\n\t\t\tthis.Reveal.getRevealElement().classList.add( 'show-notes' );\n\t\t}\n\t\telse {\n\t\t\tthis.Reveal.getRevealElement().classList.remove( 'show-notes' );\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if there are speaker notes for ANY slide in the\n\t * presentation.\n\t */\n\thasNotes() {\n\n\t\treturn this.Reveal.getSlidesElement().querySelectorAll( '[data-notes], aside.notes' ).length > 0;\n\n\t}\n\n\t/**\n\t * Checks if this presentation is running inside of the\n\t * speaker notes window.\n\t *\n\t * @return {boolean}\n\t */\n\tisSpeakerNotesWindow() {\n\n\t\treturn !!window.location.search.match( /receiver/gi );\n\n\t}\n\n\t/**\n\t * Retrieves the speaker notes from a slide. Notes can be\n\t * defined in two ways:\n\t * 1. As a data-notes attribute on the slide <section>\n\t * 2. With <aside class=\"notes\"> elements inside the slide\n\t *\n\t * @param {HTMLElement} [slide=currentSlide]\n\t * @return {(string|null)}\n\t */\n\tgetSlideNotes( slide = this.Reveal.getCurrentSlide() ) {\n\n\t\t// Notes can be specified via the data-notes attribute...\n\t\tif( slide.hasAttribute( 'data-notes' ) ) {\n\t\t\treturn slide.getAttribute( 'data-notes' );\n\t\t}\n\n\t\t// ... or using <aside class=\"notes\"> elements\n\t\tlet notesElements = slide.querySelectorAll( 'aside.notes' );\n\t\tif( notesElements ) {\n\t\t\treturn Array.from(notesElements).map( notesElement => notesElement.innerHTML ).join( '\\n' );\n\t\t}\n\n\t\treturn null;\n\n\t}\n\n\tdestroy() {\n\n\t\tthis.element.remove();\n\n\t}\n\n}", "/**\n * UI component that lets the use control auto-slide\n * playback via play/pause.\n */\nexport default class Playback {\n\n\t/**\n\t * @param {HTMLElement} container The component will append\n\t * itself to this\n\t * @param {function} progressCheck A method which will be\n\t * called frequently to get the current playback progress on\n\t * a range of 0-1\n\t */\n\tconstructor( container, progressCheck ) {\n\n\t\t// Cosmetics\n\t\tthis.diameter = 100;\n\t\tthis.diameter2 = this.diameter/2;\n\t\tthis.thickness = 6;\n\n\t\t// Flags if we are currently playing\n\t\tthis.playing = false;\n\n\t\t// Current progress on a 0-1 range\n\t\tthis.progress = 0;\n\n\t\t// Used to loop the animation smoothly\n\t\tthis.progressOffset = 1;\n\n\t\tthis.container = container;\n\t\tthis.progressCheck = progressCheck;\n\n\t\tthis.canvas = document.createElement( 'canvas' );\n\t\tthis.canvas.className = 'playback';\n\t\tthis.canvas.width = this.diameter;\n\t\tthis.canvas.height = this.diameter;\n\t\tthis.canvas.style.width = this.diameter2 + 'px';\n\t\tthis.canvas.style.height = this.diameter2 + 'px';\n\t\tthis.context = this.canvas.getContext( '2d' );\n\n\t\tthis.container.appendChild( this.canvas );\n\n\t\tthis.render();\n\n\t}\n\n\tsetPlaying( value ) {\n\n\t\tconst wasPlaying = this.playing;\n\n\t\tthis.playing = value;\n\n\t\t// Start repainting if we weren't already\n\t\tif( !wasPlaying && this.playing ) {\n\t\t\tthis.animate();\n\t\t}\n\t\telse {\n\t\t\tthis.render();\n\t\t}\n\n\t}\n\n\tanimate() {\n\n\t\tconst progressBefore = this.progress;\n\n\t\tthis.progress = this.progressCheck();\n\n\t\t// When we loop, offset the progress so that it eases\n\t\t// smoothly rather than immediately resetting\n\t\tif( progressBefore > 0.8 && this.progress < 0.2 ) {\n\t\t\tthis.progressOffset = this.progress;\n\t\t}\n\n\t\tthis.render();\n\n\t\tif( this.playing ) {\n\t\t\trequestAnimationFrame( this.animate.bind( this ) );\n\t\t}\n\n\t}\n\n\t/**\n\t * Renders the current progress and playback state.\n\t */\n\trender() {\n\n\t\tlet progress = this.playing ? this.progress : 0,\n\t\t\tradius = ( this.diameter2 ) - this.thickness,\n\t\t\tx = this.diameter2,\n\t\t\ty = this.diameter2,\n\t\t\ticonSize = 28;\n\n\t\t// Ease towards 1\n\t\tthis.progressOffset += ( 1 - this.progressOffset ) * 0.1;\n\n\t\tconst endAngle = ( - Math.PI / 2 ) + ( progress * ( Math.PI * 2 ) );\n\t\tconst startAngle = ( - Math.PI / 2 ) + ( this.progressOffset * ( Math.PI * 2 ) );\n\n\t\tthis.context.save();\n\t\tthis.context.clearRect( 0, 0, this.diameter, this.diameter );\n\n\t\t// Solid background color\n\t\tthis.context.beginPath();\n\t\tthis.context.arc( x, y, radius + 4, 0, Math.PI * 2, false );\n\t\tthis.context.fillStyle = 'rgba( 0, 0, 0, 0.4 )';\n\t\tthis.context.fill();\n\n\t\t// Draw progress track\n\t\tthis.context.beginPath();\n\t\tthis.context.arc( x, y, radius, 0, Math.PI * 2, false );\n\t\tthis.context.lineWidth = this.thickness;\n\t\tthis.context.strokeStyle = 'rgba( 255, 255, 255, 0.2 )';\n\t\tthis.context.stroke();\n\n\t\tif( this.playing ) {\n\t\t\t// Draw progress on top of track\n\t\t\tthis.context.beginPath();\n\t\t\tthis.context.arc( x, y, radius, startAngle, endAngle, false );\n\t\t\tthis.context.lineWidth = this.thickness;\n\t\t\tthis.context.strokeStyle = '#fff';\n\t\t\tthis.context.stroke();\n\t\t}\n\n\t\tthis.context.translate( x - ( iconSize / 2 ), y - ( iconSize / 2 ) );\n\n\t\t// Draw play/pause icons\n\t\tif( this.playing ) {\n\t\t\tthis.context.fillStyle = '#fff';\n\t\t\tthis.context.fillRect( 0, 0, iconSize / 2 - 4, iconSize );\n\t\t\tthis.context.fillRect( iconSize / 2 + 4, 0, iconSize / 2 - 4, iconSize );\n\t\t}\n\t\telse {\n\t\t\tthis.context.beginPath();\n\t\t\tthis.context.translate( 4, 0 );\n\t\t\tthis.context.moveTo( 0, 0 );\n\t\t\tthis.context.lineTo( iconSize - 4, iconSize / 2 );\n\t\t\tthis.context.lineTo( 0, iconSize );\n\t\t\tthis.context.fillStyle = '#fff';\n\t\t\tthis.context.fill();\n\t\t}\n\n\t\tthis.context.restore();\n\n\t}\n\n\ton( type, listener ) {\n\t\tthis.canvas.addEventListener( type, listener, false );\n\t}\n\n\toff( type, listener ) {\n\t\tthis.canvas.removeEventListener( type, listener, false );\n\t}\n\n\tdestroy() {\n\n\t\tthis.playing = false;\n\n\t\tif( this.canvas.parentNode ) {\n\t\t\tthis.container.removeChild( this.canvas );\n\t\t}\n\n\t}\n\n}", "/**\n * The default reveal.js config object.\n */\nexport default {\n\n\t// The \"normal\" size of the presentation, aspect ratio will be preserved\n\t// when the presentation is scaled to fit different resolutions\n\twidth: 960,\n\theight: 700,\n\n\t// Factor of the display size that should remain empty around the content\n\tmargin: 0.04,\n\n\t// Bounds for smallest/largest possible scale to apply to content\n\tminScale: 0.2,\n\tmaxScale: 2.0,\n\n\t// Display presentation control arrows\n\tcontrols: true,\n\n\t// Help the user learn the controls by providing hints, for example by\n\t// bouncing the down arrow when they first encounter a vertical slide\n\tcontrolsTutorial: true,\n\n\t// Determines where controls appear, \"edges\" or \"bottom-right\"\n\tcontrolsLayout: 'bottom-right',\n\n\t// Visibility rule for backwards navigation arrows; \"faded\", \"hidden\"\n\t// or \"visible\"\n\tcontrolsBackArrows: 'faded',\n\n\t// Display a presentation progress bar\n\tprogress: true,\n\n\t// Display the page number of the current slide\n\t// - true:    Show slide number\n\t// - false:   Hide slide number\n\t//\n\t// Can optionally be set as a string that specifies the number formatting:\n\t// - \"h.v\":\t  Horizontal . vertical slide number (default)\n\t// - \"h/v\":\t  Horizontal / vertical slide number\n\t// - \"c\":\t  Flattened slide number\n\t// - \"c/t\":\t  Flattened slide number / total slides\n\t//\n\t// Alternatively, you can provide a function that returns the slide\n\t// number for the current slide. The function should take in a slide\n\t// object and return an array with one string [slideNumber] or\n\t// three strings [n1,delimiter,n2]. See #formatSlideNumber().\n\tslideNumber: false,\n\n\t// Can be used to limit the contexts in which the slide number appears\n\t// - \"all\":      Always show the slide number\n\t// - \"print\":    Only when printing to PDF\n\t// - \"speaker\":  Only in the speaker view\n\tshowSlideNumber: 'all',\n\n\t// Use 1 based indexing for # links to match slide number (default is zero\n\t// based)\n\thashOneBasedIndex: false,\n\n\t// Add the current slide number to the URL hash so that reloading the\n\t// page/copying the URL will return you to the same slide\n\thash: false,\n\n\t// Flags if we should monitor the hash and change slides accordingly\n\trespondToHashChanges: true,\n\n\t// Enable support for jump-to-slide navigation shortcuts\n\tjumpToSlide: true,\n\n\t// Push each slide change to the browser history.  Implies `hash: true`\n\thistory: false,\n\n\t// Enable keyboard shortcuts for navigation\n\tkeyboard: true,\n\n\t// Optional function that blocks keyboard events when retuning false\n\t//\n\t// If you set this to 'focused', we will only capture keyboard events\n\t// for embedded decks when they are in focus\n\tkeyboardCondition: null,\n\n\t// Disables the default reveal.js slide layout (scaling and centering)\n\t// so that you can use custom CSS layout\n\tdisableLayout: false,\n\n\t// Enable the slide overview mode\n\toverview: true,\n\n\t// Vertical centering of slides\n\tcenter: true,\n\n\t// Enables touch navigation on devices with touch input\n\ttouch: true,\n\n\t// Loop the presentation\n\tloop: false,\n\n\t// Change the presentation direction to be RTL\n\trtl: false,\n\n\t// Changes the behavior of our navigation directions.\n\t//\n\t// \"default\"\n\t// Left/right arrow keys step between horizontal slides, up/down\n\t// arrow keys step between vertical slides. Space key steps through\n\t// all slides (both horizontal and vertical).\n\t//\n\t// \"linear\"\n\t// Removes the up/down arrows. Left/right arrows step through all\n\t// slides (both horizontal and vertical).\n\t//\n\t// \"grid\"\n\t// When this is enabled, stepping left/right from a vertical stack\n\t// to an adjacent vertical stack will land you at the same vertical\n\t// index.\n\t//\n\t// Consider a deck with six slides ordered in two vertical stacks:\n\t// 1.1    2.1\n\t// 1.2    2.2\n\t// 1.3    2.3\n\t//\n\t// If you're on slide 1.3 and navigate right, you will normally move\n\t// from 1.3 -> 2.1. If \"grid\" is used, the same navigation takes you\n\t// from 1.3 -> 2.3.\n\tnavigationMode: 'default',\n\n\t// Randomizes the order of slides each time the presentation loads\n\tshuffle: false,\n\n\t// Turns fragments on and off globally\n\tfragments: true,\n\n\t// Flags whether to include the current fragment in the URL,\n\t// so that reloading brings you to the same fragment position\n\tfragmentInURL: true,\n\n\t// Flags if the presentation is running in an embedded mode,\n\t// i.e. contained within a limited portion of the screen\n\tembedded: false,\n\n\t// Flags if we should show a help overlay when the question-mark\n\t// key is pressed\n\thelp: true,\n\n\t// Flags if it should be possible to pause the presentation (blackout)\n\tpause: true,\n\n\t// Flags if speaker notes should be visible to all viewers\n\tshowNotes: false,\n\n\t// Flags if slides with data-visibility=\"hidden\" should be kep visible\n\tshowHiddenSlides: false,\n\n\t// Global override for autoplaying embedded media (video/audio/iframe)\n\t// - null:   Media will only autoplay if data-autoplay is present\n\t// - true:   All media will autoplay, regardless of individual setting\n\t// - false:  No media will autoplay, regardless of individual setting\n\tautoPlayMedia: null,\n\n\t// Global override for preloading lazy-loaded iframes\n\t// - null:   Iframes with data-src AND data-preload will be loaded when within\n\t//           the viewDistance, iframes with only data-src will be loaded when visible\n\t// - true:   All iframes with data-src will be loaded when within the viewDistance\n\t// - false:  All iframes with data-src will be loaded only when visible\n\tpreloadIframes: null,\n\n\t// Can be used to globally disable auto-animation\n\tautoAnimate: true,\n\n\t// Optionally provide a custom element matcher that will be\n\t// used to dictate which elements we can animate between.\n\tautoAnimateMatcher: null,\n\n\t// Default settings for our auto-animate transitions, can be\n\t// overridden per-slide or per-element via data arguments\n\tautoAnimateEasing: 'ease',\n\tautoAnimateDuration: 1.0,\n\tautoAnimateUnmatched: true,\n\n\t// CSS properties that can be auto-animated. Position & scale\n\t// is matched separately so there's no need to include styles\n\t// like top/right/bottom/left, width/height or margin.\n\tautoAnimateStyles: [\n\t\t'opacity',\n\t\t'color',\n\t\t'background-color',\n\t\t'padding',\n\t\t'font-size',\n\t\t'line-height',\n\t\t'letter-spacing',\n\t\t'border-width',\n\t\t'border-color',\n\t\t'border-radius',\n\t\t'outline',\n\t\t'outline-offset'\n\t],\n\n\t// Controls automatic progression to the next slide\n\t// - 0:      Auto-sliding only happens if the data-autoslide HTML attribute\n\t//           is present on the current slide or fragment\n\t// - 1+:     All slides will progress automatically at the given interval\n\t// - false:  No auto-sliding, even if data-autoslide is present\n\tautoSlide: 0,\n\n\t// Stop auto-sliding after user input\n\tautoSlideStoppable: true,\n\n\t// Use this method for navigation when auto-sliding (defaults to navigateNext)\n\tautoSlideMethod: null,\n\n\t// Specify the average time in seconds that you think you will spend\n\t// presenting each slide. This is used to show a pacing timer in the\n\t// speaker view\n\tdefaultTiming: null,\n\n\t// Enable slide navigation via mouse wheel\n\tmouseWheel: false,\n\n\t// Opens links in an iframe preview overlay\n\t// Add `data-preview-link` and `data-preview-link=\"false\"` to customise each link\n\t// individually\n\tpreviewLinks: false,\n\n\t// Exposes the reveal.js API through window.postMessage\n\tpostMessage: true,\n\n\t// Dispatches all reveal.js events to the parent window through postMessage\n\tpostMessageEvents: false,\n\n\t// Focuses body when page changes visibility to ensure keyboard shortcuts work\n\tfocusBodyOnPageVisibilityChange: true,\n\n\t// Transition style\n\ttransition: 'slide', // none/fade/slide/convex/concave/zoom\n\n\t// Transition speed\n\ttransitionSpeed: 'default', // default/fast/slow\n\n\t// Transition style for full page slide backgrounds\n\tbackgroundTransition: 'fade', // none/fade/slide/convex/concave/zoom\n\n\t// Parallax background image\n\tparallaxBackgroundImage: '', // CSS syntax, e.g. \"a.jpg\"\n\n\t// Parallax background size\n\tparallaxBackgroundSize: '', // CSS syntax, e.g. \"3000px 2000px\"\n\n\t// Parallax background repeat\n\tparallaxBackgroundRepeat: '', // repeat/repeat-x/repeat-y/no-repeat/initial/inherit\n\n\t// Parallax background position\n\tparallaxBackgroundPosition: '', // CSS syntax, e.g. \"top left\"\n\n\t// Amount of pixels to move the parallax background per slide step\n\tparallaxBackgroundHorizontal: null,\n\tparallaxBackgroundVertical: null,\n\n\t// Can be used to initialize reveal.js in one of the following views:\n\t// - print:   Render the presentation so that it can be printed to PDF\n\t// - scroll:  Show the presentation as a tall scrollable page with scroll\n\t//            triggered animations\n\tview: null,\n\n\t// Adjusts the height of each slide in the scroll view.\n\t// - full:       Each slide is as tall as the viewport\n\t// - compact:    Slides are as small as possible, allowing multiple slides\n\t//               to be visible in parallel on tall devices\n\tscrollLayout: 'full',\n\n\t// Control how scroll snapping works in the scroll view.\n\t// - false:   \tNo snapping, scrolling is continuous\n\t// - proximity:  Snap when close to a slide\n\t// - mandatory:  Always snap to the closest slide\n\t//\n\t// Only applies to presentations in scroll view.\n\tscrollSnap: 'mandatory',\n\n\t// Enables and configure the scroll view progress bar.\n\t// - 'auto':    Show the scrollbar while scrolling, hide while idle\n\t// - true:      Always show the scrollbar\n\t// - false:     Never show the scrollbar\n\tscrollProgress: 'auto',\n\n\t// Automatically activate the scroll view when we the viewport falls\n\t// below the given width.\n\tscrollActivationWidth: 435,\n\n\t// The maximum number of pages a single slide can expand onto when printing\n\t// to PDF, unlimited by default\n\tpdfMaxPagesPerSlide: Number.POSITIVE_INFINITY,\n\n\t// Prints each fragment on a separate slide\n\tpdfSeparateFragments: true,\n\n\t// Offset used to reduce the height of content within exported PDF pages.\n\t// This exists to account for environment differences based on how you\n\t// print to PDF. CLI printing options, like phantomjs and wkpdf, can end\n\t// on precisely the total height of the document whereas in-browser\n\t// printing has to end one pixel before.\n\tpdfPageHeightOffset: -1,\n\n\t// Number of slides away from the current that are visible\n\tviewDistance: 3,\n\n\t// Number of slides away from the current that are visible on mobile\n\t// devices. It is advisable to set this to a lower number than\n\t// viewDistance in order to save resources.\n\tmobileViewDistance: 2,\n\n\t// The display mode that will be used to show slides\n\tdisplay: 'block',\n\n\t// Hide cursor if inactive\n\thideInactiveCursor: true,\n\n\t// Time before the cursor is hidden (in ms)\n\thideCursorTime: 5000,\n\n\t// Should we automatically sort and set indices for fragments\n\t// at each sync? (See Reveal.sync)\n\tsortFragmentsOnSync: true,\n\n\t// Script dependencies to load\n\tdependencies: [],\n\n\t// Plugin objects to register and use for this presentation\n\tplugins: []\n\n}", "import SlideContent from './controllers/slidecontent.js'\nimport SlideNumber from './controllers/slidenumber.js'\nimport JumpToSlide from './controllers/jumptoslide.js'\nimport Backgrounds from './controllers/backgrounds.js'\nimport AutoAnimate from './controllers/autoanimate.js'\nimport ScrollView from './controllers/scrollview.js'\nimport PrintView from './controllers/printview.js'\nimport Fragments from './controllers/fragments.js'\nimport Overview from './controllers/overview.js'\nimport Keyboard from './controllers/keyboard.js'\nimport Location from './controllers/location.js'\nimport Controls from './controllers/controls.js'\nimport Progress from './controllers/progress.js'\nimport Pointer from './controllers/pointer.js'\nimport Plugins from './controllers/plugins.js'\nimport Touch from './controllers/touch.js'\nimport Focus from './controllers/focus.js'\nimport Notes from './controllers/notes.js'\nimport Playback from './components/playback.js'\nimport defaultConfig from './config.js'\nimport * as Util from './utils/util.js'\nimport * as Device from './utils/device.js'\nimport {\n\tSLIDES_SELECTOR,\n\tHORIZONTAL_SLIDES_SELECTOR,\n\tVERTICAL_SLIDES_SELECTOR,\n\tPOST_MESSAGE_METHOD_BLACKLIST\n} from './utils/constants.js'\n\n// The reveal.js version\nexport const VERSION = '5.1.0';\n\n/**\n * reveal.js\n * https://revealjs.com\n * MIT licensed\n *\n * Copyright (C) 2011-2022 Hakim El Hattab, https://hakim.se\n */\nexport default function( revealElement, options ) {\n\n\t// Support initialization with no args, one arg\n\t// [options] or two args [revealElement, options]\n\tif( arguments.length < 2 ) {\n\t\toptions = arguments[0];\n\t\trevealElement = document.querySelector( '.reveal' );\n\t}\n\n\tconst Reveal = {};\n\n\t// Configuration defaults, can be overridden at initialization time\n\tlet config = {},\n\n\t\t// Flags if initialize() has been invoked for this reveal instance\n\t\tinitialized = false,\n\n\t\t// Flags if reveal.js is loaded (has dispatched the 'ready' event)\n\t\tready = false,\n\n\t\t// The horizontal and vertical index of the currently active slide\n\t\tindexh,\n\t\tindexv,\n\n\t\t// The previous and current slide HTML elements\n\t\tpreviousSlide,\n\t\tcurrentSlide,\n\n\t\t// Remember which directions that the user has navigated towards\n\t\tnavigationHistory = {\n\t\t\thasNavigatedHorizontally: false,\n\t\t\thasNavigatedVertically: false\n\t\t},\n\n\t\t// Slides may have a data-state attribute which we pick up and apply\n\t\t// as a class to the body. This list contains the combined state of\n\t\t// all current slides.\n\t\tstate = [],\n\n\t\t// The current scale of the presentation (see width/height config)\n\t\tscale = 1,\n\n\t\t// CSS transform that is currently applied to the slides container,\n\t\t// split into two groups\n\t\tslidesTransform = { layout: '', overview: '' },\n\n\t\t// Cached references to DOM elements\n\t\tdom = {},\n\n\t\t// Flags if the interaction event listeners are bound\n\t\teventsAreBound = false,\n\n\t\t// The current slide transition state; idle or running\n\t\ttransition = 'idle',\n\n\t\t// The current auto-slide duration\n\t\tautoSlide = 0,\n\n\t\t// Auto slide properties\n\t\tautoSlidePlayer,\n\t\tautoSlideTimeout = 0,\n\t\tautoSlideStartTime = -1,\n\t\tautoSlidePaused = false,\n\n\t\t// Controllers for different aspects of our presentation. They're\n\t\t// all given direct references to this Reveal instance since there\n\t\t// may be multiple presentations running in parallel.\n\t\tslideContent = new SlideContent( Reveal ),\n\t\tslideNumber = new SlideNumber( Reveal ),\n\t\tjumpToSlide = new JumpToSlide( Reveal ),\n\t\tautoAnimate = new AutoAnimate( Reveal ),\n\t\tbackgrounds = new Backgrounds( Reveal ),\n\t\tscrollView = new ScrollView( Reveal ),\n\t\tprintView = new PrintView( Reveal ),\n\t\tfragments = new Fragments( Reveal ),\n\t\toverview = new Overview( Reveal ),\n\t\tkeyboard = new Keyboard( Reveal ),\n\t\tlocation = new Location( Reveal ),\n\t\tcontrols = new Controls( Reveal ),\n\t\tprogress = new Progress( Reveal ),\n\t\tpointer = new Pointer( Reveal ),\n\t\tplugins = new Plugins( Reveal ),\n\t\tfocus = new Focus( Reveal ),\n\t\ttouch = new Touch( Reveal ),\n\t\tnotes = new Notes( Reveal );\n\n\t/**\n\t * Starts up the presentation.\n\t */\n\tfunction initialize( initOptions ) {\n\n\t\tif( !revealElement ) throw 'Unable to find presentation root (<div class=\"reveal\">).';\n\n\t\tinitialized = true;\n\n\t\t// Cache references to key DOM elements\n\t\tdom.wrapper = revealElement;\n\t\tdom.slides = revealElement.querySelector( '.slides' );\n\n\t\tif( !dom.slides ) throw 'Unable to find slides container (<div class=\"slides\">).';\n\n\t\t// Compose our config object in order of increasing precedence:\n\t\t// 1. Default reveal.js options\n\t\t// 2. Options provided via Reveal.configure() prior to\n\t\t//    initialization\n\t\t// 3. Options passed to the Reveal constructor\n\t\t// 4. Options passed to Reveal.initialize\n\t\t// 5. Query params\n\t\tconfig = { ...defaultConfig, ...config, ...options, ...initOptions, ...Util.getQueryHash() };\n\n\t\t// Legacy support for the ?print-pdf query\n\t\tif( /print-pdf/gi.test( window.location.search ) ) {\n\t\t\tconfig.view = 'print';\n\t\t}\n\n\t\tsetViewport();\n\n\t\t// Force a layout when the whole page, incl fonts, has loaded\n\t\twindow.addEventListener( 'load', layout, false );\n\n\t\t// Register plugins and load dependencies, then move on to #start()\n\t\tplugins.load( config.plugins, config.dependencies ).then( start );\n\n\t\treturn new Promise( resolve => Reveal.on( 'ready', resolve ) );\n\n\t}\n\n\t/**\n\t * Encase the presentation in a reveal.js viewport. The\n\t * extent of the viewport differs based on configuration.\n\t */\n\tfunction setViewport() {\n\n\t\t// Embedded decks use the reveal element as their viewport\n\t\tif( config.embedded === true ) {\n\t\t\tdom.viewport = Util.closest( revealElement, '.reveal-viewport' ) || revealElement;\n\t\t}\n\t\t// Full-page decks use the body as their viewport\n\t\telse {\n\t\t\tdom.viewport = document.body;\n\t\t\tdocument.documentElement.classList.add( 'reveal-full-page' );\n\t\t}\n\n\t\tdom.viewport.classList.add( 'reveal-viewport' );\n\n\t}\n\n\t/**\n\t * Starts up reveal.js by binding input events and navigating\n\t * to the current URL deeplink if there is one.\n\t */\n\tfunction start() {\n\n\t\tready = true;\n\n\t\t// Remove slides hidden with data-visibility\n\t\tremoveHiddenSlides();\n\n\t\t// Make sure we've got all the DOM elements we need\n\t\tsetupDOM();\n\n\t\t// Listen to messages posted to this window\n\t\tsetupPostMessage();\n\n\t\t// Prevent the slides from being scrolled out of view\n\t\tsetupScrollPrevention();\n\n\t\t// Adds bindings for fullscreen mode\n\t\tsetupFullscreen();\n\n\t\t// Resets all vertical slides so that only the first is visible\n\t\tresetVerticalSlides();\n\n\t\t// Updates the presentation to match the current configuration values\n\t\tconfigure();\n\n\t\t// Create slide backgrounds\n\t\tbackgrounds.update( true );\n\n\t\t// Activate the print/scroll view if configured\n\t\tactivateInitialView();\n\n\t\t// Read the initial hash\n\t\tlocation.readURL();\n\n\t\t// Notify listeners that the presentation is ready but use a 1ms\n\t\t// timeout to ensure it's not fired synchronously after #initialize()\n\t\tsetTimeout( () => {\n\t\t\t// Enable transitions now that we're loaded\n\t\t\tdom.slides.classList.remove( 'no-transition' );\n\n\t\t\tdom.wrapper.classList.add( 'ready' );\n\n\t\t\tdispatchEvent({\n\t\t\t\ttype: 'ready',\n\t\t\t\tdata: {\n\t\t\t\t\tindexh,\n\t\t\t\t\tindexv,\n\t\t\t\t\tcurrentSlide\n\t\t\t\t}\n\t\t\t});\n\t\t}, 1 );\n\n\t}\n\n\t/**\n\t * Activates the correct reveal.js view based on our config.\n\t * This is only invoked once during initialization.\n\t */\n\tfunction activateInitialView() {\n\n\t\tconst activatePrintView = config.view === 'print';\n\t\tconst activateScrollView = config.view === 'scroll' || config.view === 'reader';\n\n\t\tif( activatePrintView || activateScrollView ) {\n\n\t\t\tif( activatePrintView ) {\n\t\t\t\tremoveEventListeners();\n\t\t\t}\n\t\t\telse {\n\t\t\t\ttouch.unbind();\n\t\t\t}\n\n\t\t\t// Avoid content flickering during layout\n\t\t\tdom.viewport.classList.add( 'loading-scroll-mode' );\n\n\t\t\tif( activatePrintView ) {\n\t\t\t\t// The document needs to have loaded for the PDF layout\n\t\t\t\t// measurements to be accurate\n\t\t\t\tif( document.readyState === 'complete' ) {\n\t\t\t\t\tprintView.activate();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\twindow.addEventListener( 'load', () => printView.activate() );\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tscrollView.activate();\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Removes all slides with data-visibility=\"hidden\". This\n\t * is done right before the rest of the presentation is\n\t * initialized.\n\t *\n\t * If you want to show all hidden slides, initialize\n\t * reveal.js with showHiddenSlides set to true.\n\t */\n\tfunction removeHiddenSlides() {\n\n\t\tif( !config.showHiddenSlides ) {\n\t\t\tUtil.queryAll( dom.wrapper, 'section[data-visibility=\"hidden\"]' ).forEach( slide => {\n\t\t\t\tconst parent = slide.parentNode;\n\n\t\t\t\t// If this slide is part of a stack and that stack will be\n\t\t\t\t// empty after removing the hidden slide, remove the entire\n\t\t\t\t// stack\n\t\t\t\tif( parent.childElementCount === 1 && /section/i.test( parent.nodeName ) ) {\n\t\t\t\t\tparent.remove();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tslide.remove();\n\t\t\t\t}\n\n\t\t\t} );\n\t\t}\n\n\t}\n\n\t/**\n\t * Finds and stores references to DOM elements which are\n\t * required by the presentation. If a required element is\n\t * not found, it is created.\n\t */\n\tfunction setupDOM() {\n\n\t\t// Prevent transitions while we're loading\n\t\tdom.slides.classList.add( 'no-transition' );\n\n\t\tif( Device.isMobile ) {\n\t\t\tdom.wrapper.classList.add( 'no-hover' );\n\t\t}\n\t\telse {\n\t\t\tdom.wrapper.classList.remove( 'no-hover' );\n\t\t}\n\n\t\tbackgrounds.render();\n\t\tslideNumber.render();\n\t\tjumpToSlide.render();\n\t\tcontrols.render();\n\t\tprogress.render();\n\t\tnotes.render();\n\n\t\t// Overlay graphic which is displayed during the paused mode\n\t\tdom.pauseOverlay = Util.createSingletonNode( dom.wrapper, 'div', 'pause-overlay', config.controls ? '<button class=\"resume-button\">Resume presentation</button>' : null );\n\n\t\tdom.statusElement = createStatusElement();\n\n\t\tdom.wrapper.setAttribute( 'role', 'application' );\n\t}\n\n\t/**\n\t * Creates a hidden div with role aria-live to announce the\n\t * current slide content. Hide the div off-screen to make it\n\t * available only to Assistive Technologies.\n\t *\n\t * @return {HTMLElement}\n\t */\n\tfunction createStatusElement() {\n\n\t\tlet statusElement = dom.wrapper.querySelector( '.aria-status' );\n\t\tif( !statusElement ) {\n\t\t\tstatusElement = document.createElement( 'div' );\n\t\t\tstatusElement.style.position = 'absolute';\n\t\t\tstatusElement.style.height = '1px';\n\t\t\tstatusElement.style.width = '1px';\n\t\t\tstatusElement.style.overflow = 'hidden';\n\t\t\tstatusElement.style.clip = 'rect( 1px, 1px, 1px, 1px )';\n\t\t\tstatusElement.classList.add( 'aria-status' );\n\t\t\tstatusElement.setAttribute( 'aria-live', 'polite' );\n\t\t\tstatusElement.setAttribute( 'aria-atomic','true' );\n\t\t\tdom.wrapper.appendChild( statusElement );\n\t\t}\n\t\treturn statusElement;\n\n\t}\n\n\t/**\n\t * Announces the given text to screen readers.\n\t */\n\tfunction announceStatus( value ) {\n\n\t\tdom.statusElement.textContent = value;\n\n\t}\n\n\t/**\n\t * Converts the given HTML element into a string of text\n\t * that can be announced to a screen reader. Hidden\n\t * elements are excluded.\n\t */\n\tfunction getStatusText( node ) {\n\n\t\tlet text = '';\n\n\t\t// Text node\n\t\tif( node.nodeType === 3 ) {\n\t\t\ttext += node.textContent;\n\t\t}\n\t\t// Element node\n\t\telse if( node.nodeType === 1 ) {\n\n\t\t\tlet isAriaHidden = node.getAttribute( 'aria-hidden' );\n\t\t\tlet isDisplayHidden = window.getComputedStyle( node )['display'] === 'none';\n\t\t\tif( isAriaHidden !== 'true' && !isDisplayHidden ) {\n\n\t\t\t\tArray.from( node.childNodes ).forEach( child => {\n\t\t\t\t\ttext += getStatusText( child );\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t}\n\n\t\ttext = text.trim();\n\n\t\treturn text === '' ? '' : text + ' ';\n\n\t}\n\n\t/**\n\t * This is an unfortunate necessity. Some actions – such as\n\t * an input field being focused in an iframe or using the\n\t * keyboard to expand text selection beyond the bounds of\n\t * a slide – can trigger our content to be pushed out of view.\n\t * This scrolling can not be prevented by hiding overflow in\n\t * CSS (we already do) so we have to resort to repeatedly\n\t * checking if the slides have been offset :(\n\t */\n\tfunction setupScrollPrevention() {\n\n\t\tsetInterval( () => {\n\t\t\tif( !scrollView.isActive() && dom.wrapper.scrollTop !== 0 || dom.wrapper.scrollLeft !== 0 ) {\n\t\t\t\tdom.wrapper.scrollTop = 0;\n\t\t\t\tdom.wrapper.scrollLeft = 0;\n\t\t\t}\n\t\t}, 1000 );\n\n\t}\n\n\t/**\n\t * After entering fullscreen we need to force a layout to\n\t * get our presentations to scale correctly. This behavior\n\t * is inconsistent across browsers but a force layout seems\n\t * to normalize it.\n\t */\n\tfunction setupFullscreen() {\n\n\t\tdocument.addEventListener( 'fullscreenchange', onFullscreenChange );\n\t\tdocument.addEventListener( 'webkitfullscreenchange', onFullscreenChange );\n\n\t}\n\n\t/**\n\t * Registers a listener to postMessage events, this makes it\n\t * possible to call all reveal.js API methods from another\n\t * window. For example:\n\t *\n\t * revealWindow.postMessage( JSON.stringify({\n\t *   method: 'slide',\n\t *   args: [ 2 ]\n\t * }), '*' );\n\t */\n\tfunction setupPostMessage() {\n\n\t\tif( config.postMessage ) {\n\t\t\twindow.addEventListener( 'message', onPostMessage, false );\n\t\t}\n\n\t}\n\n\t/**\n\t * Applies the configuration settings from the config\n\t * object. May be called multiple times.\n\t *\n\t * @param {object} options\n\t */\n\tfunction configure( options ) {\n\n\t\tconst oldConfig = { ...config }\n\n\t\t// New config options may be passed when this method\n\t\t// is invoked through the API after initialization\n\t\tif( typeof options === 'object' ) Util.extend( config, options );\n\n\t\t// Abort if reveal.js hasn't finished loading, config\n\t\t// changes will be applied automatically once ready\n\t\tif( Reveal.isReady() ===  false ) return;\n\n\t\tconst numberOfSlides = dom.wrapper.querySelectorAll( SLIDES_SELECTOR ).length;\n\n\t\t// The transition is added as a class on the .reveal element\n\t\tdom.wrapper.classList.remove( oldConfig.transition );\n\t\tdom.wrapper.classList.add( config.transition );\n\n\t\tdom.wrapper.setAttribute( 'data-transition-speed', config.transitionSpeed );\n\t\tdom.wrapper.setAttribute( 'data-background-transition', config.backgroundTransition );\n\n\t\t// Expose our configured slide dimensions as custom props\n\t\tdom.viewport.style.setProperty( '--slide-width', typeof config.width === 'string' ? config.width :  config.width + 'px' );\n\t\tdom.viewport.style.setProperty( '--slide-height', typeof config.height === 'string' ? config.height :  config.height + 'px' );\n\n\t\tif( config.shuffle ) {\n\t\t\tshuffle();\n\t\t}\n\n\t\tUtil.toggleClass( dom.wrapper, 'embedded', config.embedded );\n\t\tUtil.toggleClass( dom.wrapper, 'rtl', config.rtl );\n\t\tUtil.toggleClass( dom.wrapper, 'center', config.center );\n\n\t\t// Exit the paused mode if it was configured off\n\t\tif( config.pause === false ) {\n\t\t\tresume();\n\t\t}\n\n\t\t// Iframe link previews\n\t\tif( config.previewLinks ) {\n\t\t\tenablePreviewLinks();\n\t\t\tdisablePreviewLinks( '[data-preview-link=false]' );\n\t\t}\n\t\telse {\n\t\t\tdisablePreviewLinks();\n\t\t\tenablePreviewLinks( '[data-preview-link]:not([data-preview-link=false])' );\n\t\t}\n\n\t\t// Reset all changes made by auto-animations\n\t\tautoAnimate.reset();\n\n\t\t// Remove existing auto-slide controls\n\t\tif( autoSlidePlayer ) {\n\t\t\tautoSlidePlayer.destroy();\n\t\t\tautoSlidePlayer = null;\n\t\t}\n\n\t\t// Generate auto-slide controls if needed\n\t\tif( numberOfSlides > 1 && config.autoSlide && config.autoSlideStoppable ) {\n\t\t\tautoSlidePlayer = new Playback( dom.wrapper, () => {\n\t\t\t\treturn Math.min( Math.max( ( Date.now() - autoSlideStartTime ) / autoSlide, 0 ), 1 );\n\t\t\t} );\n\n\t\t\tautoSlidePlayer.on( 'click', onAutoSlidePlayerClick );\n\t\t\tautoSlidePaused = false;\n\t\t}\n\n\t\t// Add the navigation mode to the DOM so we can adjust styling\n\t\tif( config.navigationMode !== 'default' ) {\n\t\t\tdom.wrapper.setAttribute( 'data-navigation-mode', config.navigationMode );\n\t\t}\n\t\telse {\n\t\t\tdom.wrapper.removeAttribute( 'data-navigation-mode' );\n\t\t}\n\n\t\tnotes.configure( config, oldConfig );\n\t\tfocus.configure( config, oldConfig );\n\t\tpointer.configure( config, oldConfig );\n\t\tcontrols.configure( config, oldConfig );\n\t\tprogress.configure( config, oldConfig );\n\t\tkeyboard.configure( config, oldConfig );\n\t\tfragments.configure( config, oldConfig );\n\t\tslideNumber.configure( config, oldConfig );\n\n\t\tsync();\n\n\t}\n\n\t/**\n\t * Binds all event listeners.\n\t */\n\tfunction addEventListeners() {\n\n\t\teventsAreBound = true;\n\n\t\twindow.addEventListener( 'resize', onWindowResize, false );\n\n\t\tif( config.touch ) touch.bind();\n\t\tif( config.keyboard ) keyboard.bind();\n\t\tif( config.progress ) progress.bind();\n\t\tif( config.respondToHashChanges ) location.bind();\n\t\tcontrols.bind();\n\t\tfocus.bind();\n\n\t\tdom.slides.addEventListener( 'click', onSlidesClicked, false );\n\t\tdom.slides.addEventListener( 'transitionend', onTransitionEnd, false );\n\t\tdom.pauseOverlay.addEventListener( 'click', resume, false );\n\n\t\tif( config.focusBodyOnPageVisibilityChange ) {\n\t\t\tdocument.addEventListener( 'visibilitychange', onPageVisibilityChange, false );\n\t\t}\n\n\t}\n\n\t/**\n\t * Unbinds all event listeners.\n\t */\n\tfunction removeEventListeners() {\n\n\t\teventsAreBound = false;\n\n\t\ttouch.unbind();\n\t\tfocus.unbind();\n\t\tkeyboard.unbind();\n\t\tcontrols.unbind();\n\t\tprogress.unbind();\n\t\tlocation.unbind();\n\n\t\twindow.removeEventListener( 'resize', onWindowResize, false );\n\n\t\tdom.slides.removeEventListener( 'click', onSlidesClicked, false );\n\t\tdom.slides.removeEventListener( 'transitionend', onTransitionEnd, false );\n\t\tdom.pauseOverlay.removeEventListener( 'click', resume, false );\n\n\t}\n\n\t/**\n\t * Uninitializes reveal.js by undoing changes made to the\n\t * DOM and removing all event listeners.\n\t */\n\tfunction destroy() {\n\n\t\t// There's nothing to destroy if this instance hasn't been\n\t\t// initialized yet\n\t\tif( initialized === false ) return;\n\n\t\tremoveEventListeners();\n\t\tcancelAutoSlide();\n\t\tdisablePreviewLinks();\n\n\t\t// Destroy controllers\n\t\tnotes.destroy();\n\t\tfocus.destroy();\n\t\tplugins.destroy();\n\t\tpointer.destroy();\n\t\tcontrols.destroy();\n\t\tprogress.destroy();\n\t\tbackgrounds.destroy();\n\t\tslideNumber.destroy();\n\t\tjumpToSlide.destroy();\n\n\t\t// Remove event listeners\n\t\tdocument.removeEventListener( 'fullscreenchange', onFullscreenChange );\n\t\tdocument.removeEventListener( 'webkitfullscreenchange', onFullscreenChange );\n\t\tdocument.removeEventListener( 'visibilitychange', onPageVisibilityChange, false );\n\t\twindow.removeEventListener( 'message', onPostMessage, false );\n\t\twindow.removeEventListener( 'load', layout, false );\n\n\t\t// Undo DOM changes\n\t\tif( dom.pauseOverlay ) dom.pauseOverlay.remove();\n\t\tif( dom.statusElement ) dom.statusElement.remove();\n\n\t\tdocument.documentElement.classList.remove( 'reveal-full-page' );\n\n\t\tdom.wrapper.classList.remove( 'ready', 'center', 'has-horizontal-slides', 'has-vertical-slides' );\n\t\tdom.wrapper.removeAttribute( 'data-transition-speed' );\n\t\tdom.wrapper.removeAttribute( 'data-background-transition' );\n\n\t\tdom.viewport.classList.remove( 'reveal-viewport' );\n\t\tdom.viewport.style.removeProperty( '--slide-width' );\n\t\tdom.viewport.style.removeProperty( '--slide-height' );\n\n\t\tdom.slides.style.removeProperty( 'width' );\n\t\tdom.slides.style.removeProperty( 'height' );\n\t\tdom.slides.style.removeProperty( 'zoom' );\n\t\tdom.slides.style.removeProperty( 'left' );\n\t\tdom.slides.style.removeProperty( 'top' );\n\t\tdom.slides.style.removeProperty( 'bottom' );\n\t\tdom.slides.style.removeProperty( 'right' );\n\t\tdom.slides.style.removeProperty( 'transform' );\n\n\t\tArray.from( dom.wrapper.querySelectorAll( SLIDES_SELECTOR ) ).forEach( slide => {\n\t\t\tslide.style.removeProperty( 'display' );\n\t\t\tslide.style.removeProperty( 'top' );\n\t\t\tslide.removeAttribute( 'hidden' );\n\t\t\tslide.removeAttribute( 'aria-hidden' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Adds a listener to one of our custom reveal.js events,\n\t * like slidechanged.\n\t */\n\tfunction on( type, listener, useCapture ) {\n\n\t\trevealElement.addEventListener( type, listener, useCapture );\n\n\t}\n\n\t/**\n\t * Unsubscribes from a reveal.js event.\n\t */\n\tfunction off( type, listener, useCapture ) {\n\n\t\trevealElement.removeEventListener( type, listener, useCapture );\n\n\t}\n\n\t/**\n\t * Applies CSS transforms to the slides container. The container\n\t * is transformed from two separate sources: layout and the overview\n\t * mode.\n\t *\n\t * @param {object} transforms\n\t */\n\tfunction transformSlides( transforms ) {\n\n\t\t// Pick up new transforms from arguments\n\t\tif( typeof transforms.layout === 'string' ) slidesTransform.layout = transforms.layout;\n\t\tif( typeof transforms.overview === 'string' ) slidesTransform.overview = transforms.overview;\n\n\t\t// Apply the transforms to the slides container\n\t\tif( slidesTransform.layout ) {\n\t\t\tUtil.transformElement( dom.slides, slidesTransform.layout + ' ' + slidesTransform.overview );\n\t\t}\n\t\telse {\n\t\t\tUtil.transformElement( dom.slides, slidesTransform.overview );\n\t\t}\n\n\t}\n\n\t/**\n\t * Dispatches an event of the specified type from the\n\t * reveal DOM element.\n\t */\n\tfunction dispatchEvent({ target=dom.wrapper, type, data, bubbles=true }) {\n\n\t\tlet event = document.createEvent( 'HTMLEvents', 1, 2 );\n\t\tevent.initEvent( type, bubbles, true );\n\t\tUtil.extend( event, data );\n\t\ttarget.dispatchEvent( event );\n\n\t\tif( target === dom.wrapper ) {\n\t\t\t// If we're in an iframe, post each reveal.js event to the\n\t\t\t// parent window. Used by the notes plugin\n\t\t\tdispatchPostMessage( type );\n\t\t}\n\n\t\treturn event;\n\n\t}\n\n\t/**\n\t * Dispatches a slidechanged event.\n\t *\n\t * @param {string} origin Used to identify multiplex clients\n\t */\n\tfunction dispatchSlideChanged( origin ) {\n\n\t\tdispatchEvent({\n\t\t\ttype: 'slidechanged',\n\t\t\tdata: {\n\t\t\t\tindexh,\n\t\t\t\tindexv,\n\t\t\t\tpreviousSlide,\n\t\t\t\tcurrentSlide,\n\t\t\t\torigin\n\t\t\t}\n\t\t});\n\n\t}\n\n\t/**\n\t * Dispatched a postMessage of the given type from our window.\n\t */\n\tfunction dispatchPostMessage( type, data ) {\n\n\t\tif( config.postMessageEvents && window.parent !== window.self ) {\n\t\t\tlet message = {\n\t\t\t\tnamespace: 'reveal',\n\t\t\t\teventName: type,\n\t\t\t\tstate: getState()\n\t\t\t};\n\n\t\t\tUtil.extend( message, data );\n\n\t\t\twindow.parent.postMessage( JSON.stringify( message ), '*' );\n\t\t}\n\n\t}\n\n\t/**\n\t * Bind preview frame links.\n\t *\n\t * @param {string} [selector=a] - selector for anchors\n\t */\n\tfunction enablePreviewLinks( selector = 'a' ) {\n\n\t\tArray.from( dom.wrapper.querySelectorAll( selector ) ).forEach( element => {\n\t\t\tif( /^(http|www)/gi.test( element.getAttribute( 'href' ) ) ) {\n\t\t\t\telement.addEventListener( 'click', onPreviewLinkClicked, false );\n\t\t\t}\n\t\t} );\n\n\t}\n\n\t/**\n\t * Unbind preview frame links.\n\t */\n\tfunction disablePreviewLinks( selector = 'a' ) {\n\n\t\tArray.from( dom.wrapper.querySelectorAll( selector ) ).forEach( element => {\n\t\t\tif( /^(http|www)/gi.test( element.getAttribute( 'href' ) ) ) {\n\t\t\t\telement.removeEventListener( 'click', onPreviewLinkClicked, false );\n\t\t\t}\n\t\t} );\n\n\t}\n\n\t/**\n\t * Opens a preview window for the target URL.\n\t *\n\t * @param {string} url - url for preview iframe src\n\t */\n\tfunction showPreview( url ) {\n\n\t\tcloseOverlay();\n\n\t\tdom.overlay = document.createElement( 'div' );\n\t\tdom.overlay.classList.add( 'overlay' );\n\t\tdom.overlay.classList.add( 'overlay-preview' );\n\t\tdom.wrapper.appendChild( dom.overlay );\n\n\t\tdom.overlay.innerHTML =\n\t\t\t`<header>\n\t\t\t\t<a class=\"close\" href=\"#\"><span class=\"icon\"></span></a>\n\t\t\t\t<a class=\"external\" href=\"${url}\" target=\"_blank\"><span class=\"icon\"></span></a>\n\t\t\t</header>\n\t\t\t<div class=\"spinner\"></div>\n\t\t\t<div class=\"viewport\">\n\t\t\t\t<iframe src=\"${url}\"></iframe>\n\t\t\t\t<small class=\"viewport-inner\">\n\t\t\t\t\t<span class=\"x-frame-error\">Unable to load iframe. This is likely due to the site's policy (x-frame-options).</span>\n\t\t\t\t</small>\n\t\t\t</div>`;\n\n\t\tdom.overlay.querySelector( 'iframe' ).addEventListener( 'load', event => {\n\t\t\tdom.overlay.classList.add( 'loaded' );\n\t\t}, false );\n\n\t\tdom.overlay.querySelector( '.close' ).addEventListener( 'click', event => {\n\t\t\tcloseOverlay();\n\t\t\tevent.preventDefault();\n\t\t}, false );\n\n\t\tdom.overlay.querySelector( '.external' ).addEventListener( 'click', event => {\n\t\t\tcloseOverlay();\n\t\t}, false );\n\n\t}\n\n\t/**\n\t * Open or close help overlay window.\n\t *\n\t * @param {Boolean} [override] Flag which overrides the\n\t * toggle logic and forcibly sets the desired state. True means\n\t * help is open, false means it's closed.\n\t */\n\tfunction toggleHelp( override ){\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? showHelp() : closeOverlay();\n\t\t}\n\t\telse {\n\t\t\tif( dom.overlay ) {\n\t\t\t\tcloseOverlay();\n\t\t\t}\n\t\t\telse {\n\t\t\t\tshowHelp();\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Opens an overlay window with help material.\n\t */\n\tfunction showHelp() {\n\n\t\tif( config.help ) {\n\n\t\t\tcloseOverlay();\n\n\t\t\tdom.overlay = document.createElement( 'div' );\n\t\t\tdom.overlay.classList.add( 'overlay' );\n\t\t\tdom.overlay.classList.add( 'overlay-help' );\n\t\t\tdom.wrapper.appendChild( dom.overlay );\n\n\t\t\tlet html = '<p class=\"title\">Keyboard Shortcuts</p><br/>';\n\n\t\t\tlet shortcuts = keyboard.getShortcuts(),\n\t\t\t\tbindings = keyboard.getBindings();\n\n\t\t\thtml += '<table><th>KEY</th><th>ACTION</th>';\n\t\t\tfor( let key in shortcuts ) {\n\t\t\t\thtml += `<tr><td>${key}</td><td>${shortcuts[ key ]}</td></tr>`;\n\t\t\t}\n\n\t\t\t// Add custom key bindings that have associated descriptions\n\t\t\tfor( let binding in bindings ) {\n\t\t\t\tif( bindings[binding].key && bindings[binding].description ) {\n\t\t\t\t\thtml += `<tr><td>${bindings[binding].key}</td><td>${bindings[binding].description}</td></tr>`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\thtml += '</table>';\n\n\t\t\tdom.overlay.innerHTML = `\n\t\t\t\t<header>\n\t\t\t\t\t<a class=\"close\" href=\"#\"><span class=\"icon\"></span></a>\n\t\t\t\t</header>\n\t\t\t\t<div class=\"viewport\">\n\t\t\t\t\t<div class=\"viewport-inner\">${html}</div>\n\t\t\t\t</div>\n\t\t\t`;\n\n\t\t\tdom.overlay.querySelector( '.close' ).addEventListener( 'click', event => {\n\t\t\t\tcloseOverlay();\n\t\t\t\tevent.preventDefault();\n\t\t\t}, false );\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Closes any currently open overlay.\n\t */\n\tfunction closeOverlay() {\n\n\t\tif( dom.overlay ) {\n\t\t\tdom.overlay.parentNode.removeChild( dom.overlay );\n\t\t\tdom.overlay = null;\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t/**\n\t * Applies JavaScript-controlled layout rules to the\n\t * presentation.\n\t */\n\tfunction layout() {\n\n\t\tif( dom.wrapper && !printView.isActive() ) {\n\n\t\t\tconst viewportWidth = dom.viewport.offsetWidth;\n\t\t\tconst viewportHeight = dom.viewport.offsetHeight;\n\n\t\t\tif( !config.disableLayout ) {\n\n\t\t\t\t// On some mobile devices '100vh' is taller than the visible\n\t\t\t\t// viewport which leads to part of the presentation being\n\t\t\t\t// cut off. To work around this we define our own '--vh' custom\n\t\t\t\t// property where 100x adds up to the correct height.\n\t\t\t\t//\n\t\t\t\t// https://css-tricks.com/the-trick-to-viewport-units-on-mobile/\n\t\t\t\tif( Device.isMobile && !config.embedded ) {\n\t\t\t\t\tdocument.documentElement.style.setProperty( '--vh', ( window.innerHeight * 0.01 ) + 'px' );\n\t\t\t\t}\n\n\t\t\t\tconst size = scrollView.isActive() ?\n\t\t\t\t\t\t\t getComputedSlideSize( viewportWidth, viewportHeight ) :\n\t\t\t\t\t\t\t getComputedSlideSize();\n\n\t\t\t\tconst oldScale = scale;\n\n\t\t\t\t// Layout the contents of the slides\n\t\t\t\tlayoutSlideContents( config.width, config.height );\n\n\t\t\t\tdom.slides.style.width = size.width + 'px';\n\t\t\t\tdom.slides.style.height = size.height + 'px';\n\n\t\t\t\t// Determine scale of content to fit within available space\n\t\t\t\tscale = Math.min( size.presentationWidth / size.width, size.presentationHeight / size.height );\n\n\t\t\t\t// Respect max/min scale settings\n\t\t\t\tscale = Math.max( scale, config.minScale );\n\t\t\t\tscale = Math.min( scale, config.maxScale );\n\n\t\t\t\t// Don't apply any scaling styles if scale is 1 or we're\n\t\t\t\t// in the scroll view\n\t\t\t\tif( scale === 1 || scrollView.isActive() ) {\n\t\t\t\t\tdom.slides.style.zoom = '';\n\t\t\t\t\tdom.slides.style.left = '';\n\t\t\t\t\tdom.slides.style.top = '';\n\t\t\t\t\tdom.slides.style.bottom = '';\n\t\t\t\t\tdom.slides.style.right = '';\n\t\t\t\t\ttransformSlides( { layout: '' } );\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tdom.slides.style.zoom = '';\n\t\t\t\t\tdom.slides.style.left = '50%';\n\t\t\t\t\tdom.slides.style.top = '50%';\n\t\t\t\t\tdom.slides.style.bottom = 'auto';\n\t\t\t\t\tdom.slides.style.right = 'auto';\n\t\t\t\t\ttransformSlides( { layout: 'translate(-50%, -50%) scale('+ scale +')' } );\n\t\t\t\t}\n\n\t\t\t\t// Select all slides, vertical and horizontal\n\t\t\t\tconst slides = Array.from( dom.wrapper.querySelectorAll( SLIDES_SELECTOR ) );\n\n\t\t\t\tfor( let i = 0, len = slides.length; i < len; i++ ) {\n\t\t\t\t\tconst slide = slides[ i ];\n\n\t\t\t\t\t// Don't bother updating invisible slides\n\t\t\t\t\tif( slide.style.display === 'none' ) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif( ( config.center || slide.classList.contains( 'center' ) ) ) {\n\t\t\t\t\t\t// Vertical stacks are not centred since their section\n\t\t\t\t\t\t// children will be\n\t\t\t\t\t\tif( slide.classList.contains( 'stack' ) ) {\n\t\t\t\t\t\t\tslide.style.top = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tslide.style.top = Math.max( ( size.height - slide.scrollHeight ) / 2, 0 ) + 'px';\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tslide.style.top = '';\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif( oldScale !== scale ) {\n\t\t\t\t\tdispatchEvent({\n\t\t\t\t\t\ttype: 'resize',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\toldScale,\n\t\t\t\t\t\t\tscale,\n\t\t\t\t\t\t\tsize\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcheckResponsiveScrollView();\n\n\t\t\tdom.viewport.style.setProperty( '--slide-scale', scale );\n\t\t\tdom.viewport.style.setProperty( '--viewport-width', viewportWidth + 'px' );\n\t\t\tdom.viewport.style.setProperty( '--viewport-height', viewportHeight + 'px' );\n\n\t\t\tscrollView.layout();\n\n\t\t\tprogress.update();\n\t\t\tbackgrounds.updateParallax();\n\n\t\t\tif( overview.isActive() ) {\n\t\t\t\toverview.update();\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Applies layout logic to the contents of all slides in\n\t * the presentation.\n\t *\n\t * @param {string|number} width\n\t * @param {string|number} height\n\t */\n\tfunction layoutSlideContents( width, height ) {\n\t\t// Handle sizing of elements with the 'r-stretch' class\n\t\tUtil.queryAll( dom.slides, 'section > .stretch, section > .r-stretch' ).forEach( element => {\n\n\t\t\t// Determine how much vertical space we can use\n\t\t\tlet remainingHeight = Util.getRemainingHeight( element, height );\n\n\t\t\t// Consider the aspect ratio of media elements\n\t\t\tif( /(img|video)/gi.test( element.nodeName ) ) {\n\t\t\t\tconst nw = element.naturalWidth || element.videoWidth,\n\t\t\t\t\t  nh = element.naturalHeight || element.videoHeight;\n\n\t\t\t\tconst es = Math.min( width / nw, remainingHeight / nh );\n\n\t\t\t\telement.style.width = ( nw * es ) + 'px';\n\t\t\t\telement.style.height = ( nh * es ) + 'px';\n\n\t\t\t}\n\t\t\telse {\n\t\t\t\telement.style.width = width + 'px';\n\t\t\t\telement.style.height = remainingHeight + 'px';\n\t\t\t}\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Responsively activates the scroll mode when we reach the configured\n\t * activation width.\n\t */\n\tfunction checkResponsiveScrollView() {\n\n\t\t// Only proceed if...\n\t\t// 1. The DOM is ready\n\t\t// 2. Layouts aren't disabled via config\n\t\t// 3. We're not currently printing\n\t\t// 4. There is a scrollActivationWidth set\n\t\t// 5. The deck isn't configured to always use the scroll view\n\t\tif(\n\t\t\tdom.wrapper &&\n\t\t\t!config.disableLayout &&\n\t\t\t!printView.isActive() &&\n\t\t\ttypeof config.scrollActivationWidth === 'number' &&\n\t\t\tconfig.view !== 'scroll'\n\t\t) {\n\t\t\tconst size = getComputedSlideSize();\n\n\t\t\tif( size.presentationWidth > 0 && size.presentationWidth <= config.scrollActivationWidth ) {\n\t\t\t\tif( !scrollView.isActive() ) {\n\t\t\t\t\tbackgrounds.create();\n\t\t\t\t\tscrollView.activate()\n\t\t\t\t};\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif( scrollView.isActive() ) scrollView.deactivate();\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Calculates the computed pixel size of our slides. These\n\t * values are based on the width and height configuration\n\t * options.\n\t *\n\t * @param {number} [presentationWidth=dom.wrapper.offsetWidth]\n\t * @param {number} [presentationHeight=dom.wrapper.offsetHeight]\n\t */\n\tfunction getComputedSlideSize( presentationWidth, presentationHeight ) {\n\n\t\tlet width = config.width;\n\t\tlet height = config.height;\n\n\t\tif( config.disableLayout ) {\n\t\t\twidth = dom.slides.offsetWidth;\n\t\t\theight = dom.slides.offsetHeight;\n\t\t}\n\n\t\tconst size = {\n\t\t\t// Slide size\n\t\t\twidth: width,\n\t\t\theight: height,\n\n\t\t\t// Presentation size\n\t\t\tpresentationWidth: presentationWidth || dom.wrapper.offsetWidth,\n\t\t\tpresentationHeight: presentationHeight || dom.wrapper.offsetHeight\n\t\t};\n\n\t\t// Reduce available space by margin\n\t\tsize.presentationWidth -= ( size.presentationWidth * config.margin );\n\t\tsize.presentationHeight -= ( size.presentationHeight * config.margin );\n\n\t\t// Slide width may be a percentage of available width\n\t\tif( typeof size.width === 'string' && /%$/.test( size.width ) ) {\n\t\t\tsize.width = parseInt( size.width, 10 ) / 100 * size.presentationWidth;\n\t\t}\n\n\t\t// Slide height may be a percentage of available height\n\t\tif( typeof size.height === 'string' && /%$/.test( size.height ) ) {\n\t\t\tsize.height = parseInt( size.height, 10 ) / 100 * size.presentationHeight;\n\t\t}\n\n\t\treturn size;\n\n\t}\n\n\t/**\n\t * Stores the vertical index of a stack so that the same\n\t * vertical slide can be selected when navigating to and\n\t * from the stack.\n\t *\n\t * @param {HTMLElement} stack The vertical stack element\n\t * @param {string|number} [v=0] Index to memorize\n\t */\n\tfunction setPreviousVerticalIndex( stack, v ) {\n\n\t\tif( typeof stack === 'object' && typeof stack.setAttribute === 'function' ) {\n\t\t\tstack.setAttribute( 'data-previous-indexv', v || 0 );\n\t\t}\n\n\t}\n\n\t/**\n\t * Retrieves the vertical index which was stored using\n\t * #setPreviousVerticalIndex() or 0 if no previous index\n\t * exists.\n\t *\n\t * @param {HTMLElement} stack The vertical stack element\n\t */\n\tfunction getPreviousVerticalIndex( stack ) {\n\n\t\tif( typeof stack === 'object' && typeof stack.setAttribute === 'function' && stack.classList.contains( 'stack' ) ) {\n\t\t\t// Prefer manually defined start-indexv\n\t\t\tconst attributeName = stack.hasAttribute( 'data-start-indexv' ) ? 'data-start-indexv' : 'data-previous-indexv';\n\n\t\t\treturn parseInt( stack.getAttribute( attributeName ) || 0, 10 );\n\t\t}\n\n\t\treturn 0;\n\n\t}\n\n\t/**\n\t * Checks if the current or specified slide is vertical\n\t * (nested within another slide).\n\t *\n\t * @param {HTMLElement} [slide=currentSlide] The slide to check\n\t * orientation of\n\t * @return {Boolean}\n\t */\n\tfunction isVerticalSlide( slide = currentSlide ) {\n\n\t\treturn slide && slide.parentNode && !!slide.parentNode.nodeName.match( /section/i );\n\n\t}\n\n\t/**\n\t * Checks if the current or specified slide is a stack containing\n\t * vertical slides.\n\t *\n\t * @param {HTMLElement} [slide=currentSlide]\n\t * @return {Boolean}\n\t */\n\tfunction isVerticalStack( slide = currentSlide ) {\n\n\t\treturn slide.classList.contains( '.stack' ) || slide.querySelector( 'section' ) !== null;\n\n\t}\n\n\t/**\n\t * Returns true if we're on the last slide in the current\n\t * vertical stack.\n\t */\n\tfunction isLastVerticalSlide() {\n\n\t\tif( currentSlide && isVerticalSlide( currentSlide ) ) {\n\t\t\t// Does this slide have a next sibling?\n\t\t\tif( currentSlide.nextElementSibling ) return false;\n\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t/**\n\t * Returns true if we're currently on the first slide in\n\t * the presentation.\n\t */\n\tfunction isFirstSlide() {\n\n\t\treturn indexh === 0 && indexv === 0;\n\n\t}\n\n\t/**\n\t * Returns true if we're currently on the last slide in\n\t * the presenation. If the last slide is a stack, we only\n\t * consider this the last slide if it's at the end of the\n\t * stack.\n\t */\n\tfunction isLastSlide() {\n\n\t\tif( currentSlide ) {\n\t\t\t// Does this slide have a next sibling?\n\t\t\tif( currentSlide.nextElementSibling ) return false;\n\n\t\t\t// If it's vertical, does its parent have a next sibling?\n\t\t\tif( isVerticalSlide( currentSlide ) && currentSlide.parentNode.nextElementSibling ) return false;\n\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\n\t}\n\n\t/**\n\t * Enters the paused mode which fades everything on screen to\n\t * black.\n\t */\n\tfunction pause() {\n\n\t\tif( config.pause ) {\n\t\t\tconst wasPaused = dom.wrapper.classList.contains( 'paused' );\n\n\t\t\tcancelAutoSlide();\n\t\t\tdom.wrapper.classList.add( 'paused' );\n\n\t\t\tif( wasPaused === false ) {\n\t\t\t\tdispatchEvent({ type: 'paused' });\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Exits from the paused mode.\n\t */\n\tfunction resume() {\n\n\t\tconst wasPaused = dom.wrapper.classList.contains( 'paused' );\n\t\tdom.wrapper.classList.remove( 'paused' );\n\n\t\tcueAutoSlide();\n\n\t\tif( wasPaused ) {\n\t\t\tdispatchEvent({ type: 'resumed' });\n\t\t}\n\n\t}\n\n\t/**\n\t * Toggles the paused mode on and off.\n\t */\n\tfunction togglePause( override ) {\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? pause() : resume();\n\t\t}\n\t\telse {\n\t\t\tisPaused() ? resume() : pause();\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if we are currently in the paused mode.\n\t *\n\t * @return {Boolean}\n\t */\n\tfunction isPaused() {\n\n\t\treturn dom.wrapper.classList.contains( 'paused' );\n\n\t}\n\n\t/**\n\t * Toggles visibility of the jump-to-slide UI.\n\t */\n\tfunction toggleJumpToSlide( override ) {\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? jumpToSlide.show() : jumpToSlide.hide();\n\t\t}\n\t\telse {\n\t\t\tjumpToSlide.isVisible() ? jumpToSlide.hide() : jumpToSlide.show();\n\t\t}\n\n\t}\n\n\t/**\n\t * Toggles the auto slide mode on and off.\n\t *\n\t * @param {Boolean} [override] Flag which sets the desired state.\n\t * True means autoplay starts, false means it stops.\n\t */\n\n\tfunction toggleAutoSlide( override ) {\n\n\t\tif( typeof override === 'boolean' ) {\n\t\t\toverride ? resumeAutoSlide() : pauseAutoSlide();\n\t\t}\n\n\t\telse {\n\t\t\tautoSlidePaused ? resumeAutoSlide() : pauseAutoSlide();\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks if the auto slide mode is currently on.\n\t *\n\t * @return {Boolean}\n\t */\n\tfunction isAutoSliding() {\n\n\t\treturn !!( autoSlide && !autoSlidePaused );\n\n\t}\n\n\t/**\n\t * Steps from the current point in the presentation to the\n\t * slide which matches the specified horizontal and vertical\n\t * indices.\n\t *\n\t * @param {number} [h=indexh] Horizontal index of the target slide\n\t * @param {number} [v=indexv] Vertical index of the target slide\n\t * @param {number} [f] Index of a fragment within the\n\t * target slide to activate\n\t * @param {number} [origin] Origin for use in multimaster environments\n\t */\n\tfunction slide( h, v, f, origin ) {\n\n\t\t// Dispatch an event before the slide\n\t\tconst slidechange = dispatchEvent({\n\t\t\ttype: 'beforeslidechange',\n\t\t\tdata: {\n\t\t\t\tindexh: h === undefined ? indexh : h,\n\t\t\t\tindexv: v === undefined ? indexv : v,\n\t\t\t\torigin\n\t\t\t}\n\t\t});\n\n\t\t// Abort if this slide change was prevented by an event listener\n\t\tif( slidechange.defaultPrevented ) return;\n\n\t\t// Remember where we were at before\n\t\tpreviousSlide = currentSlide;\n\n\t\t// Query all horizontal slides in the deck\n\t\tconst horizontalSlides = dom.wrapper.querySelectorAll( HORIZONTAL_SLIDES_SELECTOR );\n\n\t\t// If we're in scroll mode, we scroll the target slide into view\n\t\t// instead of running our standard slide transition\n\t\tif( scrollView.isActive() ) {\n\t\t\tconst scrollToSlide = scrollView.getSlideByIndices( h, v );\n\t\t\tif( scrollToSlide ) scrollView.scrollToSlide( scrollToSlide );\n\t\t\treturn;\n\t\t}\n\n\t\t// Abort if there are no slides\n\t\tif( horizontalSlides.length === 0 ) return;\n\n\t\t// If no vertical index is specified and the upcoming slide is a\n\t\t// stack, resume at its previous vertical index\n\t\tif( v === undefined && !overview.isActive() ) {\n\t\t\tv = getPreviousVerticalIndex( horizontalSlides[ h ] );\n\t\t}\n\n\t\t// If we were on a vertical stack, remember what vertical index\n\t\t// it was on so we can resume at the same position when returning\n\t\tif( previousSlide && previousSlide.parentNode && previousSlide.parentNode.classList.contains( 'stack' ) ) {\n\t\t\tsetPreviousVerticalIndex( previousSlide.parentNode, indexv );\n\t\t}\n\n\t\t// Remember the state before this slide\n\t\tconst stateBefore = state.concat();\n\n\t\t// Reset the state array\n\t\tstate.length = 0;\n\n\t\tlet indexhBefore = indexh || 0,\n\t\t\tindexvBefore = indexv || 0;\n\n\t\t// Activate and transition to the new slide\n\t\tindexh = updateSlides( HORIZONTAL_SLIDES_SELECTOR, h === undefined ? indexh : h );\n\t\tindexv = updateSlides( VERTICAL_SLIDES_SELECTOR, v === undefined ? indexv : v );\n\n\t\t// Dispatch an event if the slide changed\n\t\tlet slideChanged = ( indexh !== indexhBefore || indexv !== indexvBefore );\n\n\t\t// Ensure that the previous slide is never the same as the current\n\t\tif( !slideChanged ) previousSlide = null;\n\n\t\t// Find the current horizontal slide and any possible vertical slides\n\t\t// within it\n\t\tlet currentHorizontalSlide = horizontalSlides[ indexh ],\n\t\t\tcurrentVerticalSlides = currentHorizontalSlide.querySelectorAll( 'section' );\n\n\t\t// Indicate when we're on a vertical slide\n\t\trevealElement.classList.toggle( 'is-vertical-slide', currentVerticalSlides.length > 1 );\n\n\t\t// Store references to the previous and current slides\n\t\tcurrentSlide = currentVerticalSlides[ indexv ] || currentHorizontalSlide;\n\n\t\tlet autoAnimateTransition = false;\n\n\t\t// Detect if we're moving between two auto-animated slides\n\t\tif( slideChanged && previousSlide && currentSlide && !overview.isActive() ) {\n\t\t\ttransition = 'running';\n\n\t\t\tautoAnimateTransition = shouldAutoAnimateBetween( previousSlide, currentSlide, indexhBefore, indexvBefore );\n\n\t\t\t// If this is an auto-animated transition, we disable the\n\t\t\t// regular slide transition\n\t\t\t//\n\t\t\t// Note 20-03-2020:\n\t\t\t// This needs to happen before we update slide visibility,\n\t\t\t// otherwise transitions will still run in Safari.\n\t\t\tif( autoAnimateTransition ) {\n\t\t\t\tdom.slides.classList.add( 'disable-slide-transitions' )\n\t\t\t}\n\t\t}\n\n\t\t// Update the visibility of slides now that the indices have changed\n\t\tupdateSlidesVisibility();\n\n\t\tlayout();\n\n\t\t// Update the overview if it's currently active\n\t\tif( overview.isActive() ) {\n\t\t\toverview.update();\n\t\t}\n\n\t\t// Show fragment, if specified\n\t\tif( typeof f !== 'undefined' ) {\n\t\t\tfragments.goto( f );\n\t\t}\n\n\t\t// Solves an edge case where the previous slide maintains the\n\t\t// 'present' class when navigating between adjacent vertical\n\t\t// stacks\n\t\tif( previousSlide && previousSlide !== currentSlide ) {\n\t\t\tpreviousSlide.classList.remove( 'present' );\n\t\t\tpreviousSlide.setAttribute( 'aria-hidden', 'true' );\n\n\t\t\t// Reset all slides upon navigate to home\n\t\t\tif( isFirstSlide() ) {\n\t\t\t\t// Launch async task\n\t\t\t\tsetTimeout( () => {\n\t\t\t\t\tgetVerticalStacks().forEach( slide => {\n\t\t\t\t\t\tsetPreviousVerticalIndex( slide, 0 );\n\t\t\t\t\t} );\n\t\t\t\t}, 0 );\n\t\t\t}\n\t\t}\n\n\t\t// Apply the new state\n\t\tstateLoop: for( let i = 0, len = state.length; i < len; i++ ) {\n\t\t\t// Check if this state existed on the previous slide. If it\n\t\t\t// did, we will avoid adding it repeatedly\n\t\t\tfor( let j = 0; j < stateBefore.length; j++ ) {\n\t\t\t\tif( stateBefore[j] === state[i] ) {\n\t\t\t\t\tstateBefore.splice( j, 1 );\n\t\t\t\t\tcontinue stateLoop;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tdom.viewport.classList.add( state[i] );\n\n\t\t\t// Dispatch custom event matching the state's name\n\t\t\tdispatchEvent({ type: state[i] });\n\t\t}\n\n\t\t// Clean up the remains of the previous state\n\t\twhile( stateBefore.length ) {\n\t\t\tdom.viewport.classList.remove( stateBefore.pop() );\n\t\t}\n\n\t\tif( slideChanged ) {\n\t\t\tdispatchSlideChanged( origin );\n\t\t}\n\n\t\t// Handle embedded content\n\t\tif( slideChanged || !previousSlide ) {\n\t\t\tslideContent.stopEmbeddedContent( previousSlide );\n\t\t\tslideContent.startEmbeddedContent( currentSlide );\n\t\t}\n\n\t\t// Announce the current slide contents to screen readers\n\t\t// Use animation frame to prevent getComputedStyle in getStatusText\n\t\t// from triggering layout mid-frame\n\t\trequestAnimationFrame( () => {\n\t\t\tannounceStatus( getStatusText( currentSlide ) );\n\t\t});\n\n\t\tprogress.update();\n\t\tcontrols.update();\n\t\tnotes.update();\n\t\tbackgrounds.update();\n\t\tbackgrounds.updateParallax();\n\t\tslideNumber.update();\n\t\tfragments.update();\n\n\t\t// Update the URL hash\n\t\tlocation.writeURL();\n\n\t\tcueAutoSlide();\n\n\t\t// Auto-animation\n\t\tif( autoAnimateTransition ) {\n\n\t\t\tsetTimeout( () => {\n\t\t\t\tdom.slides.classList.remove( 'disable-slide-transitions' );\n\t\t\t}, 0 );\n\n\t\t\tif( config.autoAnimate ) {\n\t\t\t\t// Run the auto-animation between our slides\n\t\t\t\tautoAnimate.run( previousSlide, currentSlide );\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Checks whether or not an auto-animation should take place between\n\t * the two given slides.\n\t *\n\t * @param {HTMLElement} fromSlide\n\t * @param {HTMLElement} toSlide\n\t * @param {number} indexhBefore\n\t * @param {number} indexvBefore\n\t *\n\t * @returns {boolean}\n\t */\n\tfunction shouldAutoAnimateBetween( fromSlide, toSlide, indexhBefore, indexvBefore ) {\n\n\t\treturn \tfromSlide.hasAttribute( 'data-auto-animate' ) && toSlide.hasAttribute( 'data-auto-animate' ) &&\n\t\t\t\tfromSlide.getAttribute( 'data-auto-animate-id' ) === toSlide.getAttribute( 'data-auto-animate-id' ) &&\n\t\t\t\t!( ( indexh > indexhBefore || indexv > indexvBefore ) ? toSlide : fromSlide ).hasAttribute( 'data-auto-animate-restart' );\n\n\t}\n\n\t/**\n\t * Called anytime a new slide should be activated while in the scroll\n\t * view. The active slide is the page that occupies the most space in\n\t * the scrollable viewport.\n\t *\n\t * @param {number} pageIndex\n\t * @param {HTMLElement} slideElement\n\t */\n\tfunction setCurrentScrollPage( slideElement, h, v ) {\n\n\t\tlet indexhBefore = indexh || 0;\n\n\t\tindexh = h;\n\t\tindexv = v;\n\n\t\tconst slideChanged = currentSlide !== slideElement;\n\n\t\tpreviousSlide = currentSlide;\n\t\tcurrentSlide = slideElement;\n\n\t\tif( currentSlide && previousSlide ) {\n\t\t\tif( config.autoAnimate && shouldAutoAnimateBetween( previousSlide, currentSlide, indexhBefore, indexv ) ) {\n\t\t\t\t// Run the auto-animation between our slides\n\t\t\t\tautoAnimate.run( previousSlide, currentSlide );\n\t\t\t}\n\t\t}\n\n\t\t// Start or stop embedded content like videos and iframes\n\t\tif( slideChanged ) {\n\t\t\tif( previousSlide ) {\n\t\t\t\tslideContent.stopEmbeddedContent( previousSlide );\n\t\t\t\tslideContent.stopEmbeddedContent( previousSlide.slideBackgroundElement );\n\t\t\t}\n\n\t\t\tslideContent.startEmbeddedContent( currentSlide );\n\t\t\tslideContent.startEmbeddedContent( currentSlide.slideBackgroundElement );\n\t\t}\n\n\t\trequestAnimationFrame( () => {\n\t\t\tannounceStatus( getStatusText( currentSlide ) );\n\t\t});\n\n\t\tdispatchSlideChanged();\n\n\t}\n\n\t/**\n\t * Syncs the presentation with the current DOM. Useful\n\t * when new slides or control elements are added or when\n\t * the configuration has changed.\n\t */\n\tfunction sync() {\n\n\t\t// Subscribe to input\n\t\tremoveEventListeners();\n\t\taddEventListeners();\n\n\t\t// Force a layout to make sure the current config is accounted for\n\t\tlayout();\n\n\t\t// Reflect the current autoSlide value\n\t\tautoSlide = config.autoSlide;\n\n\t\t// Start auto-sliding if it's enabled\n\t\tcueAutoSlide();\n\n\t\t// Re-create all slide backgrounds\n\t\tbackgrounds.create();\n\n\t\t// Write the current hash to the URL\n\t\tlocation.writeURL();\n\n\t\tif( config.sortFragmentsOnSync === true ) {\n\t\t\tfragments.sortAll();\n\t\t}\n\n\t\tcontrols.update();\n\t\tprogress.update();\n\n\t\tupdateSlidesVisibility();\n\n\t\tnotes.update();\n\t\tnotes.updateVisibility();\n\t\tbackgrounds.update( true );\n\t\tslideNumber.update();\n\t\tslideContent.formatEmbeddedContent();\n\n\t\t// Start or stop embedded content depending on global config\n\t\tif( config.autoPlayMedia === false ) {\n\t\t\tslideContent.stopEmbeddedContent( currentSlide, { unloadIframes: false } );\n\t\t}\n\t\telse {\n\t\t\tslideContent.startEmbeddedContent( currentSlide );\n\t\t}\n\n\t\tif( overview.isActive() ) {\n\t\t\toverview.layout();\n\t\t}\n\n\t}\n\n\t/**\n\t * Updates reveal.js to keep in sync with new slide attributes. For\n\t * example, if you add a new `data-background-image` you can call\n\t * this to have reveal.js render the new background image.\n\t *\n\t * Similar to #sync() but more efficient when you only need to\n\t * refresh a specific slide.\n\t *\n\t * @param {HTMLElement} slide\n\t */\n\tfunction syncSlide( slide = currentSlide ) {\n\n\t\tbackgrounds.sync( slide );\n\t\tfragments.sync( slide );\n\n\t\tslideContent.load( slide );\n\n\t\tbackgrounds.update();\n\t\tnotes.update();\n\n\t}\n\n\t/**\n\t * Resets all vertical slides so that only the first\n\t * is visible.\n\t */\n\tfunction resetVerticalSlides() {\n\n\t\tgetHorizontalSlides().forEach( horizontalSlide => {\n\n\t\t\tUtil.queryAll( horizontalSlide, 'section' ).forEach( ( verticalSlide, y ) => {\n\n\t\t\t\tif( y > 0 ) {\n\t\t\t\t\tverticalSlide.classList.remove( 'present' );\n\t\t\t\t\tverticalSlide.classList.remove( 'past' );\n\t\t\t\t\tverticalSlide.classList.add( 'future' );\n\t\t\t\t\tverticalSlide.setAttribute( 'aria-hidden', 'true' );\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Randomly shuffles all slides in the deck.\n\t */\n\tfunction shuffle( slides = getHorizontalSlides() ) {\n\n\t\tslides.forEach( ( slide, i ) => {\n\n\t\t\t// Insert the slide next to a randomly picked sibling slide\n\t\t\t// slide. This may cause the slide to insert before itself,\n\t\t\t// but that's not an issue.\n\t\t\tlet beforeSlide = slides[ Math.floor( Math.random() * slides.length ) ];\n\t\t\tif( beforeSlide.parentNode === slide.parentNode ) {\n\t\t\t\tslide.parentNode.insertBefore( slide, beforeSlide );\n\t\t\t}\n\n\t\t\t// Randomize the order of vertical slides (if there are any)\n\t\t\tlet verticalSlides = slide.querySelectorAll( 'section' );\n\t\t\tif( verticalSlides.length ) {\n\t\t\t\tshuffle( verticalSlides );\n\t\t\t}\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Updates one dimension of slides by showing the slide\n\t * with the specified index.\n\t *\n\t * @param {string} selector A CSS selector that will fetch\n\t * the group of slides we are working with\n\t * @param {number} index The index of the slide that should be\n\t * shown\n\t *\n\t * @return {number} The index of the slide that is now shown,\n\t * might differ from the passed in index if it was out of\n\t * bounds.\n\t */\n\tfunction updateSlides( selector, index ) {\n\n\t\t// Select all slides and convert the NodeList result to\n\t\t// an array\n\t\tlet slides = Util.queryAll( dom.wrapper, selector ),\n\t\t\tslidesLength = slides.length;\n\n\t\tlet printMode = scrollView.isActive() || printView.isActive();\n\t\tlet loopedForwards = false;\n\t\tlet loopedBackwards = false;\n\n\t\tif( slidesLength ) {\n\n\t\t\t// Should the index loop?\n\t\t\tif( config.loop ) {\n\t\t\t\tif( index >= slidesLength ) loopedForwards = true;\n\n\t\t\t\tindex %= slidesLength;\n\n\t\t\t\tif( index < 0 ) {\n\t\t\t\t\tindex = slidesLength + index;\n\t\t\t\t\tloopedBackwards = true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Enforce max and minimum index bounds\n\t\t\tindex = Math.max( Math.min( index, slidesLength - 1 ), 0 );\n\n\t\t\tfor( let i = 0; i < slidesLength; i++ ) {\n\t\t\t\tlet element = slides[i];\n\n\t\t\t\tlet reverse = config.rtl && !isVerticalSlide( element );\n\n\t\t\t\t// Avoid .remove() with multiple args for IE11 support\n\t\t\t\telement.classList.remove( 'past' );\n\t\t\t\telement.classList.remove( 'present' );\n\t\t\t\telement.classList.remove( 'future' );\n\n\t\t\t\t// http://www.w3.org/html/wg/drafts/html/master/editing.html#the-hidden-attribute\n\t\t\t\telement.setAttribute( 'hidden', '' );\n\t\t\t\telement.setAttribute( 'aria-hidden', 'true' );\n\n\t\t\t\t// If this element contains vertical slides\n\t\t\t\tif( element.querySelector( 'section' ) ) {\n\t\t\t\t\telement.classList.add( 'stack' );\n\t\t\t\t}\n\n\t\t\t\t// If we're printing static slides, all slides are \"present\"\n\t\t\t\tif( printMode ) {\n\t\t\t\t\telement.classList.add( 'present' );\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif( i < index ) {\n\t\t\t\t\t// Any element previous to index is given the 'past' class\n\t\t\t\t\telement.classList.add( reverse ? 'future' : 'past' );\n\n\t\t\t\t\tif( config.fragments ) {\n\t\t\t\t\t\t// Show all fragments in prior slides\n\t\t\t\t\t\tshowFragmentsIn( element );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse if( i > index ) {\n\t\t\t\t\t// Any element subsequent to index is given the 'future' class\n\t\t\t\t\telement.classList.add( reverse ? 'past' : 'future' );\n\n\t\t\t\t\tif( config.fragments ) {\n\t\t\t\t\t\t// Hide all fragments in future slides\n\t\t\t\t\t\thideFragmentsIn( element );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Update the visibility of fragments when a presentation loops\n\t\t\t\t// in either direction\n\t\t\t\telse if( i === index && config.fragments ) {\n\t\t\t\t\tif( loopedForwards ) {\n\t\t\t\t\t\thideFragmentsIn( element );\n\t\t\t\t\t}\n\t\t\t\t\telse if( loopedBackwards ) {\n\t\t\t\t\t\tshowFragmentsIn( element );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet slide = slides[index];\n\t\t\tlet wasPresent = slide.classList.contains( 'present' );\n\n\t\t\t// Mark the current slide as present\n\t\t\tslide.classList.add( 'present' );\n\t\t\tslide.removeAttribute( 'hidden' );\n\t\t\tslide.removeAttribute( 'aria-hidden' );\n\n\t\t\tif( !wasPresent ) {\n\t\t\t\t// Dispatch an event indicating the slide is now visible\n\t\t\t\tdispatchEvent({\n\t\t\t\t\ttarget: slide,\n\t\t\t\t\ttype: 'visible',\n\t\t\t\t\tbubbles: false\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// If this slide has a state associated with it, add it\n\t\t\t// onto the current state of the deck\n\t\t\tlet slideState = slide.getAttribute( 'data-state' );\n\t\t\tif( slideState ) {\n\t\t\t\tstate = state.concat( slideState.split( ' ' ) );\n\t\t\t}\n\n\t\t}\n\t\telse {\n\t\t\t// Since there are no slides we can't be anywhere beyond the\n\t\t\t// zeroth index\n\t\t\tindex = 0;\n\t\t}\n\n\t\treturn index;\n\n\t}\n\n\t/**\n\t * Shows all fragment elements within the given container.\n\t */\n\tfunction showFragmentsIn( container ) {\n\n\t\tUtil.queryAll( container, '.fragment' ).forEach( fragment => {\n\t\t\tfragment.classList.add( 'visible' );\n\t\t\tfragment.classList.remove( 'current-fragment' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Hides all fragment elements within the given container.\n\t */\n\tfunction hideFragmentsIn( container ) {\n\n\t\tUtil.queryAll( container, '.fragment.visible' ).forEach( fragment => {\n\t\t\tfragment.classList.remove( 'visible', 'current-fragment' );\n\t\t} );\n\n\t}\n\n\t/**\n\t * Optimization method; hide all slides that are far away\n\t * from the present slide.\n\t */\n\tfunction updateSlidesVisibility() {\n\n\t\t// Select all slides and convert the NodeList result to\n\t\t// an array\n\t\tlet horizontalSlides = getHorizontalSlides(),\n\t\t\thorizontalSlidesLength = horizontalSlides.length,\n\t\t\tdistanceX,\n\t\t\tdistanceY;\n\n\t\tif( horizontalSlidesLength && typeof indexh !== 'undefined' ) {\n\n\t\t\t// The number of steps away from the present slide that will\n\t\t\t// be visible\n\t\t\tlet viewDistance = overview.isActive() ? 10 : config.viewDistance;\n\n\t\t\t// Shorten the view distance on devices that typically have\n\t\t\t// less resources\n\t\t\tif( Device.isMobile ) {\n\t\t\t\tviewDistance = overview.isActive() ? 6 : config.mobileViewDistance;\n\t\t\t}\n\n\t\t\t// All slides need to be visible when exporting to PDF\n\t\t\tif( printView.isActive() ) {\n\t\t\t\tviewDistance = Number.MAX_VALUE;\n\t\t\t}\n\n\t\t\tfor( let x = 0; x < horizontalSlidesLength; x++ ) {\n\t\t\t\tlet horizontalSlide = horizontalSlides[x];\n\n\t\t\t\tlet verticalSlides = Util.queryAll( horizontalSlide, 'section' ),\n\t\t\t\t\tverticalSlidesLength = verticalSlides.length;\n\n\t\t\t\t// Determine how far away this slide is from the present\n\t\t\t\tdistanceX = Math.abs( ( indexh || 0 ) - x ) || 0;\n\n\t\t\t\t// If the presentation is looped, distance should measure\n\t\t\t\t// 1 between the first and last slides\n\t\t\t\tif( config.loop ) {\n\t\t\t\t\tdistanceX = Math.abs( ( ( indexh || 0 ) - x ) % ( horizontalSlidesLength - viewDistance ) ) || 0;\n\t\t\t\t}\n\n\t\t\t\t// Show the horizontal slide if it's within the view distance\n\t\t\t\tif( distanceX < viewDistance ) {\n\t\t\t\t\tslideContent.load( horizontalSlide );\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tslideContent.unload( horizontalSlide );\n\t\t\t\t}\n\n\t\t\t\tif( verticalSlidesLength ) {\n\n\t\t\t\t\tlet oy = getPreviousVerticalIndex( horizontalSlide );\n\n\t\t\t\t\tfor( let y = 0; y < verticalSlidesLength; y++ ) {\n\t\t\t\t\t\tlet verticalSlide = verticalSlides[y];\n\n\t\t\t\t\t\tdistanceY = x === ( indexh || 0 ) ? Math.abs( ( indexv || 0 ) - y ) : Math.abs( y - oy );\n\n\t\t\t\t\t\tif( distanceX + distanceY < viewDistance ) {\n\t\t\t\t\t\t\tslideContent.load( verticalSlide );\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tslideContent.unload( verticalSlide );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Flag if there are ANY vertical slides, anywhere in the deck\n\t\t\tif( hasVerticalSlides() ) {\n\t\t\t\tdom.wrapper.classList.add( 'has-vertical-slides' );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tdom.wrapper.classList.remove( 'has-vertical-slides' );\n\t\t\t}\n\n\t\t\t// Flag if there are ANY horizontal slides, anywhere in the deck\n\t\t\tif( hasHorizontalSlides() ) {\n\t\t\t\tdom.wrapper.classList.add( 'has-horizontal-slides' );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tdom.wrapper.classList.remove( 'has-horizontal-slides' );\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Determine what available routes there are for navigation.\n\t *\n\t * @return {{left: boolean, right: boolean, up: boolean, down: boolean}}\n\t */\n\tfunction availableRoutes({ includeFragments = false } = {}) {\n\n\t\tlet horizontalSlides = dom.wrapper.querySelectorAll( HORIZONTAL_SLIDES_SELECTOR ),\n\t\t\tverticalSlides = dom.wrapper.querySelectorAll( VERTICAL_SLIDES_SELECTOR );\n\n\t\tlet routes = {\n\t\t\tleft: indexh > 0,\n\t\t\tright: indexh < horizontalSlides.length - 1,\n\t\t\tup: indexv > 0,\n\t\t\tdown: indexv < verticalSlides.length - 1\n\t\t};\n\n\t\t// Looped presentations can always be navigated as long as\n\t\t// there are slides available\n\t\tif( config.loop ) {\n\t\t\tif( horizontalSlides.length > 1 ) {\n\t\t\t\troutes.left = true;\n\t\t\t\troutes.right = true;\n\t\t\t}\n\n\t\t\tif( verticalSlides.length > 1 ) {\n\t\t\t\troutes.up = true;\n\t\t\t\troutes.down = true;\n\t\t\t}\n\t\t}\n\n\t\tif ( horizontalSlides.length > 1 && config.navigationMode === 'linear' ) {\n\t\t\troutes.right = routes.right || routes.down;\n\t\t\troutes.left = routes.left || routes.up;\n\t\t}\n\n\t\t// If includeFragments is set, a route will be considered\n\t\t// available if either a slid OR fragment is available in\n\t\t// the given direction\n\t\tif( includeFragments === true ) {\n\t\t\tlet fragmentRoutes = fragments.availableRoutes();\n\t\t\troutes.left = routes.left || fragmentRoutes.prev;\n\t\t\troutes.up = routes.up || fragmentRoutes.prev;\n\t\t\troutes.down = routes.down || fragmentRoutes.next;\n\t\t\troutes.right = routes.right || fragmentRoutes.next;\n\t\t}\n\n\t\t// Reverse horizontal controls for rtl\n\t\tif( config.rtl ) {\n\t\t\tlet left = routes.left;\n\t\t\troutes.left = routes.right;\n\t\t\troutes.right = left;\n\t\t}\n\n\t\treturn routes;\n\n\t}\n\n\t/**\n\t * Returns the number of past slides. This can be used as a global\n\t * flattened index for slides.\n\t *\n\t * @param {HTMLElement} [slide=currentSlide] The slide we're counting before\n\t *\n\t * @return {number} Past slide count\n\t */\n\tfunction getSlidePastCount( slide = currentSlide ) {\n\n\t\tlet horizontalSlides = getHorizontalSlides();\n\n\t\t// The number of past slides\n\t\tlet pastCount = 0;\n\n\t\t// Step through all slides and count the past ones\n\t\tmainLoop: for( let i = 0; i < horizontalSlides.length; i++ ) {\n\n\t\t\tlet horizontalSlide = horizontalSlides[i];\n\t\t\tlet verticalSlides = horizontalSlide.querySelectorAll( 'section' );\n\n\t\t\tfor( let j = 0; j < verticalSlides.length; j++ ) {\n\n\t\t\t\t// Stop as soon as we arrive at the present\n\t\t\t\tif( verticalSlides[j] === slide ) {\n\t\t\t\t\tbreak mainLoop;\n\t\t\t\t}\n\n\t\t\t\t// Don't count slides with the \"uncounted\" class\n\t\t\t\tif( verticalSlides[j].dataset.visibility !== 'uncounted' ) {\n\t\t\t\t\tpastCount++;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Stop as soon as we arrive at the present\n\t\t\tif( horizontalSlide === slide ) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\t// Don't count the wrapping section for vertical slides and\n\t\t\t// slides marked as uncounted\n\t\t\tif( horizontalSlide.classList.contains( 'stack' ) === false && horizontalSlide.dataset.visibility !== 'uncounted' ) {\n\t\t\t\tpastCount++;\n\t\t\t}\n\n\t\t}\n\n\t\treturn pastCount;\n\n\t}\n\n\t/**\n\t * Returns a value ranging from 0-1 that represents\n\t * how far into the presentation we have navigated.\n\t *\n\t * @return {number}\n\t */\n\tfunction getProgress() {\n\n\t\t// The number of past and total slides\n\t\tlet totalCount = getTotalSlides();\n\t\tlet pastCount = getSlidePastCount();\n\n\t\tif( currentSlide ) {\n\n\t\t\tlet allFragments = currentSlide.querySelectorAll( '.fragment' );\n\n\t\t\t// If there are fragments in the current slide those should be\n\t\t\t// accounted for in the progress.\n\t\t\tif( allFragments.length > 0 ) {\n\t\t\t\tlet visibleFragments = currentSlide.querySelectorAll( '.fragment.visible' );\n\n\t\t\t\t// This value represents how big a portion of the slide progress\n\t\t\t\t// that is made up by its fragments (0-1)\n\t\t\t\tlet fragmentWeight = 0.9;\n\n\t\t\t\t// Add fragment progress to the past slide count\n\t\t\t\tpastCount += ( visibleFragments.length / allFragments.length ) * fragmentWeight;\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.min( pastCount / ( totalCount - 1 ), 1 );\n\n\t}\n\n\t/**\n\t * Retrieves the h/v location and fragment of the current,\n\t * or specified, slide.\n\t *\n\t * @param {HTMLElement} [slide] If specified, the returned\n\t * index will be for this slide rather than the currently\n\t * active one\n\t *\n\t * @return {{h: number, v: number, f: number}}\n\t */\n\tfunction getIndices( slide ) {\n\n\t\t// By default, return the current indices\n\t\tlet h = indexh,\n\t\t\tv = indexv,\n\t\t\tf;\n\n\t\t// If a slide is specified, return the indices of that slide\n\t\tif( slide ) {\n\t\t\t// In scroll mode the original h/x index is stored on the slide\n\t\t\tif( scrollView.isActive() ) {\n\t\t\t\th = parseInt( slide.getAttribute( 'data-index-h' ), 10 );\n\n\t\t\t\tif( slide.getAttribute( 'data-index-v' ) ) {\n\t\t\t\t\tv = parseInt( slide.getAttribute( 'data-index-v' ), 10 );\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet isVertical = isVerticalSlide( slide );\n\t\t\t\tlet slideh = isVertical ? slide.parentNode : slide;\n\n\t\t\t\t// Select all horizontal slides\n\t\t\t\tlet horizontalSlides = getHorizontalSlides();\n\n\t\t\t\t// Now that we know which the horizontal slide is, get its index\n\t\t\t\th = Math.max( horizontalSlides.indexOf( slideh ), 0 );\n\n\t\t\t\t// Assume we're not vertical\n\t\t\t\tv = undefined;\n\n\t\t\t\t// If this is a vertical slide, grab the vertical index\n\t\t\t\tif( isVertical ) {\n\t\t\t\t\tv = Math.max( Util.queryAll( slide.parentNode, 'section' ).indexOf( slide ), 0 );\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif( !slide && currentSlide ) {\n\t\t\tlet hasFragments = currentSlide.querySelectorAll( '.fragment' ).length > 0;\n\t\t\tif( hasFragments ) {\n\t\t\t\tlet currentFragment = currentSlide.querySelector( '.current-fragment' );\n\t\t\t\tif( currentFragment && currentFragment.hasAttribute( 'data-fragment-index' ) ) {\n\t\t\t\t\tf = parseInt( currentFragment.getAttribute( 'data-fragment-index' ), 10 );\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tf = currentSlide.querySelectorAll( '.fragment.visible' ).length - 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn { h, v, f };\n\n\t}\n\n\t/**\n\t * Retrieves all slides in this presentation.\n\t */\n\tfunction getSlides() {\n\n\t\treturn Util.queryAll( dom.wrapper, SLIDES_SELECTOR + ':not(.stack):not([data-visibility=\"uncounted\"])' );\n\n\t}\n\n\t/**\n\t * Returns a list of all horizontal slides in the deck. Each\n\t * vertical stack is included as one horizontal slide in the\n\t * resulting array.\n\t */\n\tfunction getHorizontalSlides() {\n\n\t\treturn Util.queryAll( dom.wrapper, HORIZONTAL_SLIDES_SELECTOR );\n\n\t}\n\n\t/**\n\t * Returns all vertical slides that exist within this deck.\n\t */\n\tfunction getVerticalSlides() {\n\n\t\treturn Util.queryAll( dom.wrapper, '.slides>section>section' );\n\n\t}\n\n\t/**\n\t * Returns all vertical stacks (each stack can contain multiple slides).\n\t */\n\tfunction getVerticalStacks() {\n\n\t\treturn Util.queryAll( dom.wrapper, HORIZONTAL_SLIDES_SELECTOR + '.stack');\n\n\t}\n\n\t/**\n\t * Returns true if there are at least two horizontal slides.\n\t */\n\tfunction hasHorizontalSlides() {\n\n\t\treturn getHorizontalSlides().length > 1;\n\t}\n\n\t/**\n\t * Returns true if there are at least two vertical slides.\n\t */\n\tfunction hasVerticalSlides() {\n\n\t\treturn getVerticalSlides().length > 1;\n\n\t}\n\n\t/**\n\t * Returns an array of objects where each object represents the\n\t * attributes on its respective slide.\n\t */\n\tfunction getSlidesAttributes() {\n\n\t\treturn getSlides().map( slide => {\n\n\t\t\tlet attributes = {};\n\t\t\tfor( let i = 0; i < slide.attributes.length; i++ ) {\n\t\t\t\tlet attribute = slide.attributes[ i ];\n\t\t\t\tattributes[ attribute.name ] = attribute.value;\n\t\t\t}\n\t\t\treturn attributes;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Retrieves the total number of slides in this presentation.\n\t *\n\t * @return {number}\n\t */\n\tfunction getTotalSlides() {\n\n\t\treturn getSlides().length;\n\n\t}\n\n\t/**\n\t * Returns the slide element matching the specified index.\n\t *\n\t * @return {HTMLElement}\n\t */\n\tfunction getSlide( x, y ) {\n\n\t\tlet horizontalSlide = getHorizontalSlides()[ x ];\n\t\tlet verticalSlides = horizontalSlide && horizontalSlide.querySelectorAll( 'section' );\n\n\t\tif( verticalSlides && verticalSlides.length && typeof y === 'number' ) {\n\t\t\treturn verticalSlides ? verticalSlides[ y ] : undefined;\n\t\t}\n\n\t\treturn horizontalSlide;\n\n\t}\n\n\t/**\n\t * Returns the background element for the given slide.\n\t * All slides, even the ones with no background properties\n\t * defined, have a background element so as long as the\n\t * index is valid an element will be returned.\n\t *\n\t * @param {mixed} x Horizontal background index OR a slide\n\t * HTML element\n\t * @param {number} y Vertical background index\n\t * @return {(HTMLElement[]|*)}\n\t */\n\tfunction getSlideBackground( x, y ) {\n\n\t\tlet slide = typeof x === 'number' ? getSlide( x, y ) : x;\n\t\tif( slide ) {\n\t\t\treturn slide.slideBackgroundElement;\n\t\t}\n\n\t\treturn undefined;\n\n\t}\n\n\t/**\n\t * Retrieves the current state of the presentation as\n\t * an object. This state can then be restored at any\n\t * time.\n\t *\n\t * @return {{indexh: number, indexv: number, indexf: number, paused: boolean, overview: boolean}}\n\t */\n\tfunction getState() {\n\n\t\tlet indices = getIndices();\n\n\t\treturn {\n\t\t\tindexh: indices.h,\n\t\t\tindexv: indices.v,\n\t\t\tindexf: indices.f,\n\t\t\tpaused: isPaused(),\n\t\t\toverview: overview.isActive()\n\t\t};\n\n\t}\n\n\t/**\n\t * Restores the presentation to the given state.\n\t *\n\t * @param {object} state As generated by getState()\n\t * @see {@link getState} generates the parameter `state`\n\t */\n\tfunction setState( state ) {\n\n\t\tif( typeof state === 'object' ) {\n\t\t\tslide( Util.deserialize( state.indexh ), Util.deserialize( state.indexv ), Util.deserialize( state.indexf ) );\n\n\t\t\tlet pausedFlag = Util.deserialize( state.paused ),\n\t\t\t\toverviewFlag = Util.deserialize( state.overview );\n\n\t\t\tif( typeof pausedFlag === 'boolean' && pausedFlag !== isPaused() ) {\n\t\t\t\ttogglePause( pausedFlag );\n\t\t\t}\n\n\t\t\tif( typeof overviewFlag === 'boolean' && overviewFlag !== overview.isActive() ) {\n\t\t\t\toverview.toggle( overviewFlag );\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Cues a new automated slide if enabled in the config.\n\t */\n\tfunction cueAutoSlide() {\n\n\t\tcancelAutoSlide();\n\n\t\tif( currentSlide && config.autoSlide !== false ) {\n\n\t\t\tlet fragment = currentSlide.querySelector( '.current-fragment[data-autoslide]' );\n\n\t\t\tlet fragmentAutoSlide = fragment ? fragment.getAttribute( 'data-autoslide' ) : null;\n\t\t\tlet parentAutoSlide = currentSlide.parentNode ? currentSlide.parentNode.getAttribute( 'data-autoslide' ) : null;\n\t\t\tlet slideAutoSlide = currentSlide.getAttribute( 'data-autoslide' );\n\n\t\t\t// Pick value in the following priority order:\n\t\t\t// 1. Current fragment's data-autoslide\n\t\t\t// 2. Current slide's data-autoslide\n\t\t\t// 3. Parent slide's data-autoslide\n\t\t\t// 4. Global autoSlide setting\n\t\t\tif( fragmentAutoSlide ) {\n\t\t\t\tautoSlide = parseInt( fragmentAutoSlide, 10 );\n\t\t\t}\n\t\t\telse if( slideAutoSlide ) {\n\t\t\t\tautoSlide = parseInt( slideAutoSlide, 10 );\n\t\t\t}\n\t\t\telse if( parentAutoSlide ) {\n\t\t\t\tautoSlide = parseInt( parentAutoSlide, 10 );\n\t\t\t}\n\t\t\telse {\n\t\t\t\tautoSlide = config.autoSlide;\n\n\t\t\t\t// If there are media elements with data-autoplay,\n\t\t\t\t// automatically set the autoSlide duration to the\n\t\t\t\t// length of that media. Not applicable if the slide\n\t\t\t\t// is divided up into fragments.\n\t\t\t\t// playbackRate is accounted for in the duration.\n\t\t\t\tif( currentSlide.querySelectorAll( '.fragment' ).length === 0 ) {\n\t\t\t\t\tUtil.queryAll( currentSlide, 'video, audio' ).forEach( el => {\n\t\t\t\t\t\tif( el.hasAttribute( 'data-autoplay' ) ) {\n\t\t\t\t\t\t\tif( autoSlide && (el.duration * 1000 / el.playbackRate ) > autoSlide ) {\n\t\t\t\t\t\t\t\tautoSlide = ( el.duration * 1000 / el.playbackRate ) + 1000;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Cue the next auto-slide if:\n\t\t\t// - There is an autoSlide value\n\t\t\t// - Auto-sliding isn't paused by the user\n\t\t\t// - The presentation isn't paused\n\t\t\t// - The overview isn't active\n\t\t\t// - The presentation isn't over\n\t\t\tif( autoSlide && !autoSlidePaused && !isPaused() && !overview.isActive() && ( !isLastSlide() || fragments.availableRoutes().next || config.loop === true ) ) {\n\t\t\t\tautoSlideTimeout = setTimeout( () => {\n\t\t\t\t\tif( typeof config.autoSlideMethod === 'function' ) {\n\t\t\t\t\t\tconfig.autoSlideMethod()\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tnavigateNext();\n\t\t\t\t\t}\n\t\t\t\t\tcueAutoSlide();\n\t\t\t\t}, autoSlide );\n\t\t\t\tautoSlideStartTime = Date.now();\n\t\t\t}\n\n\t\t\tif( autoSlidePlayer ) {\n\t\t\t\tautoSlidePlayer.setPlaying( autoSlideTimeout !== -1 );\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Cancels any ongoing request to auto-slide.\n\t */\n\tfunction cancelAutoSlide() {\n\n\t\tclearTimeout( autoSlideTimeout );\n\t\tautoSlideTimeout = -1;\n\n\t}\n\n\tfunction pauseAutoSlide() {\n\n\t\tif( autoSlide && !autoSlidePaused ) {\n\t\t\tautoSlidePaused = true;\n\t\t\tdispatchEvent({ type: 'autoslidepaused' });\n\t\t\tclearTimeout( autoSlideTimeout );\n\n\t\t\tif( autoSlidePlayer ) {\n\t\t\t\tautoSlidePlayer.setPlaying( false );\n\t\t\t}\n\t\t}\n\n\t}\n\n\tfunction resumeAutoSlide() {\n\n\t\tif( autoSlide && autoSlidePaused ) {\n\t\t\tautoSlidePaused = false;\n\t\t\tdispatchEvent({ type: 'autoslideresumed' });\n\t\t\tcueAutoSlide();\n\t\t}\n\n\t}\n\n\tfunction navigateLeft({skipFragments=false}={}) {\n\n\t\tnavigationHistory.hasNavigatedHorizontally = true;\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.prev();\n\n\t\t// Reverse for RTL\n\t\tif( config.rtl ) {\n\t\t\tif( ( overview.isActive() || skipFragments || fragments.next() === false ) && availableRoutes().left ) {\n\t\t\t\tslide( indexh + 1, config.navigationMode === 'grid' ? indexv : undefined );\n\t\t\t}\n\t\t}\n\t\t// Normal navigation\n\t\telse if( ( overview.isActive() || skipFragments || fragments.prev() === false ) && availableRoutes().left ) {\n\t\t\tslide( indexh - 1, config.navigationMode === 'grid' ? indexv : undefined );\n\t\t}\n\n\t}\n\n\tfunction navigateRight({skipFragments=false}={}) {\n\n\t\tnavigationHistory.hasNavigatedHorizontally = true;\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.next();\n\n\t\t// Reverse for RTL\n\t\tif( config.rtl ) {\n\t\t\tif( ( overview.isActive() || skipFragments || fragments.prev() === false ) && availableRoutes().right ) {\n\t\t\t\tslide( indexh - 1, config.navigationMode === 'grid' ? indexv : undefined );\n\t\t\t}\n\t\t}\n\t\t// Normal navigation\n\t\telse if( ( overview.isActive() || skipFragments || fragments.next() === false ) && availableRoutes().right ) {\n\t\t\tslide( indexh + 1, config.navigationMode === 'grid' ? indexv : undefined );\n\t\t}\n\n\t}\n\n\tfunction navigateUp({skipFragments=false}={}) {\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.prev();\n\n\t\t// Prioritize hiding fragments\n\t\tif( ( overview.isActive() || skipFragments || fragments.prev() === false ) && availableRoutes().up ) {\n\t\t\tslide( indexh, indexv - 1 );\n\t\t}\n\n\t}\n\n\tfunction navigateDown({skipFragments=false}={}) {\n\n\t\tnavigationHistory.hasNavigatedVertically = true;\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.next();\n\n\t\t// Prioritize revealing fragments\n\t\tif( ( overview.isActive() || skipFragments || fragments.next() === false ) && availableRoutes().down ) {\n\t\t\tslide( indexh, indexv + 1 );\n\t\t}\n\n\t}\n\n\t/**\n\t * Navigates backwards, prioritized in the following order:\n\t * 1) Previous fragment\n\t * 2) Previous vertical slide\n\t * 3) Previous horizontal slide\n\t */\n\tfunction navigatePrev({skipFragments=false}={}) {\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.prev();\n\n\t\t// Prioritize revealing fragments\n\t\tif( skipFragments || fragments.prev() === false ) {\n\t\t\tif( availableRoutes().up ) {\n\t\t\t\tnavigateUp({skipFragments});\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Fetch the previous horizontal slide, if there is one\n\t\t\t\tlet previousSlide;\n\n\t\t\t\tif( config.rtl ) {\n\t\t\t\t\tpreviousSlide = Util.queryAll( dom.wrapper, HORIZONTAL_SLIDES_SELECTOR + '.future' ).pop();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tpreviousSlide = Util.queryAll( dom.wrapper, HORIZONTAL_SLIDES_SELECTOR + '.past' ).pop();\n\t\t\t\t}\n\n\t\t\t\t// When going backwards and arriving on a stack we start\n\t\t\t\t// at the bottom of the stack\n\t\t\t\tif( previousSlide && previousSlide.classList.contains( 'stack' ) ) {\n\t\t\t\t\tlet v = ( previousSlide.querySelectorAll( 'section' ).length - 1 ) || undefined;\n\t\t\t\t\tlet h = indexh - 1;\n\t\t\t\t\tslide( h, v );\n\t\t\t\t}\n\t\t\t\telse if( config.rtl ) {\n\t\t\t\t\tnavigateRight({skipFragments});\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tnavigateLeft({skipFragments});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * The reverse of #navigatePrev().\n\t */\n\tfunction navigateNext({skipFragments=false}={}) {\n\n\t\tnavigationHistory.hasNavigatedHorizontally = true;\n\t\tnavigationHistory.hasNavigatedVertically = true;\n\n\t\t// Scroll view navigation is handled independently\n\t\tif( scrollView.isActive() ) return scrollView.next();\n\n\t\t// Prioritize revealing fragments\n\t\tif( skipFragments || fragments.next() === false ) {\n\n\t\t\tlet routes = availableRoutes();\n\n\t\t\t// When looping is enabled `routes.down` is always available\n\t\t\t// so we need a separate check for when we've reached the\n\t\t\t// end of a stack and should move horizontally\n\t\t\tif( routes.down && routes.right && config.loop && isLastVerticalSlide() ) {\n\t\t\t\troutes.down = false;\n\t\t\t}\n\n\t\t\tif( routes.down ) {\n\t\t\t\tnavigateDown({skipFragments});\n\t\t\t}\n\t\t\telse if( config.rtl ) {\n\t\t\t\tnavigateLeft({skipFragments});\n\t\t\t}\n\t\t\telse {\n\t\t\t\tnavigateRight({skipFragments});\n\t\t\t}\n\t\t}\n\n\t}\n\n\n\t// --------------------------------------------------------------------//\n\t// ----------------------------- EVENTS -------------------------------//\n\t// --------------------------------------------------------------------//\n\n\t/**\n\t * Called by all event handlers that are based on user\n\t * input.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onUserInput( event ) {\n\n\t\tif( config.autoSlideStoppable ) {\n\t\t\tpauseAutoSlide();\n\t\t}\n\n\t}\n\n\t/**\n\t* Listener for post message events posted to this window.\n\t*/\n\tfunction onPostMessage( event ) {\n\n\t\tlet data = event.data;\n\n\t\t// Make sure we're dealing with JSON\n\t\tif( typeof data === 'string' && data.charAt( 0 ) === '{' && data.charAt( data.length - 1 ) === '}' ) {\n\t\t\tdata = JSON.parse( data );\n\n\t\t\t// Check if the requested method can be found\n\t\t\tif( data.method && typeof Reveal[data.method] === 'function' ) {\n\n\t\t\t\tif( POST_MESSAGE_METHOD_BLACKLIST.test( data.method ) === false ) {\n\n\t\t\t\t\tconst result = Reveal[data.method].apply( Reveal, data.args );\n\n\t\t\t\t\t// Dispatch a postMessage event with the returned value from\n\t\t\t\t\t// our method invocation for getter functions\n\t\t\t\t\tdispatchPostMessage( 'callback', { method: data.method, result: result } );\n\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tconsole.warn( 'reveal.js: \"'+ data.method +'\" is is blacklisted from the postMessage API' );\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Event listener for transition end on the current slide.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onTransitionEnd( event ) {\n\n\t\tif( transition === 'running' && /section/gi.test( event.target.nodeName ) ) {\n\t\t\ttransition = 'idle';\n\t\t\tdispatchEvent({\n\t\t\t\ttype: 'slidetransitionend',\n\t\t\t\tdata: { indexh, indexv, previousSlide, currentSlide }\n\t\t\t});\n\t\t}\n\n\t}\n\n\t/**\n\t * A global listener for all click events inside of the\n\t * .slides container.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onSlidesClicked( event ) {\n\n\t\tconst anchor = Util.closest( event.target, 'a[href^=\"#\"]' );\n\n\t\t// If a hash link is clicked, we find the target slide\n\t\t// and navigate to it. We previously relied on 'hashchange'\n\t\t// for links like these but that prevented media with\n\t\t// audio tracks from playing in mobile browsers since it\n\t\t// wasn't considered a direct interaction with the document.\n\t\tif( anchor ) {\n\t\t\tconst hash = anchor.getAttribute( 'href' );\n\t\t\tconst indices = location.getIndicesFromHash( hash );\n\n\t\t\tif( indices ) {\n\t\t\t\tReveal.slide( indices.h, indices.v, indices.f );\n\t\t\t\tevent.preventDefault();\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Handler for the window level 'resize' event.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onWindowResize( event ) {\n\n\t\tlayout();\n\t}\n\n\t/**\n\t * Handle for the window level 'visibilitychange' event.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onPageVisibilityChange( event ) {\n\n\t\t// If, after clicking a link or similar and we're coming back,\n\t\t// focus the document.body to ensure we can use keyboard shortcuts\n\t\tif( document.hidden === false && document.activeElement !== document.body ) {\n\t\t\t// Not all elements support .blur() - SVGs among them.\n\t\t\tif( typeof document.activeElement.blur === 'function' ) {\n\t\t\t\tdocument.activeElement.blur();\n\t\t\t}\n\t\t\tdocument.body.focus();\n\t\t}\n\n\t}\n\n\t/**\n\t * Handler for the document level 'fullscreenchange' event.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onFullscreenChange( event ) {\n\n\t\tlet element = document.fullscreenElement || document.webkitFullscreenElement;\n\t\tif( element === dom.wrapper ) {\n\t\t\tevent.stopImmediatePropagation();\n\n\t\t\t// Timeout to avoid layout shift in Safari\n\t\t\tsetTimeout( () => {\n\t\t\t\tReveal.layout();\n\t\t\t\tReveal.focus.focus(); // focus.focus :'(\n\t\t\t}, 1 );\n\t\t}\n\n\t}\n\n\t/**\n\t * Handles clicks on links that are set to preview in the\n\t * iframe overlay.\n\t *\n\t * @param {object} event\n\t */\n\tfunction onPreviewLinkClicked( event ) {\n\n\t\tif( event.currentTarget && event.currentTarget.hasAttribute( 'href' ) ) {\n\t\t\tlet url = event.currentTarget.getAttribute( 'href' );\n\t\t\tif( url ) {\n\t\t\t\tshowPreview( url );\n\t\t\t\tevent.preventDefault();\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/**\n\t * Handles click on the auto-sliding controls element.\n\t *\n\t * @param {object} [event]\n\t */\n\tfunction onAutoSlidePlayerClick( event ) {\n\n\t\t// Replay\n\t\tif( isLastSlide() && config.loop === false ) {\n\t\t\tslide( 0, 0 );\n\t\t\tresumeAutoSlide();\n\t\t}\n\t\t// Resume\n\t\telse if( autoSlidePaused ) {\n\t\t\tresumeAutoSlide();\n\t\t}\n\t\t// Pause\n\t\telse {\n\t\t\tpauseAutoSlide();\n\t\t}\n\n\t}\n\n\n\t// --------------------------------------------------------------------//\n\t// ------------------------------- API --------------------------------//\n\t// --------------------------------------------------------------------//\n\n\t// The public reveal.js API\n\tconst API = {\n\t\tVERSION,\n\n\t\tinitialize,\n\t\tconfigure,\n\t\tdestroy,\n\n\t\tsync,\n\t\tsyncSlide,\n\t\tsyncFragments: fragments.sync.bind( fragments ),\n\n\t\t// Navigation methods\n\t\tslide,\n\t\tleft: navigateLeft,\n\t\tright: navigateRight,\n\t\tup: navigateUp,\n\t\tdown: navigateDown,\n\t\tprev: navigatePrev,\n\t\tnext: navigateNext,\n\n\t\t// Navigation aliases\n\t\tnavigateLeft, navigateRight, navigateUp, navigateDown, navigatePrev, navigateNext,\n\n\t\t// Fragment methods\n\t\tnavigateFragment: fragments.goto.bind( fragments ),\n\t\tprevFragment: fragments.prev.bind( fragments ),\n\t\tnextFragment: fragments.next.bind( fragments ),\n\n\t\t// Event binding\n\t\ton,\n\t\toff,\n\n\t\t// Legacy event binding methods left in for backwards compatibility\n\t\taddEventListener: on,\n\t\tremoveEventListener: off,\n\n\t\t// Forces an update in slide layout\n\t\tlayout,\n\n\t\t// Randomizes the order of slides\n\t\tshuffle,\n\n\t\t// Returns an object with the available routes as booleans (left/right/top/bottom)\n\t\tavailableRoutes,\n\n\t\t// Returns an object with the available fragments as booleans (prev/next)\n\t\tavailableFragments: fragments.availableRoutes.bind( fragments ),\n\n\t\t// Toggles a help overlay with keyboard shortcuts\n\t\ttoggleHelp,\n\n\t\t// Toggles the overview mode on/off\n\t\ttoggleOverview: overview.toggle.bind( overview ),\n\n\t\t// Toggles the scroll view on/off\n\t\ttoggleScrollView: scrollView.toggle.bind( scrollView ),\n\n\t\t// Toggles the \"black screen\" mode on/off\n\t\ttogglePause,\n\n\t\t// Toggles the auto slide mode on/off\n\t\ttoggleAutoSlide,\n\n\t\t// Toggles visibility of the jump-to-slide UI\n\t\ttoggleJumpToSlide,\n\n\t\t// Slide navigation checks\n\t\tisFirstSlide,\n\t\tisLastSlide,\n\t\tisLastVerticalSlide,\n\t\tisVerticalSlide,\n\t\tisVerticalStack,\n\n\t\t// State checks\n\t\tisPaused,\n\t\tisAutoSliding,\n\t\tisSpeakerNotes: notes.isSpeakerNotesWindow.bind( notes ),\n\t\tisOverview: overview.isActive.bind( overview ),\n\t\tisFocused: focus.isFocused.bind( focus ),\n\n\t\tisScrollView: scrollView.isActive.bind( scrollView ),\n\t\tisPrintView: printView.isActive.bind( printView ),\n\n\t\t// Checks if reveal.js has been loaded and is ready for use\n\t\tisReady: () => ready,\n\n\t\t// Slide preloading\n\t\tloadSlide: slideContent.load.bind( slideContent ),\n\t\tunloadSlide: slideContent.unload.bind( slideContent ),\n\n\t\t// Start/stop all media inside of the current slide\n\t\tstartEmbeddedContent: () => slideContent.startEmbeddedContent( currentSlide ),\n\t\tstopEmbeddedContent: () => slideContent.stopEmbeddedContent( currentSlide, { unloadIframes: false } ),\n\n\t\t// Preview management\n\t\tshowPreview,\n\t\thidePreview: closeOverlay,\n\n\t\t// Adds or removes all internal event listeners\n\t\taddEventListeners,\n\t\tremoveEventListeners,\n\t\tdispatchEvent,\n\n\t\t// Facility for persisting and restoring the presentation state\n\t\tgetState,\n\t\tsetState,\n\n\t\t// Presentation progress on range of 0-1\n\t\tgetProgress,\n\n\t\t// Returns the indices of the current, or specified, slide\n\t\tgetIndices,\n\n\t\t// Returns an Array of key:value maps of the attributes of each\n\t\t// slide in the deck\n\t\tgetSlidesAttributes,\n\n\t\t// Returns the number of slides that we have passed\n\t\tgetSlidePastCount,\n\n\t\t// Returns the total number of slides\n\t\tgetTotalSlides,\n\n\t\t// Returns the slide element at the specified index\n\t\tgetSlide,\n\n\t\t// Returns the previous slide element, may be null\n\t\tgetPreviousSlide: () => previousSlide,\n\n\t\t// Returns the current slide element\n\t\tgetCurrentSlide: () => currentSlide,\n\n\t\t// Returns the slide background element at the specified index\n\t\tgetSlideBackground,\n\n\t\t// Returns the speaker notes string for a slide, or null\n\t\tgetSlideNotes: notes.getSlideNotes.bind( notes ),\n\n\t\t// Returns an Array of all slides\n\t\tgetSlides,\n\n\t\t// Returns an array with all horizontal/vertical slides in the deck\n\t\tgetHorizontalSlides,\n\t\tgetVerticalSlides,\n\n\t\t// Checks if the presentation contains two or more horizontal\n\t\t// and vertical slides\n\t\thasHorizontalSlides,\n\t\thasVerticalSlides,\n\n\t\t// Checks if the deck has navigated on either axis at least once\n\t\thasNavigatedHorizontally: () => navigationHistory.hasNavigatedHorizontally,\n\t\thasNavigatedVertically: () => navigationHistory.hasNavigatedVertically,\n\n\t\tshouldAutoAnimateBetween,\n\n\t\t// Adds/removes a custom key binding\n\t\taddKeyBinding: keyboard.addKeyBinding.bind( keyboard ),\n\t\tremoveKeyBinding: keyboard.removeKeyBinding.bind( keyboard ),\n\n\t\t// Programmatically triggers a keyboard event\n\t\ttriggerKey: keyboard.triggerKey.bind( keyboard ),\n\n\t\t// Registers a new shortcut to include in the help overlay\n\t\tregisterKeyboardShortcut: keyboard.registerKeyboardShortcut.bind( keyboard ),\n\n\t\tgetComputedSlideSize,\n\t\tsetCurrentScrollPage,\n\n\t\t// Returns the current scale of the presentation content\n\t\tgetScale: () => scale,\n\n\t\t// Returns the current configuration object\n\t\tgetConfig: () => config,\n\n\t\t// Helper method, retrieves query string as a key:value map\n\t\tgetQueryHash: Util.getQueryHash,\n\n\t\t// Returns the path to the current slide as represented in the URL\n\t\tgetSlidePath: location.getHash.bind( location ),\n\n\t\t// Returns reveal.js DOM elements\n\t\tgetRevealElement: () => revealElement,\n\t\tgetSlidesElement: () => dom.slides,\n\t\tgetViewportElement: () => dom.viewport,\n\t\tgetBackgroundsElement: () => backgrounds.element,\n\n\t\t// API for registering and retrieving plugins\n\t\tregisterPlugin: plugins.registerPlugin.bind( plugins ),\n\t\thasPlugin: plugins.hasPlugin.bind( plugins ),\n\t\tgetPlugin: plugins.getPlugin.bind( plugins ),\n\t\tgetPlugins: plugins.getRegisteredPlugins.bind( plugins )\n\n\t};\n\n\t// Our internal API which controllers have access to\n\tUtil.extend( Reveal, {\n\t\t...API,\n\n\t\t// Methods for announcing content to screen readers\n\t\tannounceStatus,\n\t\tgetStatusText,\n\n\t\t// Controllers\n\t\tfocus,\n\t\tscroll: scrollView,\n\t\tprogress,\n\t\tcontrols,\n\t\tlocation,\n\t\toverview,\n\t\tfragments,\n\t\tbackgrounds,\n\t\tslideContent,\n\t\tslideNumber,\n\n\t\tonUserInput,\n\t\tcloseOverlay,\n\t\tupdateSlidesVisibility,\n\t\tlayoutSlideContents,\n\t\ttransformSlides,\n\t\tcueAutoSlide,\n\t\tcancelAutoSlide\n\t} );\n\n\treturn API;\n\n};\n", "import Deck, { VERSION } from './reveal.js'\n\n/**\n * Expose the Reveal class to the window. To create a\n * new instance:\n * let deck = new Reveal( document.querySelector( '.reveal' ), {\n *   controls: false\n * } );\n * deck.initialize().then(() => {\n *   // reveal.js is ready\n * });\n */\nlet Reveal = Deck;\n\n\n/**\n * The below is a thin shell that mimics the pre 4.0\n * reveal.js API and ensures backwards compatibility.\n * This API only allows for one Reveal instance per\n * page, whereas the new API above lets you run many\n * presentations on the same page.\n *\n * Reveal.initialize( { controls: false } ).then(() => {\n *   // reveal.js is ready\n * });\n */\n\nlet enqueuedAPICalls = [];\n\nReveal.initialize = options => {\n\n\t// Create our singleton reveal.js instance\n\tObject.assign( Reveal, new Deck( document.querySelector( '.reveal' ), options ) );\n\n\t// Invoke any enqueued API calls\n\tenqueuedAPICalls.map( method => method( Reveal ) );\n\n\treturn Reveal.initialize();\n\n}\n\n/**\n * The pre 4.0 API let you add event listener before\n * initializing. We maintain the same behavior by\n * queuing up premature API calls and invoking all\n * of them when Reveal.initialize is called.\n */\n[ 'configure', 'on', 'off', 'addEventListener', 'removeEventListener', 'registerPlugin' ].forEach( method => {\n\tReveal[method] = ( ...args ) => {\n\t\tenqueuedAPICalls.push( deck => deck[method].call( null, ...args ) );\n\t}\n} );\n\nReveal.isReady = () => false;\n\nReveal.VERSION = VERSION;\n\nexport default Reveal;"], "names": ["extend", "a", "b", "i", "queryAll", "el", "selector", "Array", "from", "querySelectorAll", "toggleClass", "className", "value", "classList", "add", "remove", "deserialize", "match", "parseFloat", "transformElement", "element", "transform", "style", "matches", "target", "matchesMethod", "matchesSelector", "msMatchesSelector", "call", "closest", "parentNode", "enterFullscreen", "requestMethod", "document", "documentElement", "requestFullscreen", "webkitRequestFullscreen", "webkitRequestFullScreen", "mozRequestFullScreen", "msRequestFullscreen", "apply", "createStyleSheet", "tag", "createElement", "type", "length", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getQueryHash", "query", "location", "search", "replace", "split", "shift", "pop", "unescape", "fileExtensionToMimeMap", "mp4", "m4a", "ogv", "mpeg", "webm", "UA", "navigator", "userAgent", "isMobile", "test", "platform", "maxTouchPoints", "isAndroid", "e", "t", "slice", "o", "l", "u", "cancelAnimationFrame", "requestAnimationFrame", "s", "filter", "dirty", "active", "c", "for<PERSON>ach", "styleComputed", "m", "y", "v", "p", "d", "f", "S", "availableWidth", "clientWidth", "currentWidth", "scrollWidth", "previousFontSize", "currentFontSize", "Math", "min", "max", "minSize", "maxSize", "whiteSpace", "multiLine", "n", "getComputedStyle", "getPropertyValue", "display", "preStyleTestCompleted", "fontSize", "dispatchEvent", "CustomEvent", "detail", "oldValue", "newValue", "scaleFactor", "h", "w", "observeMutations", "observer", "disconnect", "originalStyle", "z", "F", "MutationObserver", "observe", "g", "subtree", "childList", "characterData", "W", "E", "clearTimeout", "setTimeout", "x", "observeWindowDelay", "M", "Object", "defineProperty", "set", "concat", "observeWindow", "fitAll", "C", "assign", "map", "newbie", "push", "fit", "unfreeze", "freeze", "unsubscribe", "arguments", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "Reveal", "this", "startEmbeddedIframe", "bind", "shouldPreload", "isScrollView", "preload", "getConfig", "preloadIframes", "hasAttribute", "load", "slide", "options", "tagName", "setAttribute", "getAttribute", "removeAttribute", "media", "sources", "source", "background", "slideBackgroundElement", "backgroundContent", "slideBackgroundContentElement", "backgroundIframe", "backgroundImage", "backgroundVideo", "backgroundVideoLoop", "backgroundVideoMuted", "trim", "encodeRFC3986URI", "url", "encodeURI", "charCodeAt", "toString", "toUpperCase", "decodeURI", "join", "isSpeakerNotes", "video", "muted", "sourceElement", "getMimeTypeFromFile", "filename", "excludeIframes", "iframe", "width", "height", "maxHeight", "max<PERSON><PERSON><PERSON>", "backgroundIframeElement", "querySelector", "layout", "scopeElement", "fitty", "unload", "getSlideBackground", "formatEmbeddedContent", "_appendParamToIframeSource", "sourceAttribute", "sourceURL", "param", "getSlidesElement", "src", "indexOf", "startEmbeddedContent", "autoplay", "autoPlayMedia", "play", "readyState", "startEmbeddedMedia", "promise", "catch", "controls", "addEventListener", "removeEventListener", "event", "isAttachedToDOM", "isVisible", "paused", "ended", "currentTime", "contentWindow", "postMessage", "stopEmbeddedContent", "unloadIframes", "pause", "SLIDES_SELECTOR", "HORIZONTAL_SLIDES_SELECTOR", "VERTICAL_SLIDES_SELECTOR", "POST_MESSAGE_METHOD_BLACKLIST", "FRAGMENT_STYLE_REGEX", "SlideNumber", "render", "getRevealElement", "configure", "config", "oldConfig", "slideNumberDisplay", "slideNumber", "isPrintView", "showSlideNumber", "update", "innerHTML", "getSlideNumber", "getCurrentSlide", "format", "getHorizontalSlides", "horizontalOffset", "dataset", "visibility", "getSlidePastCount", "getTotalSlides", "indices", "getIndices", "sep", "isVerticalSlide", "getHash", "formatNumber", "delimiter", "isNaN", "destroy", "JumpToSlide", "onInput", "onBlur", "onKeyDown", "jumpInput", "placeholder", "show", "indicesOnShow", "focus", "hide", "jumpTimeout", "jump", "slideNumberFormat", "getSlides", "parseInt", "getIndicesFromHash", "oneBasedIndex", "jumpAfter", "delay", "regex", "RegExp", "find", "innerText", "cancel", "confirm", "keyCode", "stopImmediatePropagation", "colorToRgb", "color", "hex3", "r", "char<PERSON>t", "hex6", "rgb", "rgba", "Backgrounds", "create", "slideh", "backgroundStack", "createBackground", "slidev", "parallaxBackgroundImage", "backgroundSize", "parallaxBackgroundSize", "backgroundRepeat", "parallaxBackgroundRepeat", "backgroundPosition", "parallaxBackgroundPosition", "container", "contentElement", "sync", "data", "backgroundColor", "backgroundGradient", "backgroundTransition", "backgroundOpacity", "dataPreload", "opacity", "contrastClass", "getContrastClass", "contrastColor", "computedBackgroundStyle", "bubbleSlideContrastClassToElement", "classToBubble", "contains", "includeAll", "currentSlide", "currentBackground", "horizontalPast", "rtl", "horizontalFuture", "childNodes", "backgroundh", "backgroundv", "indexv", "previousBackground", "previousBackgroundHash", "currentBackgroundHash", "currentVideo", "previousVideo", "currentVideoParent", "slideContent", "currentBackgroundContent", "backgroundImageURL", "updateParallax", "backgroundWidth", "backgroundHeight", "horizontalSlides", "verticalSlides", "getVerticalSlides", "horizontalOffsetMultiplier", "slideWidth", "offsetWidth", "horizontalSlideCount", "parallaxBackgroundHorizontal", "verticalOffsetMultiplier", "verticalOffset", "slideHeight", "offsetHeight", "verticalSlideCount", "parallaxBackgroundVertical", "autoAnimateCounter", "AutoAnimate", "run", "fromSlide", "toSlide", "reset", "allSlides", "toSlideIndex", "fromSlideIndex", "autoAnimateStyleSheet", "animationOptions", "getAutoAnimateOptions", "autoAnimate", "slideDirection", "fromSlideIsHidden", "css", "getAutoAnimatableElements", "elements", "autoAnimateElements", "to", "autoAnimateUnmatched", "defaultUnmatchedDuration", "duration", "defaultUnmatchedDelay", "getUnmatchedAutoAnimateElements", "unmatchedElement", "unmatchedOptions", "id", "autoAnimateTarget", "fontWeight", "sheet", "<PERSON><PERSON><PERSON><PERSON>", "elementOptions", "easing", "fromProps", "getAutoAnimatableProperties", "toProps", "styles", "translate", "scale", "presentationScale", "getScale", "delta", "scaleX", "scaleY", "round", "propertyName", "toValue", "fromValue", "explicitValue", "toStyleProperties", "keys", "inheritedOptions", "autoAnimateEasing", "autoAnimateDuration", "autoAnimatedParent", "autoAnimateDelay", "direction", "properties", "bounds", "measure", "center", "getBoundingClientRect", "offsetLeft", "offsetTop", "computedStyles", "autoAnimateStyles", "property", "pairs", "autoAnimateMatcher", "getAutoAnimatePairs", "reserved", "pair", "index", "textNodes", "findAutoAnimateMatches", "node", "nodeName", "textContent", "getLocalBoundingBox", "fromScope", "toScope", "serializer", "fromMatches", "to<PERSON><PERSON><PERSON>", "key", "fromElement", "primaryIndex", "secondaryIndex", "rootElement", "children", "reduce", "result", "containsAnimatedElements", "ScrollView", "activatedCallbacks", "onScroll", "activate", "stateBeforeActivation", "getState", "slideHTMLBeforeActivation", "horizontalBackgrounds", "presentationBackground", "viewportElement", "viewportStyles", "pageElements", "pageContainer", "previousSlide", "createPageElement", "isVertical", "contentContainer", "shouldAutoAnimateBetween", "page", "slideBackground", "pageBackground", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "horizontalSlide", "isVerticalStack", "verticalSlide", "createProgressBar", "stack", "setState", "callback", "restoreScrollPosition", "passive", "deactivate", "stateBeforeDeactivation", "removeProgressBar", "toggle", "override", "isActive", "progressBar", "progressBarInner", "progressBarPlayhead", "<PERSON><PERSON><PERSON><PERSON>", "handleDocumentMouseMove", "progress", "clientY", "top", "progressBarHeight", "scrollTop", "scrollHeight", "handleDocumentMouseUp", "draggingProgressBar", "showProgressBar", "preventDefault", "syncPages", "syncScrollPosition", "slideSize", "getComputedSlideSize", "innerWidth", "innerHeight", "useCompactLayout", "scrollLayout", "viewportHeight", "compactHeight", "pageHeight", "scrollTriggerHeight", "setProperty", "scrollSnapType", "scrollSnap", "slideTriggers", "pages", "pageElement", "createPage", "slideElement", "stickyElement", "backgroundElement", "autoAnimatePages", "activatePage", "deactivatePage", "createFragmentTriggersForPage", "createAutoAnimateTriggersForPage", "totalScrollTriggerCount", "scrollTriggers", "total", "triggerStick", "scrollSnapAlign", "marginTop", "removeProperty", "scrollPadding", "totalHeight", "position", "setTriggerRanges", "scrollProgress", "syncProgressBar", "trigger", "rangeStart", "range", "scrollTriggerSegmentSize", "scrollTrigger", "fragmentGroups", "fragments", "sort", "autoAnimateElement", "autoAnimatePage", "indexh", "viewportHeightFactor", "playheadHeight", "progressBarScrollableHeight", "progressSegmentHeight", "spacing", "slideTrigger", "progressBarSlide", "scrollTriggerElements", "triggerElement", "scrollProgressMid", "activePage", "loaded", "activateTrigger", "deactivateTrigger", "setProgressBarValue", "getAllPages", "hideProgressBarTimeout", "prev", "next", "scrollToSlide", "getScrollTriggerBySlide", "storeScrollPosition", "storeScrollPositionTimeout", "sessionStorage", "setItem", "origin", "pathname", "scrollPosition", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "setCurrentScrollPage", "backgrounds", "sibling", "getSlideByIndices", "flatMap", "getViewportElement", "PrintView", "slides", "injectPageNumbers", "pageWidth", "floor", "margin", "Promise", "body", "layoutSlideContents", "slideScrollHeights", "left", "contentHeight", "numberOfPages", "ceil", "pdfMaxPagesPerSlide", "pdfPageHeightOffset", "showNotes", "notes", "getSlideNotes", "notesSpacing", "notesLayout", "notesElement", "bottom", "numberElement", "pdfSeparateFragments", "previousFragmentStep", "fragment", "clonedPage", "cloneNode", "fragmentNumber", "view", "Fragments", "disable", "enable", "availableRoutes", "hiddenFragments", "grouped", "ordered", "unordered", "sorted", "group", "sortAll", "changedFragments", "shown", "hidden", "maxIndex", "currentFragment", "wasVisible", "announceStatus", "getStatusText", "bubbles", "goto", "offset", "lastVisibleFragment", "fragmentInURL", "writeURL", "Overview", "onSlideClicked", "overview", "cancelAutoSlide", "getBackgroundsElement", "overviewSlideWidth", "overviewSlideHeight", "updateSlidesVisibility", "hslide", "vslide", "hbackground", "vbackground", "vmin", "transformSlides", "cueAutoSlide", "Keyboard", "shortcuts", "bindings", "onDocumentKeyDown", "navigationMode", "unbind", "add<PERSON>eyBinding", "binding", "description", "removeKeyBinding", "<PERSON><PERSON><PERSON>", "registerKeyboardShortcut", "getShortcuts", "getBindings", "keyboardCondition", "isFocused", "autoSlideWasPaused", "isAutoSliding", "onUserInput", "activeElementIsCE", "activeElement", "isContentEditable", "activeElementIsInput", "activeElementIsNotes", "unusedModifier", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "resumeKeyCodes", "keyboard", "isPaused", "useLinearMode", "hasHorizontalSlides", "hasVerticalSlides", "triggered", "action", "skipFragments", "right", "undefined", "up", "Number", "MAX_VALUE", "down", "includes", "toggle<PERSON><PERSON>e", "embedded", "autoSlideStoppable", "toggleAutoSlide", "jumpToSlide", "toggleJumpToSlide", "toggleHelp", "closeOverlay", "Location", "MAX_REPLACE_STATE_FREQUENCY", "writeURLTimeout", "replaceStateTimestamp", "onWindowHashChange", "hash", "name", "bits", "hashIndexBase", "hashOneBasedIndex", "getElementById", "decodeURIComponent", "error", "readURL", "currentIndices", "newIndices", "history", "debouncedReplaceState", "replaceState", "Date", "now", "replaceStateTimeout", "encodeURIComponent", "Controls", "onNavigateLeftClicked", "onNavigateRightClicked", "onNavigateUpClicked", "onNavigateDownClicked", "onNavigatePrevClicked", "onNavigateNextClicked", "onEnterFullscreen", "revealElement", "controlsLeft", "controlsRight", "controlsUp", "controlsDown", "controlsPrev", "controlsNext", "controlsFullscreen", "controlsRightArrow", "controlsLeftArrow", "controlsDownArrow", "controlsLayout", "controlsBackArrows", "pointerEvents", "eventName", "routes", "fragmentsRoutes", "controlsTutorial", "hasNavigatedVertically", "hasNavigatedHorizontally", "viewport", "parentElement", "Progress", "onProgressClicked", "bar", "getProgress", "getMaxWidth", "slidesTotal", "slideIndex", "clientX", "targetIndices", "Pointer", "lastMouseWheelStep", "cursor<PERSON><PERSON><PERSON>", "cursorInactiveTimeout", "onDocumentCursorActive", "onDocumentMouseScroll", "mouseWheel", "hideInactiveCursor", "showCursor", "cursor", "hideCursor", "hideCursorTime", "wheelDelta", "loadScript", "script", "async", "defer", "onload", "onreadystatechange", "onerror", "err", "Error", "<PERSON><PERSON><PERSON><PERSON>", "Plugins", "reveal", "state", "registeredPlugins", "asyncDependencies", "plugins", "dependencies", "registerPlugin", "resolve", "scripts", "scriptsToLoad", "condition", "scriptLoa<PERSON><PERSON><PERSON>back", "initPlugins", "then", "console", "warn", "pluginValues", "values", "pluginsToInitialize", "loadAsync", "initNextPlugin", "afterPlugInitialized", "plugin", "init", "hasPlugin", "getPlugin", "getRegisteredPlugins", "Touch", "touchStartX", "touchStartY", "touchStartCount", "touchCaptured", "onPointerDown", "onPointerMove", "onPointerUp", "onTouchStart", "onTouchMove", "onTouchEnd", "msPointer<PERSON><PERSON><PERSON>", "isSwipePrevented", "touches", "currentX", "currentY", "includeFragments", "deltaX", "deltaY", "abs", "pointerType", "MSPOINTER_TYPE_TOUCH", "STATE_FOCUS", "STATE_BLUR", "Focus", "onRevealPointerDown", "onDocumentPointerDown", "blur", "Notes", "updateVisibility", "hasNotes", "isSpeakerNotesWindow", "notesElements", "Playback", "progressCheck", "diameter", "diameter2", "thickness", "playing", "progressOffset", "canvas", "context", "getContext", "setPlaying", "wasPlaying", "animate", "progressBefore", "radius", "iconSize", "endAngle", "PI", "startAngle", "save", "clearRect", "beginPath", "arc", "fillStyle", "fill", "lineWidth", "strokeStyle", "stroke", "fillRect", "moveTo", "lineTo", "restore", "on", "listener", "off", "defaultConfig", "minScale", "maxScale", "respondToHashChanges", "disableLayout", "touch", "loop", "shuffle", "help", "showHiddenSlides", "autoSlide", "autoSlideMethod", "defaultTiming", "previewLinks", "postMessageEvents", "focusBodyOnPageVisibilityChange", "transition", "transitionSpeed", "scrollActivationWidth", "POSITIVE_INFINITY", "viewDistance", "mobileViewDistance", "sortFragmentsOnSync", "VERSION", "Deck", "autoSlidePlayer", "initialized", "ready", "navigationHistory", "slidesTransform", "dom", "autoSlideTimeout", "autoSlideStartTime", "autoSlidePaused", "scrollView", "printView", "pointer", "start", "<PERSON><PERSON>", "wrapper", "parent", "childElementCount", "<PERSON><PERSON>", "pauseO<PERSON>lay", "createSingletonNode", "tagname", "classname", "nodes", "testNode", "statusElement", "overflow", "clip", "createStatusElement", "setupDOM", "onPostMessage", "setInterval", "scrollLeft", "onFullscreenChange", "activatePrintView", "activateScrollView", "removeEventListeners", "activateInitialView", "text", "nodeType", "isAriaH<PERSON>den", "isDisplayHidden", "child", "isReady", "numberOfSlides", "resume", "enablePreviewLinks", "disablePreviewLinks", "onAutoSlidePlayerClick", "addEventListeners", "onWindowResize", "onSlidesClicked", "onTransitionEnd", "onPageVisibilityChange", "useCapture", "transforms", "createEvent", "initEvent", "dispatchPostMessage", "dispatchSlideChanged", "self", "message", "namespace", "JSON", "stringify", "onPreviewLinkClicked", "showPreview", "overlay", "showHelp", "html", "viewportWidth", "size", "oldScale", "presentation<PERSON>id<PERSON>", "presentationHeight", "zoom", "len", "checkResponsiveScrollView", "remainingHeight", "getRemainingHeight", "newHeight", "oldHeight", "nw", "naturalWidth", "videoWidth", "nh", "naturalHeight", "videoHeight", "es", "setPreviousVerticalIndex", "getPreviousVerticalIndex", "attributeName", "isLastVerticalSlide", "nextElement<PERSON><PERSON>ling", "isFirstSlide", "isLastSlide", "wasPaused", "defaultPrevented", "stateBefore", "indexhBefore", "indexvBefore", "updateSlides", "slideChanged", "currentHorizontalSlide", "currentVerticalSlides", "autoAnimateTransition", "stateLoop", "j", "splice", "beforeSlide", "random", "<PERSON><PERSON><PERSON><PERSON>", "printMode", "loopedForwards", "loopedBackwards", "reverse", "showFragmentsIn", "hideFragmentsIn", "wasPresent", "slideState", "distanceX", "distanceY", "horizontalSlidesLength", "verticalSlidesLength", "oy", "fragmentRoutes", "pastCount", "mainLoop", "getSlide", "indexf", "fragmentAutoSlide", "parentAutoSlide", "slideAutoSlide", "playbackRate", "navigateNext", "pauseAutoSlide", "resumeAutoSlide", "navigateLeft", "navigateRight", "navigateUp", "navigateDown", "navigatePrev", "parse", "method", "args", "anchor", "fullscreenElement", "webkitFullscreenElement", "currentTarget", "API", "initialize", "initOptions", "setViewport", "syncSlide", "syncFragments", "navigateFragment", "prevFragment", "nextFragment", "availableFragments", "toggleOverview", "toggleScrollView", "isOverview", "loadSlide", "unloadSlide", "hidePreview", "pausedFlag", "overviewFlag", "totalCount", "allFragments", "fragmentWeight", "getSlidesAttributes", "attributes", "attribute", "getPreviousSlide", "getSlidePath", "getPlugins", "scroll", "enqueuedAPICalls", "deck"], "mappings": ";;;;;;;uOAOO,MAAMA,EAASA,CAAEC,EAAGC,KAE1B,IAAK,IAAIC,KAAKD,EACbD,EAAGE,GAAMD,EAAGC,GAGb,OAAOF,CAAC,EAOIG,EAAWA,CAAEC,EAAIC,IAEtBC,MAAMC,KAAMH,EAAGI,iBAAkBH,IAO5BI,EAAcA,CAAEL,EAAIM,EAAWC,KACvCA,EACHP,EAAGQ,UAAUC,IAAKH,GAGlBN,EAAGQ,UAAUE,OAAQJ,EACtB,EASYK,EAAgBJ,IAE5B,GAAqB,iBAAVA,EAAqB,CAC/B,GAAc,SAAVA,EAAmB,OAAO,KACzB,GAAc,SAAVA,EAAmB,OAAO,EAC9B,GAAc,UAAVA,EAAoB,OAAO,EAC/B,GAAIA,EAAMK,MAAO,eAAkB,OAAOC,WAAYN,EAC5D,CAEA,OAAOA,CAAK,EA4BAO,EAAmBA,CAAEC,EAASC,KAE1CD,EAAQE,MAAMD,UAAYA,CAAS,EAavBE,EAAUA,CAAEC,EAAQlB,KAEhC,IAAImB,EAAgBD,EAAOD,SAAWC,EAAOE,iBAAmBF,EAAOG,kBAEvE,SAAWF,IAAiBA,EAAcG,KAAMJ,EAAQlB,GAAY,EAexDuB,EAAUA,CAAEL,EAAQlB,KAGhC,GAA8B,mBAAnBkB,EAAOK,QACjB,OAAOL,EAAOK,QAASvB,GAIxB,KAAOkB,GAAS,CACf,GAAID,EAASC,EAAQlB,GACpB,OAAOkB,EAIRA,EAASA,EAAOM,UACjB,CAEA,OAAO,IAAI,EAUCC,EAAkBX,IAK9B,IAAIY,GAHJZ,EAAUA,GAAWa,SAASC,iBAGFC,mBACvBf,EAAQgB,yBACRhB,EAAQiB,yBACRjB,EAAQkB,sBACRlB,EAAQmB,oBAETP,GACHA,EAAcQ,MAAOpB,EACtB,EA6CYqB,EAAqB7B,IAEjC,IAAI8B,EAAMT,SAASU,cAAe,SAclC,OAbAD,EAAIE,KAAO,WAEPhC,GAASA,EAAMiC,OAAS,IACvBH,EAAII,WACPJ,EAAII,WAAWC,QAAUnC,EAGzB8B,EAAIM,YAAaf,SAASgB,eAAgBrC,KAI5CqB,SAASiB,KAAKF,YAAaN,GAEpBA,CAAG,EAOES,EAAeA,KAE3B,IAAIC,EAAQ,CAAA,EAEZC,SAASC,OAAOC,QAAS,4BAA4BtD,IACpDmD,EAAOnD,EAAEuD,MAAO,KAAMC,SAAYxD,EAAEuD,MAAO,KAAME,KAAK,IAIvD,IAAK,IAAIvD,KAAKiD,EAAQ,CACrB,IAAIxC,EAAQwC,EAAOjD,GAEnBiD,EAAOjD,GAAMa,EAAa2C,SAAU/C,GACrC,CAMA,YAFqC,IAA1BwC,EAAoB,qBAA2BA,EAAoB,aAEvEA,CAAK,EAyCPQ,EAAyB,CAC9BC,IAAO,YACPC,IAAO,YACPC,IAAO,YACPC,KAAQ,aACRC,KAAQ,cChSHC,EAAKC,UAAUC,UAERC,EAAW,+BAA+BC,KAAMJ,IAC9B,aAAvBC,UAAUI,UAA2BJ,UAAUK,eAAiB,EAI3DC,EAAY,YAAYH,KAAMJ,GCF3C,IAAIQ,EAAE,SAASA,GAAG,GAAGA,EAAE,CAAC,IAAIC,EAAE,SAASD,GAAG,MAAM,GAAGE,MAAMhD,KAAK8C,EAAE,EAAcG,EAAE,EAAE5E,EAAE,GAAG6E,EAAE,KAAKC,EAAE,0BAA0BL,EAAE,WAAWA,EAAEM,qBAAqBF,GAAGA,EAAEJ,EAAEO,uBAAuB,WAAW,OAAOC,EAAEjF,EAAEkF,QAAQ,SAAST,GAAG,OAAOA,EAAEU,OAAOV,EAAEW,MAAO,IAAI,GAAE,EAAE,WAAY,EAACC,EAAE,SAASZ,GAAG,OAAO,WAAWzE,EAAEsF,SAAS,SAASZ,GAAG,OAAOA,EAAES,MAAMV,CAAE,IAAGK,GAAG,CAAC,EAAEG,EAAE,SAASR,GAAGA,EAAES,iBAAiBT,GAAG,OAAOA,EAAEc,aAAc,IAAGD,SAAS,SAASb,GAAGA,EAAEc,cAAcC,EAAEf,EAAG,IAAGA,EAAES,OAAOO,GAAGH,QAAQI,GAAG,IAAIhB,EAAED,EAAES,OAAOS,GAAGjB,EAAEY,QAAQM,GAAGlB,EAAEY,SAAS,SAASb,GAAGiB,EAAEjB,GAAGoB,EAAEpB,EAAG,IAAGC,EAAEY,QAAQQ,EAAE,EAAED,EAAE,SAASpB,GAAG,OAAOA,EAAEU,MAA3gB,CAAkhB,EAAES,EAAE,SAASnB,GAAGA,EAAEsB,eAAetB,EAAEtD,QAAQU,WAAWmE,YAAYvB,EAAEwB,aAAaxB,EAAEtD,QAAQ+E,YAAYzB,EAAE0B,iBAAiB1B,EAAE2B,gBAAgB3B,EAAE2B,gBAAgBC,KAAKC,IAAID,KAAKE,IAAI9B,EAAE+B,QAAQ/B,EAAEsB,eAAetB,EAAEwB,aAAaxB,EAAE0B,kBAAkB1B,EAAEgC,SAAShC,EAAEiC,WAAWjC,EAAEkC,WAAWlC,EAAE2B,kBAAkB3B,EAAE+B,QAAQ,SAAS,QAAQ,EAAEb,EAAE,SAASlB,GAAG,OAA51B,IAAm2BA,EAAEU,OAAr2B,IAAg3BV,EAAEU,OAAWV,EAAEtD,QAAQU,WAAWmE,cAAcvB,EAAEsB,cAAc,EAAEP,EAAE,SAASd,GAAG,IAAIkC,EAAEnC,EAAEoC,iBAAiBnC,EAAEvD,QAAQ,MAAM,OAAOuD,EAAE0B,gBAAgBnF,WAAW2F,EAAEE,iBAAiB,cAAcpC,EAAEqC,QAAQH,EAAEE,iBAAiB,WAAWpC,EAAEgC,WAAWE,EAAEE,iBAAiB,gBAAe,CAAE,EAAErB,EAAE,SAAShB,GAAG,IAAIC,GAAE,EAAG,OAAOD,EAAEuC,wBAAwB,UAAU3C,KAAKI,EAAEsC,WAAWrC,GAAE,EAAGD,EAAEsC,QAAQ,gBAAgB,WAAWtC,EAAEiC,aAAahC,GAAE,EAAGD,EAAEiC,WAAW,UAAUjC,EAAEuC,uBAAsB,EAAGtC,EAAE,EAAEgB,EAAE,SAASjB,GAAGA,EAAEtD,QAAQE,MAAMqF,WAAWjC,EAAEiC,WAAWjC,EAAEtD,QAAQE,MAAM0F,QAAQtC,EAAEsC,QAAQtC,EAAEtD,QAAQE,MAAM4F,SAASxC,EAAE2B,gBAAgB,IAAI,EAAEN,EAAE,SAASrB,GAAGA,EAAEtD,QAAQ+F,cAAc,IAAIC,YAAY,MAAM,CAACC,OAAO,CAACC,SAAS5C,EAAE0B,iBAAiBmB,SAAS7C,EAAE2B,gBAAgBmB,YAAY9C,EAAE2B,gBAAgB3B,EAAE0B,oBAAoB,EAAEqB,EAAE,SAAS/C,EAAEC,GAAG,OAAO,WAAWD,EAAEU,MAAMT,EAAED,EAAEW,QAAQN,GAAG,CAAC,EAAE2C,EAAE,SAAShD,GAAG,OAAO,WAAWzE,EAAEA,EAAEkF,QAAQ,SAASR,GAAG,OAAOA,EAAEvD,UAAUsD,EAAEtD,OAAQ,IAAGsD,EAAEiD,kBAAkBjD,EAAEkD,SAASC,aAAanD,EAAEtD,QAAQE,MAAMqF,WAAWjC,EAAEoD,cAAcnB,WAAWjC,EAAEtD,QAAQE,MAAM0F,QAAQtC,EAAEoD,cAAcd,QAAQtC,EAAEtD,QAAQE,MAAM4F,SAASxC,EAAEoD,cAAcZ,QAAQ,CAAC,EAAEhH,EAAE,SAASwE,GAAG,OAAO,WAAWA,EAAEW,SAASX,EAAEW,QAAO,EAAGN,IAAI,CAAC,EAAEgD,EAAE,SAASrD,GAAG,OAAO,WAAW,OAAOA,EAAEW,QAAO,CAAE,CAAC,EAAE2C,EAAE,SAAStD,GAAGA,EAAEiD,mBAAmBjD,EAAEkD,SAAS,IAAIK,iBAAiBR,EAAE/C,EAAlqE,IAAwqEA,EAAEkD,SAASM,QAAQxD,EAAEtD,QAAQsD,EAAEiD,kBAAkB,EAAEQ,EAAE,CAAC1B,QAAQ,GAAGC,QAAQ,IAAIE,WAAU,EAAGe,iBAAiB,qBAAqBjD,GAAG,CAAC0D,SAAQ,EAAGC,WAAU,EAAGC,eAAc,IAAKC,EAAE,KAAKC,EAAE,WAAW9D,EAAE+D,aAAaF,GAAGA,EAAE7D,EAAEgE,WAAWpD,EAAx4E,GAA64EqD,EAAEC,mBAAmB,EAAEC,EAAE,CAAC,SAAS,qBAAqB,OAAOC,OAAOC,eAAeJ,EAAE,gBAAgB,CAACK,IAAI,SAASrE,GAAG,IAAIkC,EAAE,GAAGoC,OAAOtE,EAAE,MAAM,SAAS,iBAAiBkE,EAAEtD,SAAO,SAAWZ,GAAGD,EAAEmC,GAAGlC,EAAE6D,EAAG,GAAE,IAAIG,EAAEO,eAAc,EAAGP,EAAEC,mBAAmB,IAAID,EAAEQ,OAAO7D,EAAET,GAAG8D,CAAC,CAAC,SAASS,EAAE1E,EAAEC,GAAG,IAAIkC,EAAEiC,OAAOO,OAAO,CAAE,EAAClB,EAAExD,GAAGxE,EAAEuE,EAAE4E,KAAK,SAAS5E,GAAG,IAAIC,EAAEmE,OAAOO,OAAO,CAAA,EAAGxC,EAAE,CAACzF,QAAQsD,EAAEW,QAAO,IAAK,OAAO,SAASX,GAAGA,EAAEoD,cAAc,CAACnB,WAAWjC,EAAEtD,QAAQE,MAAMqF,WAAWK,QAAQtC,EAAEtD,QAAQE,MAAM0F,QAAQE,SAASxC,EAAEtD,QAAQE,MAAM4F,UAAUc,EAAEtD,GAAGA,EAAE6E,QAAO,EAAG7E,EAAEU,OAAM,EAAGnF,EAAEuJ,KAAK9E,EAAE,CAA3K,CAA6KC,GAAG,CAACvD,QAAQsD,EAAE+E,IAAIhC,EAAE9C,EAAEE,GAAG6E,SAASxJ,EAAEyE,GAAGgF,OAAO5B,EAAEpD,GAAGiF,YAAYlC,EAAE/C,GAAI,IAAG,OAAOI,IAAI5E,CAAC,CAAC,SAASwI,EAAEjE,GAAG,IAAImC,EAAEgD,UAAUhH,OAAO,QAAG,IAASgH,UAAU,GAAGA,UAAU,GAAG,CAAA,EAAG,MAAM,iBAAiBnF,EAAE0E,EAAEzE,EAAE1C,SAASxB,iBAAiBiE,IAAImC,GAAGuC,EAAE,CAAC1E,GAAGmC,GAAG,EAAE,CAAC,CAAlvG,CAAovG,oBAAoBiD,OAAO,KAAKA,QCI3wG,MAAMC,EAEpBC,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAKC,oBAAsBD,KAAKC,oBAAoBC,KAAMF,KAE3D,CAQAG,aAAAA,CAAejJ,GAEd,GAAI8I,KAAKD,OAAOK,eACf,OAAO,EAIR,IAAIC,EAAUL,KAAKD,OAAOO,YAAYC,eAQtC,MAJuB,kBAAZF,IACVA,EAAUnJ,EAAQsJ,aAAc,iBAG1BH,CACR,CASAI,IAAAA,CAAMC,EAAOC,EAAU,IAGtBD,EAAMtJ,MAAM0F,QAAUkD,KAAKD,OAAOO,YAAYxD,QAG9C5G,EAAUwK,EAAO,qEAAsErF,SAASnE,KACvE,WAApBA,EAAQ0J,SAAwBZ,KAAKG,cAAejJ,MACvDA,EAAQ2J,aAAc,MAAO3J,EAAQ4J,aAAc,aACnD5J,EAAQ2J,aAAc,mBAAoB,IAC1C3J,EAAQ6J,gBAAiB,YAC1B,IAID7K,EAAUwK,EAAO,gBAAiBrF,SAAS2F,IAC1C,IAAIC,EAAU,EAEd/K,EAAU8K,EAAO,oBAAqB3F,SAAS6F,IAC9CA,EAAOL,aAAc,MAAOK,EAAOJ,aAAc,aACjDI,EAAOH,gBAAiB,YACxBG,EAAOL,aAAc,mBAAoB,IACzCI,GAAW,CAAC,IAIT9G,GAA8B,UAAlB6G,EAAMJ,SACrBI,EAAMH,aAAc,cAAe,IAKhCI,EAAU,GACbD,EAAMP,MACP,IAKD,IAAIU,EAAaT,EAAMU,uBACvB,GAAID,EAAa,CAChBA,EAAW/J,MAAM0F,QAAU,QAE3B,IAAIuE,EAAoBX,EAAMY,8BAC1BC,EAAmBb,EAAMI,aAAc,0BAG3C,IAAiD,IAA7CK,EAAWX,aAAc,eAA4B,CACxDW,EAAWN,aAAc,cAAe,QAExC,IAAIW,EAAkBd,EAAMI,aAAc,yBACzCW,EAAkBf,EAAMI,aAAc,yBACtCY,EAAsBhB,EAAMF,aAAc,8BAC1CmB,EAAuBjB,EAAMF,aAAc,+BAG5C,GAAIgB,EAEE,SAASpH,KAAMoH,EAAgBI,QACnCP,EAAkBjK,MAAMoK,gBAAmB,OAAMA,EAAgBI,UAIjEP,EAAkBjK,MAAMoK,gBAAkBA,EAAgBlI,MAAO,KAAM8F,KAAK+B,GAGnE,OH4LiBU,EAAEC,EAAI,KAC9BC,UAAUD,GACdzI,QAAQ,OAAQ,KAChBA,QAAQ,OAAQ,KAChBA,QACF,YACC+B,GAAO,IAAGA,EAAE4G,WAAW,GAAGC,SAAS,IAAIC,kBGlMrBL,CADAM,UAAUhB,EAAWS,cAEjCQ,KAAM,UAIN,GAAKX,IAAoBzB,KAAKD,OAAOsC,iBAAmB,CAC5D,IAAIC,EAAQvK,SAASU,cAAe,SAEhCiJ,GACHY,EAAMzB,aAAc,OAAQ,IAGzBc,IACHW,EAAMC,OAAQ,GAQXpI,IACHmI,EAAMC,OAAQ,EACdD,EAAMzB,aAAc,cAAe,KAIpCY,EAAgBnI,MAAO,KAAM+B,SAAS6F,IACrC,MAAMsB,EAAgBzK,SAASU,cAAe,UAC9C+J,EAAc3B,aAAc,MAAOK,GAEnC,IAAIxI,EHmJyB+J,EAAEC,EAAS,KACtChJ,EAAuBgJ,EAASpJ,MAAM,KAAKE,OGpJlCiJ,CAAqBvB,GAC5BxI,GACH8J,EAAc3B,aAAc,OAAQnI,GAGrC4J,EAAMxJ,YAAa0J,EAAe,IAGnCnB,EAAkBvI,YAAawJ,EAChC,MAEK,GAAIf,IAA+C,IAA3BZ,EAAQgC,eAA0B,CAC9D,IAAIC,EAAS7K,SAASU,cAAe,UACrCmK,EAAO/B,aAAc,kBAAmB,IACxC+B,EAAO/B,aAAc,qBAAsB,IAC3C+B,EAAO/B,aAAc,wBAAyB,IAC9C+B,EAAO/B,aAAc,QAAS,YAE9B+B,EAAO/B,aAAc,WAAYU,GAEjCqB,EAAOxL,MAAMyL,MAAS,OACtBD,EAAOxL,MAAM0L,OAAS,OACtBF,EAAOxL,MAAM2L,UAAY,OACzBH,EAAOxL,MAAM4L,SAAW,OAExB3B,EAAkBvI,YAAa8J,EAChC,CACD,CAGA,IAAIK,EAA0B5B,EAAkB6B,cAAe,oBAC3DD,GAGCjD,KAAKG,cAAegB,KAAiB,0BAA0B/G,KAAMmH,IACpE0B,EAAwBnC,aAAc,SAAYS,GACrD0B,EAAwBpC,aAAc,MAAOU,EAMjD,CAEAvB,KAAKmD,OAAQzC,EAEd,CAKAyC,MAAAA,CAAQC,GAKP/M,MAAMC,KAAM8M,EAAa7M,iBAAkB,gBAAkB8E,SAASnE,IACrEmM,EAAOnM,EAAS,CACfqF,QAAS,GACTC,QAA0C,GAAjCwD,KAAKD,OAAOO,YAAYwC,OACjCrF,kBAAkB,EAClBuB,eAAe,GACb,GAGL,CAQAsE,MAAAA,CAAQ5C,GAGPA,EAAMtJ,MAAM0F,QAAU,OAGtB,IAAIqE,EAAanB,KAAKD,OAAOwD,mBAAoB7C,GAC7CS,IACHA,EAAW/J,MAAM0F,QAAU,OAG3B5G,EAAUiL,EAAY,eAAgB9F,SAASnE,IAC9CA,EAAQ6J,gBAAiB,MAAO,KAKlC7K,EAAUwK,EAAO,6FAA8FrF,SAASnE,IACvHA,EAAQ2J,aAAc,WAAY3J,EAAQ4J,aAAc,QACxD5J,EAAQ6J,gBAAiB,MAAO,IAIjC7K,EAAUwK,EAAO,0DAA2DrF,SAAS6F,IACpFA,EAAOL,aAAc,WAAYK,EAAOJ,aAAc,QACtDI,EAAOH,gBAAiB,MAAO,GAGjC,CAKAyC,qBAAAA,GAEC,IAAIC,EAA6BA,CAAEC,EAAiBC,EAAWC,KAC9D1N,EAAU8J,KAAKD,OAAO8D,mBAAoB,UAAWH,EAAiB,MAAOC,EAAW,MAAOtI,SAASlF,IACvG,IAAI2N,EAAM3N,EAAG2K,aAAc4C,GACvBI,IAAiC,IAA1BA,EAAIC,QAASH,IACvBzN,EAAG0K,aAAc6C,EAAiBI,GAAS,KAAK1J,KAAM0J,GAAc,IAAN,KAAcF,EAC7E,GACC,EAIHH,EAA4B,MAAO,qBAAsB,iBACzDA,EAA4B,WAAY,qBAAsB,iBAG9DA,EAA4B,MAAO,oBAAqB,SACxDA,EAA4B,WAAY,oBAAqB,QAE9D,CAQAO,oBAAAA,CAAsB9M,GAEjBA,IAAY8I,KAAKD,OAAOsC,mBAG3BnM,EAAUgB,EAAS,oBAAqBmE,SAASlF,IAGhDA,EAAG0K,aAAc,MAAO1K,EAAG2K,aAAc,OAAS,IAInD5K,EAAUgB,EAAS,gBAAiBmE,SAASlF,IAC5C,GAAIwB,EAASxB,EAAI,eAAkBwB,EAASxB,EAAI,qBAC/C,OAID,IAAI8N,EAAWjE,KAAKD,OAAOO,YAAY4D,cAQvC,GAJwB,kBAAbD,IACVA,EAAW9N,EAAGqK,aAAc,oBAAuB7I,EAASxB,EAAI,sBAG7D8N,GAA+B,mBAAZ9N,EAAGgO,KAGzB,GAAIhO,EAAGiO,WAAa,EACnBpE,KAAKqE,mBAAoB,CAAE/M,OAAQnB,SAI/B,GAAIgE,EAAW,CACnB,IAAImK,EAAUnO,EAAGgO,OAIbG,GAAoC,mBAAlBA,EAAQC,QAAwC,IAAhBpO,EAAGqO,UACxDF,EAAQC,OAAO,KACdpO,EAAGqO,UAAW,EAGdrO,EAAGsO,iBAAkB,QAAQ,KAC5BtO,EAAGqO,UAAW,CAAK,GACjB,GAGN,MAGCrO,EAAGuO,oBAAqB,aAAc1E,KAAKqE,oBAC3ClO,EAAGsO,iBAAkB,aAAczE,KAAKqE,mBAG1C,IAIDnO,EAAUgB,EAAS,eAAgBmE,SAASlF,IACvCwB,EAASxB,EAAI,eAAkBwB,EAASxB,EAAI,sBAIhD6J,KAAKC,oBAAqB,CAAE3I,OAAQnB,GAAM,IAI3CD,EAAUgB,EAAS,oBAAqBmE,SAASlF,IAC5CwB,EAASxB,EAAI,eAAkBwB,EAASxB,EAAI,sBAI5CA,EAAG2K,aAAc,SAAY3K,EAAG2K,aAAc,cACjD3K,EAAGuO,oBAAqB,OAAQ1E,KAAKC,qBACrC9J,EAAGsO,iBAAkB,OAAQzE,KAAKC,qBAClC9J,EAAG0K,aAAc,MAAO1K,EAAG2K,aAAc,aAC1C,IAKH,CAQAuD,kBAAAA,CAAoBM,GAEnB,IAAIC,IAAoBjN,EAASgN,EAAMrN,OAAQ,QAC9CuN,IAAiBlN,EAASgN,EAAMrN,OAAQ,YAErCsN,GAAmBC,IAElBF,EAAMrN,OAAOwN,QAAUH,EAAMrN,OAAOyN,SACvCJ,EAAMrN,OAAO0N,YAAc,EAC3BL,EAAMrN,OAAO6M,QAIfQ,EAAMrN,OAAOoN,oBAAqB,aAAc1E,KAAKqE,mBAEtD,CAQApE,mBAAAA,CAAqB0E,GAEpB,IAAI/B,EAAS+B,EAAMrN,OAEnB,GAAIsL,GAAUA,EAAOqC,cAAgB,CAEpC,IAAIL,IAAoBjN,EAASgN,EAAMrN,OAAQ,QAC9CuN,IAAiBlN,EAASgN,EAAMrN,OAAQ,YAEzC,GAAIsN,GAAmBC,EAAY,CAGlC,IAAIZ,EAAWjE,KAAKD,OAAOO,YAAY4D,cAIf,kBAAbD,IACVA,EAAWrB,EAAOpC,aAAc,oBAAuB7I,EAASiL,EAAQ,sBAIrE,wBAAwBxI,KAAMwI,EAAO9B,aAAc,SAAamD,EACnErB,EAAOqC,cAAcC,YAAa,mDAAoD,KAG9E,uBAAuB9K,KAAMwI,EAAO9B,aAAc,SAAamD,EACvErB,EAAOqC,cAAcC,YAAa,oBAAqB,KAIvDtC,EAAOqC,cAAcC,YAAa,cAAe,IAGnD,CAED,CAED,CAQAC,mBAAAA,CAAqBjO,EAASyJ,EAAU,IAEvCA,EAAU7K,EAAQ,CAEjBsP,eAAe,GACbzE,GAECzJ,GAAWA,EAAQU,aAEtB1B,EAAUgB,EAAS,gBAAiBmE,SAASlF,IACvCA,EAAGqK,aAAc,gBAAuC,mBAAbrK,EAAGkP,QAClDlP,EAAG0K,aAAa,wBAAyB,IACzC1K,EAAGkP,QACJ,IAIDnP,EAAUgB,EAAS,UAAWmE,SAASlF,IAClCA,EAAG8O,eAAgB9O,EAAG8O,cAAcC,YAAa,aAAc,KACnE/O,EAAGuO,oBAAqB,OAAQ1E,KAAKC,oBAAqB,IAI3D/J,EAAUgB,EAAS,qCAAsCmE,SAASlF,KAC5DA,EAAGqK,aAAc,gBAAmBrK,EAAG8O,eAAyD,mBAAjC9O,EAAG8O,cAAcC,aACpF/O,EAAG8O,cAAcC,YAAa,oDAAqD,IACpF,IAIDhP,EAAUgB,EAAS,oCAAqCmE,SAASlF,KAC3DA,EAAGqK,aAAc,gBAAmBrK,EAAG8O,eAAyD,mBAAjC9O,EAAG8O,cAAcC,aACpF/O,EAAG8O,cAAcC,YAAa,qBAAsB,IACrD,KAG6B,IAA1BvE,EAAQyE,eAEXlP,EAAUgB,EAAS,oBAAqBmE,SAASlF,IAGhDA,EAAG0K,aAAc,MAAO,eACxB1K,EAAG4K,gBAAiB,MAAO,IAK/B,ECreM,MAAMuE,EAAkB,kBAClBC,EAA6B,kBAC7BC,EAA2B,kCAI3BC,EAAgC,qFAGhCC,EAAuB,uGCArB,MAAMC,EAEpB7F,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAEA6F,MAAAA,GAEC5F,KAAK9I,QAAUa,SAASU,cAAe,OACvCuH,KAAK9I,QAAQT,UAAY,eACzBuJ,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,QAElD,CAKA4O,SAAAA,CAAWC,EAAQC,GAElB,IAAIC,EAAqB,OACrBF,EAAOG,cAAgBlG,KAAKD,OAAOoG,gBACP,QAA3BJ,EAAOK,iBAGyB,YAA3BL,EAAOK,iBAAiCpG,KAAKD,OAAOsC,oBAF5D4D,EAAqB,SAOvBjG,KAAK9I,QAAQE,MAAM0F,QAAUmJ,CAE9B,CAKAI,MAAAA,GAGKrG,KAAKD,OAAOO,YAAY4F,aAAelG,KAAK9I,UAC/C8I,KAAK9I,QAAQoP,UAAYtG,KAAKuG,iBAGhC,CAMAA,cAAAA,CAAgB7F,EAAQV,KAAKD,OAAOyG,mBAEnC,IACI9P,EADAqP,EAAS/F,KAAKD,OAAOO,YAErBmG,EDpDqD,MCsDzD,GAAmC,mBAAvBV,EAAOG,YAClBxP,EAAQqP,EAAOG,YAAaxF,OACtB,CAE4B,iBAAvBqF,EAAOG,cACjBO,EAASV,EAAOG,aAKZ,IAAI9L,KAAMqM,IAAyD,IAA7CzG,KAAKD,OAAO2G,sBAAsB/N,SAC5D8N,ED/DuC,KCmExC,IAAIE,EAAmBjG,GAAsC,cAA7BA,EAAMkG,QAAQC,WAA6B,EAAI,EAG/E,OADAnQ,EAAQ,GACA+P,GACP,IDvEuC,ICwEtC/P,EAAM4I,KAAMU,KAAKD,OAAO+G,kBAAmBpG,GAAUiG,GACrD,MACD,IDzEmD,MC0ElDjQ,EAAM4I,KAAMU,KAAKD,OAAO+G,kBAAmBpG,GAAUiG,EAAkB,IAAK3G,KAAKD,OAAOgH,kBACxF,MACD,QACC,IAAIC,EAAUhH,KAAKD,OAAOkH,WAAYvG,GACtChK,EAAM4I,KAAM0H,EAAQzJ,EAAIoJ,GACxB,IAAIO,EDjFoD,QCiF9CT,EAA2D,IAAM,IACvEzG,KAAKD,OAAOoH,gBAAiBzG,IAAUhK,EAAM4I,KAAM4H,EAAKF,EAAQvL,EAAI,GAE3E,CAEA,IAAIqG,EAAM,IAAM9B,KAAKD,OAAO5G,SAASiO,QAAS1G,GAC9C,OAAOV,KAAKqH,aAAc3Q,EAAM,GAAIA,EAAM,GAAIA,EAAM,GAAIoL,EAEzD,CAYAuF,YAAAA,CAActR,EAAGuR,EAAWtR,EAAG8L,EAAM,IAAM9B,KAAKD,OAAO5G,SAASiO,WAE/D,MAAiB,iBAANpR,GAAmBuR,MAAOvR,GAQ5B,YAAW8L,+CACc/L,2BARxB,YAAW+L,+CACa/L,4DACQuR,oDACRtR,0BASnC,CAEAwR,OAAAA,GAECxH,KAAK9I,QAAQL,QAEd,EC/Hc,MAAM4Q,EAEpB3H,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK0H,QAAU1H,KAAK0H,QAAQxH,KAAMF,MAClCA,KAAK2H,OAAS3H,KAAK2H,OAAOzH,KAAMF,MAChCA,KAAK4H,UAAY5H,KAAK4H,UAAU1H,KAAMF,KAEvC,CAEA4F,MAAAA,GAEC5F,KAAK9I,QAAUa,SAASU,cAAe,OACvCuH,KAAK9I,QAAQT,UAAY,gBAEvBuJ,KAAK6H,UAAY9P,SAASU,cAAe,SACzCuH,KAAK6H,UAAUnP,KAAO,OACtBsH,KAAK6H,UAAUpR,UAAY,sBAC3BuJ,KAAK6H,UAAUC,YAAc,gBAC/B9H,KAAK6H,UAAUpD,iBAAkB,QAASzE,KAAK0H,SAC/C1H,KAAK6H,UAAUpD,iBAAkB,UAAWzE,KAAK4H,WACjD5H,KAAK6H,UAAUpD,iBAAkB,OAAQzE,KAAK2H,QAE5C3H,KAAK9I,QAAQ4B,YAAakH,KAAK6H,UAElC,CAEAE,IAAAA,GAEC/H,KAAKgI,cAAgBhI,KAAKD,OAAOkH,aAEjCjH,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,SACjD8I,KAAK6H,UAAUI,OAEhB,CAEAC,IAAAA,GAEKlI,KAAK6E,cACR7E,KAAK9I,QAAQL,SACbmJ,KAAK6H,UAAUnR,MAAQ,GAEvB6H,aAAcyB,KAAKmI,oBACZnI,KAAKmI,YAGd,CAEAtD,SAAAA,GAEC,QAAS7E,KAAK9I,QAAQU,UAEvB,CAKAwQ,IAAAA,GAEC7J,aAAcyB,KAAKmI,oBACZnI,KAAKmI,YAEZ,IACInB,EADA9N,EAAQ8G,KAAK6H,UAAUnR,MAAMkL,KAAM,IAMvC,GAAI,QAAQxH,KAAMlB,GAAU,CAC3B,MAAMmP,EAAoBrI,KAAKD,OAAOO,YAAY4F,YAClD,GFlEwC,MEkEpCmC,GFjEgD,QEiEKA,EAAgE,CACxH,MAAM3H,EAAQV,KAAKD,OAAOuI,YAAaC,SAAUrP,EAAO,IAAO,GAC3DwH,IACHsG,EAAUhH,KAAKD,OAAOkH,WAAYvG,GAEpC,CACD,CAiBA,OAfKsG,IAGA,aAAa5M,KAAMlB,KACtBA,EAAQA,EAAMG,QAAS,IAAK,MAG7B2N,EAAUhH,KAAKD,OAAO5G,SAASqP,mBAAoBtP,EAAO,CAAEuP,eAAe,MAIvEzB,GAAW,OAAO5M,KAAMlB,IAAWA,EAAMP,OAAS,IACtDqO,EAAUhH,KAAK5G,OAAQF,IAGpB8N,GAAqB,KAAV9N,GACd8G,KAAKD,OAAOW,MAAOsG,EAAQzJ,EAAGyJ,EAAQvL,EAAGuL,EAAQpL,IAC1C,IAGPoE,KAAKD,OAAOW,MAAOV,KAAKgI,cAAczK,EAAGyC,KAAKgI,cAAcvM,EAAGuE,KAAKgI,cAAcpM,IAC3E,EAGT,CAEA8M,SAAAA,CAAWC,GAEVpK,aAAcyB,KAAKmI,aACnBnI,KAAKmI,YAAc3J,YAAY,IAAMwB,KAAKoI,QAAQO,EAEnD,CAMAvP,MAAAA,CAAQF,GAEP,MAAM0P,EAAQ,IAAIC,OAAQ,MAAQ3P,EAAM0I,OAAS,MAAO,KAElDlB,EAAQV,KAAKD,OAAOuI,YAAYQ,MAAQpI,GACtCkI,EAAMxO,KAAMsG,EAAMqI,aAG1B,OAAIrI,EACIV,KAAKD,OAAOkH,WAAYvG,GAGxB,IAGT,CAMAsI,MAAAA,GAEChJ,KAAKD,OAAOW,MAAOV,KAAKgI,cAAczK,EAAGyC,KAAKgI,cAAcvM,EAAGuE,KAAKgI,cAAcpM,GAClFoE,KAAKkI,MAEN,CAEAe,OAAAA,GAECjJ,KAAKoI,OACLpI,KAAKkI,MAEN,CAEAV,OAAAA,GAECxH,KAAK6H,UAAUnD,oBAAqB,QAAS1E,KAAK0H,SAClD1H,KAAK6H,UAAUnD,oBAAqB,UAAW1E,KAAK4H,WACpD5H,KAAK6H,UAAUnD,oBAAqB,OAAQ1E,KAAK2H,QAEjD3H,KAAK9I,QAAQL,QAEd,CAEA+Q,SAAAA,CAAWjD,GAEY,KAAlBA,EAAMuE,QACTlJ,KAAKiJ,UAEqB,KAAlBtE,EAAMuE,UACdlJ,KAAKgJ,SAELrE,EAAMwE,2BAGR,CAEAzB,OAAAA,CAAS/C,GAER3E,KAAK0I,UAAW,IAEjB,CAEAf,MAAAA,GAECnJ,YAAY,IAAMwB,KAAKkI,QAAQ,EAEhC,ECnLM,MAAMkB,EAAeC,IAE3B,IAAIC,EAAOD,EAAMtS,MAAO,qBACxB,GAAIuS,GAAQA,EAAK,GAEhB,OADAA,EAAOA,EAAK,GACL,CACNC,EAAsC,GAAnChB,SAAUe,EAAKE,OAAQ,GAAK,IAC/BvL,EAAsC,GAAnCsK,SAAUe,EAAKE,OAAQ,GAAK,IAC/BxT,EAAsC,GAAnCuS,SAAUe,EAAKE,OAAQ,GAAK,KAIjC,IAAIC,EAAOJ,EAAMtS,MAAO,qBACxB,GAAI0S,GAAQA,EAAK,GAEhB,OADAA,EAAOA,EAAK,GACL,CACNF,EAAGhB,SAAUkB,EAAK/O,MAAO,EAAG,GAAK,IACjCuD,EAAGsK,SAAUkB,EAAK/O,MAAO,EAAG,GAAK,IACjC1E,EAAGuS,SAAUkB,EAAK/O,MAAO,EAAG,GAAK,KAInC,IAAIgP,EAAML,EAAMtS,MAAO,oDACvB,GAAI2S,EACH,MAAO,CACNH,EAAGhB,SAAUmB,EAAI,GAAI,IACrBzL,EAAGsK,SAAUmB,EAAI,GAAI,IACrB1T,EAAGuS,SAAUmB,EAAI,GAAI,KAIvB,IAAIC,EAAON,EAAMtS,MAAO,gFACxB,OAAI4S,EACI,CACNJ,EAAGhB,SAAUoB,EAAK,GAAI,IACtB1L,EAAGsK,SAAUoB,EAAK,GAAI,IACtB3T,EAAGuS,SAAUoB,EAAK,GAAI,IACtB5T,EAAGiB,WAAY2S,EAAK,KAIf,IAAI,EClDG,MAAMC,EAEpB9J,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAEA6F,MAAAA,GAEC5F,KAAK9I,QAAUa,SAASU,cAAe,OACvCuH,KAAK9I,QAAQT,UAAY,cACzBuJ,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,QAElD,CAOA2S,MAAAA,GAGC7J,KAAK9I,QAAQoP,UAAY,GACzBtG,KAAK9I,QAAQP,UAAUC,IAAK,iBAG5BoJ,KAAKD,OAAO2G,sBAAsBrL,SAASyO,IAE1C,IAAIC,EAAkB/J,KAAKgK,iBAAkBF,EAAQ9J,KAAK9I,SAG1DhB,EAAU4T,EAAQ,WAAYzO,SAAS4O,IAEtCjK,KAAKgK,iBAAkBC,EAAQF,GAE/BA,EAAgBpT,UAAUC,IAAK,QAAS,GAEtC,IAKAoJ,KAAKD,OAAOO,YAAY4J,yBAE3BlK,KAAK9I,QAAQE,MAAMoK,gBAAkB,QAAUxB,KAAKD,OAAOO,YAAY4J,wBAA0B,KACjGlK,KAAK9I,QAAQE,MAAM+S,eAAiBnK,KAAKD,OAAOO,YAAY8J,uBAC5DpK,KAAK9I,QAAQE,MAAMiT,iBAAmBrK,KAAKD,OAAOO,YAAYgK,yBAC9DtK,KAAK9I,QAAQE,MAAMmT,mBAAqBvK,KAAKD,OAAOO,YAAYkK,2BAMhEhM,YAAY,KACXwB,KAAKD,OAAO8F,mBAAmBlP,UAAUC,IAAK,0BAA2B,GACvE,KAKHoJ,KAAK9I,QAAQE,MAAMoK,gBAAkB,GACrCxB,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,2BAInD,CAUAmT,gBAAAA,CAAkBtJ,EAAO+J,GAGxB,IAAIvT,EAAUa,SAASU,cAAe,OACtCvB,EAAQT,UAAY,oBAAsBiK,EAAMjK,UAAU4C,QAAS,sBAAuB,IAG1F,IAAIqR,EAAiB3S,SAASU,cAAe,OAY7C,OAXAiS,EAAejU,UAAY,2BAE3BS,EAAQ4B,YAAa4R,GACrBD,EAAU3R,YAAa5B,GAEvBwJ,EAAMU,uBAAyBlK,EAC/BwJ,EAAMY,8BAAgCoJ,EAGtC1K,KAAK2K,KAAMjK,GAEJxJ,CAER,CAQAyT,IAAAA,CAAMjK,GAEL,MAAMxJ,EAAUwJ,EAAMU,uBACrBsJ,EAAiBhK,EAAMY,8BAElBsJ,EAAO,CACZzJ,WAAYT,EAAMI,aAAc,mBAChCqJ,eAAgBzJ,EAAMI,aAAc,wBACpCU,gBAAiBd,EAAMI,aAAc,yBACrCW,gBAAiBf,EAAMI,aAAc,yBACrCS,iBAAkBb,EAAMI,aAAc,0BACtC+J,gBAAiBnK,EAAMI,aAAc,yBACrCgK,mBAAoBpK,EAAMI,aAAc,4BACxCuJ,iBAAkB3J,EAAMI,aAAc,0BACtCyJ,mBAAoB7J,EAAMI,aAAc,4BACxCiK,qBAAsBrK,EAAMI,aAAc,8BAC1CkK,kBAAmBtK,EAAMI,aAAc,4BAGlCmK,EAAcvK,EAAMF,aAAc,gBAIxCE,EAAM/J,UAAUE,OAAQ,uBACxB6J,EAAM/J,UAAUE,OAAQ,wBAExBK,EAAQ6J,gBAAiB,eACzB7J,EAAQ6J,gBAAiB,wBACzB7J,EAAQ6J,gBAAiB,wBACzB7J,EAAQ6J,gBAAiB,8BACzB7J,EAAQE,MAAMyT,gBAAkB,GAEhCH,EAAetT,MAAM+S,eAAiB,GACtCO,EAAetT,MAAMiT,iBAAmB,GACxCK,EAAetT,MAAMmT,mBAAqB,GAC1CG,EAAetT,MAAMoK,gBAAkB,GACvCkJ,EAAetT,MAAM8T,QAAU,GAC/BR,EAAepE,UAAY,GAEvBsE,EAAKzJ,aAEJ,sBAAsB/G,KAAMwQ,EAAKzJ,aAAgB,gDAAgD/G,KAAMwQ,EAAKzJ,YAC/GT,EAAMG,aAAc,wBAAyB+J,EAAKzJ,YAGlDjK,EAAQE,MAAM+J,WAAayJ,EAAKzJ,aAO9ByJ,EAAKzJ,YAAcyJ,EAAKC,iBAAmBD,EAAKE,oBAAsBF,EAAKpJ,iBAAmBoJ,EAAKnJ,iBAAmBmJ,EAAKrJ,mBAC9HrK,EAAQ2J,aAAc,uBAAwB+J,EAAKzJ,WACvCyJ,EAAKT,eACLS,EAAKpJ,gBACLoJ,EAAKnJ,gBACLmJ,EAAKrJ,iBACLqJ,EAAKC,gBACLD,EAAKE,mBACLF,EAAKP,iBACLO,EAAKL,mBACLK,EAAKG,qBACLH,EAAKI,mBAIdJ,EAAKT,gBAAiBjT,EAAQ2J,aAAc,uBAAwB+J,EAAKT,gBACzES,EAAKC,kBAAkB3T,EAAQE,MAAMyT,gBAAkBD,EAAKC,iBAC5DD,EAAKE,qBAAqB5T,EAAQE,MAAMoK,gBAAkBoJ,EAAKE,oBAC/DF,EAAKG,sBAAuB7T,EAAQ2J,aAAc,6BAA8B+J,EAAKG,sBAErFE,GAAc/T,EAAQ2J,aAAc,eAAgB,IAGpD+J,EAAKT,iBAAiBO,EAAetT,MAAM+S,eAAiBS,EAAKT,gBACjES,EAAKP,mBAAmBK,EAAetT,MAAMiT,iBAAmBO,EAAKP,kBACrEO,EAAKL,qBAAqBG,EAAetT,MAAMmT,mBAAqBK,EAAKL,oBACzEK,EAAKI,oBAAoBN,EAAetT,MAAM8T,QAAUN,EAAKI,mBAEjE,MAAMG,EAAgBnL,KAAKoL,iBAAkB1K,GAEhB,iBAAlByK,GACVzK,EAAM/J,UAAUC,IAAKuU,EAGvB,CAUAC,gBAAAA,CAAkB1K,GAEjB,MAAMxJ,EAAUwJ,EAAMU,uBAKtB,IAAIiK,EAAgB3K,EAAMI,aAAc,yBAGxC,IAAKuK,IAAkBjC,EAAYiC,GAAkB,CACpD,IAAIC,EAA0B1L,OAAOhD,iBAAkB1F,GACnDoU,GAA2BA,EAAwBT,kBACtDQ,EAAgBC,EAAwBT,gBAE1C,CAEA,GAAIQ,EAAgB,CACnB,MAAM3B,EAAMN,EAAYiC,GAKxB,GAAI3B,GAAiB,IAAVA,EAAI3T,EACd,MDpKkB,iBAFWsT,ECsKRgC,KDpKQhC,EAAQD,EAAYC,KAEhDA,GACgB,IAAVA,EAAME,EAAoB,IAAVF,EAAMpL,EAAoB,IAAVoL,EAAMrT,GAAY,IAGrD,MC8JmC,IAC/B,sBAGA,sBAGV,CD7K+BqT,MC+K/B,OAAO,IAER,CAKAkC,iCAAAA,CAAmC7K,EAAOpJ,GAEzC,CAAE,uBAAwB,uBAAwB+D,SAASmQ,IACtD9K,EAAM/J,UAAU8U,SAAUD,GAC7BlU,EAAOX,UAAUC,IAAK4U,GAGtBlU,EAAOX,UAAUE,OAAQ2U,EAC1B,GACExL,KAEJ,CASAqG,MAAAA,CAAQqF,GAAa,GAEpB,IAAI3F,EAAS/F,KAAKD,OAAOO,YACrBqL,EAAe3L,KAAKD,OAAOyG,kBAC3BQ,EAAUhH,KAAKD,OAAOkH,aAEtB2E,EAAoB,KAGpBC,EAAiB9F,EAAO+F,IAAM,SAAW,OAC5CC,EAAmBhG,EAAO+F,IAAM,OAAS,SAoD1C,GAhDAzV,MAAMC,KAAM0J,KAAK9I,QAAQ8U,YAAa3Q,SAAS,CAAE4Q,EAAa1O,KAE7D0O,EAAYtV,UAAUE,OAAQ,OAAQ,UAAW,UAE7C0G,EAAIyJ,EAAQzJ,EACf0O,EAAYtV,UAAUC,IAAKiV,GAElBtO,EAAIyJ,EAAQzJ,EACrB0O,EAAYtV,UAAUC,IAAKmV,IAG3BE,EAAYtV,UAAUC,IAAK,WAG3BgV,EAAoBK,IAGjBP,GAAcnO,IAAMyJ,EAAQzJ,IAC/BrH,EAAU+V,EAAa,qBAAsB5Q,SAAS,CAAE6Q,EAAazQ,KAEpEyQ,EAAYvV,UAAUE,OAAQ,OAAQ,UAAW,UAEjD,MAAMsV,EAA8B,iBAAdnF,EAAQvL,EAAiBuL,EAAQvL,EAAI,EAEvDA,EAAI0Q,EACPD,EAAYvV,UAAUC,IAAK,QAElB6E,EAAI0Q,EACbD,EAAYvV,UAAUC,IAAK,WAG3BsV,EAAYvV,UAAUC,IAAK,WAGvB2G,IAAMyJ,EAAQzJ,IAAIqO,EAAoBM,GAC3C,GAGF,IAMGlM,KAAKoM,qBAAuBpM,KAAKoM,mBAAmBzU,QAAS,UAChEqI,KAAKoM,mBAAqB,MAGvBR,GAAqB5L,KAAKoM,mBAAqB,CAIlD,IAAIC,EAAyBrM,KAAKoM,mBAAmBtL,aAAc,wBAC/DwL,EAAwBV,EAAkB9K,aAAc,wBAE5D,GAAIwL,GAAyBA,IAA0BD,GAA0BT,IAAsB5L,KAAKoM,mBAAqB,CAChIpM,KAAK9I,QAAQP,UAAUC,IAAK,iBAK5B,MAAM2V,EAAeX,EAAkB1I,cAAe,SAChDsJ,EAAgBxM,KAAKoM,mBAAmBlJ,cAAe,SAE7D,GAAIqJ,GAAgBC,EAAgB,CAEnC,MAAMC,EAAqBF,EAAa3U,WACZ4U,EAAc5U,WAGtBkB,YAAayT,GACjCE,EAAmB3T,YAAa0T,EAEjC,CACD,CAED,CAUA,GAPIxM,KAAKoM,oBAERpM,KAAKD,OAAO2M,aAAavH,oBAAqBnF,KAAKoM,mBAAoB,CAAEhH,eAAgBpF,KAAKD,OAAO2M,aAAavM,cAAeH,KAAKoM,sBAKnIR,EAAoB,CAEvB5L,KAAKD,OAAO2M,aAAa1I,qBAAsB4H,GAE/C,IAAIe,EAA2Bf,EAAkB1I,cAAe,6BAChE,GAAIyJ,EAA2B,CAE9B,IAAIC,EAAqBD,EAAyBvV,MAAMoK,iBAAmB,GAGvE,SAASpH,KAAMwS,KAClBD,EAAyBvV,MAAMoK,gBAAkB,GACjD5B,OAAOhD,iBAAkB+P,GAA2BzB,QACpDyB,EAAyBvV,MAAMoK,gBAAkBoL,EAGnD,CAEA5M,KAAKoM,mBAAqBR,CAE3B,CAIID,GACH3L,KAAKuL,kCAAmCI,EAAc3L,KAAKD,OAAO8F,oBAInErH,YAAY,KACXwB,KAAK9I,QAAQP,UAAUE,OAAQ,gBAAiB,GAC9C,GAEJ,CAMAgW,cAAAA,GAEC,IAAI7F,EAAUhH,KAAKD,OAAOkH,aAE1B,GAAIjH,KAAKD,OAAOO,YAAY4J,wBAA0B,CAErD,IAIC4C,EAAiBC,EAJdC,EAAmBhN,KAAKD,OAAO2G,sBAClCuG,EAAiBjN,KAAKD,OAAOmN,oBAE1B/C,EAAiBnK,KAAK9I,QAAQE,MAAM+S,eAAe7Q,MAAO,KAGhC,IAA1B6Q,EAAexR,OAClBmU,EAAkBC,EAAmBxE,SAAU4B,EAAe,GAAI,KAGlE2C,EAAkBvE,SAAU4B,EAAe,GAAI,IAC/C4C,EAAmBxE,SAAU4B,EAAe,GAAI,KAGjD,IAECgD,EACAxG,EAHGyG,EAAapN,KAAK9I,QAAQmW,YAC7BC,EAAuBN,EAAiBrU,OAKxCwU,EADmE,iBAAzDnN,KAAKD,OAAOO,YAAYiN,6BACLvN,KAAKD,OAAOO,YAAYiN,6BAGxBD,EAAuB,GAAMR,EAAkBM,IAAiBE,EAAqB,GAAM,EAGzH3G,EAAmBwG,EAA6BnG,EAAQzJ,GAAK,EAE7D,IAECiQ,EACAC,EAHGC,EAAc1N,KAAK9I,QAAQyW,aAC9BC,EAAqBX,EAAetU,OAKpC6U,EADiE,iBAAvDxN,KAAKD,OAAOO,YAAYuN,2BACP7N,KAAKD,OAAOO,YAAYuN,4BAGtBd,EAAmBW,IAAkBE,EAAmB,GAGtFH,EAAiBG,EAAqB,EAAKJ,EAA2BxG,EAAQvL,EAAI,EAElFuE,KAAK9I,QAAQE,MAAMmT,mBAAqB5D,EAAmB,OAAS8G,EAAiB,IAEtF,CAED,CAEAjG,OAAAA,GAECxH,KAAK9I,QAAQL,QAEd,EC7cD,IAAIiX,EAAqB,EAMV,MAAMC,EAEpBjO,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAQAiO,GAAAA,CAAKC,EAAWC,GAGflO,KAAKmO,QAEL,IAAIC,EAAYpO,KAAKD,OAAOuI,YACxB+F,EAAeD,EAAUrK,QAASmK,GAClCI,EAAiBF,EAAUrK,QAASkK,GAQxC,GAAIA,GAAaC,GAAWD,EAAUzN,aAAc,sBAAyB0N,EAAQ1N,aAAc,sBAC9FyN,EAAUnN,aAAc,0BAA6BoN,EAAQpN,aAAc,2BACxEuN,EAAeC,EAAiBJ,EAAUD,GAAYzN,aAAc,6BAAgC,CAG3GR,KAAKuO,sBAAwBvO,KAAKuO,uBAAyBhW,IAE3D,IAAIiW,EAAmBxO,KAAKyO,sBAAuBP,GAGnDD,EAAUrH,QAAQ8H,YAAc,UAChCR,EAAQtH,QAAQ8H,YAAc,UAG9BF,EAAiBG,eAAiBN,EAAeC,EAAiB,UAAY,WAK9E,IAAIM,EAAgD,SAA5BX,EAAU7W,MAAM0F,QACpC8R,IAAoBX,EAAU7W,MAAM0F,QAAUkD,KAAKD,OAAOO,YAAYxD,SAG1E,IAAI+R,EAAM7O,KAAK8O,0BAA2Bb,EAAWC,GAAU9O,KAAK2P,GAC5D/O,KAAKgP,oBAAqBD,EAASzY,KAAMyY,EAASE,GAAIF,EAASpO,SAAW,CAAE,EAAE6N,EAAkBV,OAMxG,GAHIc,IAAoBX,EAAU7W,MAAM0F,QAAU,QAGL,UAAzCoR,EAAQtH,QAAQsI,uBAAqF,IAAjDlP,KAAKD,OAAOO,YAAY4O,qBAAgC,CAG/G,IAAIC,EAAuD,GAA5BX,EAAiBY,SAC/CC,EAAoD,GAA5Bb,EAAiBY,SAE1CpP,KAAKsP,gCAAiCpB,GAAU7S,SAASkU,IAExD,IAAIC,EAAmBxP,KAAKyO,sBAAuBc,EAAkBf,GACjEiB,EAAK,YAILD,EAAiBJ,WAAaZ,EAAiBY,UAAYI,EAAiB7G,QAAU6F,EAAiB7F,QAC1G8G,EAAK,aAAe3B,IACpBe,EAAIvP,KAAO,4DAA2DmQ,6BAA8BD,EAAiBJ,kBAAkBI,EAAiB7G,cAGzJ4G,EAAiB3I,QAAQ8I,kBAAoBD,CAAE,GAE7CzP,MAGH6O,EAAIvP,KAAO,8FAA6F6P,WAAkCE,QAE3I,CAKArP,KAAKuO,sBAAsBjI,UAAYuI,EAAIzM,KAAM,IAGjDrH,uBAAuB,KAClBiF,KAAKuO,wBAER3R,iBAAkBoD,KAAKuO,uBAAwBoB,WAE/CzB,EAAQtH,QAAQ8H,YAAc,UAC/B,IAGD1O,KAAKD,OAAO9C,cAAc,CACzBvE,KAAM,cACNkS,KAAM,CACLqD,YACAC,UACA0B,MAAO5P,KAAKuO,wBAIf,CAED,CAMAJ,KAAAA,GAGCjY,EAAU8J,KAAKD,OAAO8F,mBAAoB,mDAAoDxK,SAASnE,IACtGA,EAAQ0P,QAAQ8H,YAAc,EAAE,IAIjCxY,EAAU8J,KAAKD,OAAO8F,mBAAoB,8BAA+BxK,SAASnE,WAC1EA,EAAQ0P,QAAQ8I,iBAAiB,IAIrC1P,KAAKuO,uBAAyBvO,KAAKuO,sBAAsB3W,aAC5DoI,KAAKuO,sBAAsB3W,WAAWiY,YAAa7P,KAAKuO,uBACxDvO,KAAKuO,sBAAwB,KAG/B,CAcAS,mBAAAA,CAAqB1Y,EAAM2Y,EAAIa,EAAgBtB,EAAkBiB,GAIhEnZ,EAAKsQ,QAAQ8I,kBAAoB,GACjCT,EAAGrI,QAAQ8I,kBAAoBD,EAI/B,IAAI9O,EAAUX,KAAKyO,sBAAuBQ,EAAIT,QAIV,IAAzBsB,EAAenH,QAAwBhI,EAAQgI,MAAQmH,EAAenH,YAC1C,IAA5BmH,EAAeV,WAA2BzO,EAAQyO,SAAWU,EAAeV,eAClD,IAA1BU,EAAeC,SAAyBpP,EAAQoP,OAASD,EAAeC,QAEnF,IAAIC,EAAYhQ,KAAKiQ,4BAA6B,OAAQ3Z,EAAMwZ,GAC/DI,EAAUlQ,KAAKiQ,4BAA6B,KAAMhB,EAAIa,GAKvD,GAAIb,EAAGtY,UAAU8U,SAAU,qBAInByE,EAAQC,OAAgB,QAE3B7Z,EAAKK,UAAU8U,SAAU,aAAe,EAEjBnV,EAAKG,UAAUM,MAAO2O,IAA0B,CAAC,KAAM,MACzDuJ,EAAGxY,UAAUM,MAAO2O,IAA0B,CAAC,KAAM,IAII,YAApC8I,EAAiBG,gBAC7DM,EAAGtY,UAAUC,IAAK,UAAW,WAG/B,CAOD,IAAiC,IAA7BkZ,EAAeM,YAAgD,IAAzBN,EAAeO,MAAkB,CAE1E,IAAIC,EAAoBtQ,KAAKD,OAAOwQ,WAEhCC,EAAQ,CACX/R,GAAKuR,EAAUvR,EAAIyR,EAAQzR,GAAM6R,EACjC9U,GAAKwU,EAAUxU,EAAI0U,EAAQ1U,GAAM8U,EACjCG,OAAQT,EAAUnN,MAAQqN,EAAQrN,MAClC6N,OAAQV,EAAUlN,OAASoN,EAAQpN,QAIpC0N,EAAM/R,EAAIrC,KAAKuU,MAAiB,IAAVH,EAAM/R,GAAa,IACzC+R,EAAMhV,EAAIY,KAAKuU,MAAiB,IAAVH,EAAMhV,GAAa,IACzCgV,EAAMC,OAASrU,KAAKuU,MAAsB,IAAfH,EAAMC,QAAkB,IACnDD,EAAMC,OAASrU,KAAKuU,MAAsB,IAAfH,EAAMC,QAAkB,IAEnD,IAAIL,GAAyC,IAA7BN,EAAeM,YAAqC,IAAZI,EAAM/R,GAAuB,IAAZ+R,EAAMhV,GAC9E6U,GAAiC,IAAzBP,EAAeO,QAAsC,IAAjBG,EAAMC,QAAiC,IAAjBD,EAAME,QAGzE,GAAIN,GAAaC,EAAQ,CAExB,IAAIlZ,EAAY,GAEZiZ,GAAYjZ,EAAUmI,KAAO,aAAYkR,EAAM/R,QAAQ+R,EAAMhV,QAC7D6U,GAAQlZ,EAAUmI,KAAO,SAAQkR,EAAMC,WAAWD,EAAME,WAE5DV,EAAUG,OAAkB,UAAIhZ,EAAUiL,KAAM,KAChD4N,EAAUG,OAAO,oBAAsB,WAEvCD,EAAQC,OAAkB,UAAI,MAE/B,CAED,CAGA,IAAK,IAAIS,KAAgBV,EAAQC,OAAS,CACzC,MAAMU,EAAUX,EAAQC,OAAOS,GACzBE,EAAYd,EAAUG,OAAOS,GAE/BC,IAAYC,SACRZ,EAAQC,OAAOS,KAKQ,IAA1BC,EAAQE,gBACXb,EAAQC,OAAOS,GAAgBC,EAAQna,QAGR,IAA5Boa,EAAUC,gBACbf,EAAUG,OAAOS,GAAgBE,EAAUpa,OAG9C,CAEA,IAAImY,EAAM,GAENmC,EAAoBpS,OAAOqS,KAAMf,EAAQC,QAI7C,GAAIa,EAAkBrY,OAAS,EAAI,CAGlCqX,EAAUG,OAAmB,WAAI,OAGjCD,EAAQC,OAAmB,WAAK,OAAMxP,EAAQyO,aAAazO,EAAQoP,UAAUpP,EAAQgI,SACrFuH,EAAQC,OAAO,uBAAyBa,EAAkB5O,KAAM,MAChE8N,EAAQC,OAAO,eAAiBa,EAAkB5O,KAAM,MAYxDyM,EAAO,8BAA+BY,EAAI,OAR5B7Q,OAAOqS,KAAMjB,EAAUG,QAAS/Q,KAAKwR,GAC3CA,EAAe,KAAOZ,EAAUG,OAAOS,GAAgB,iBAC3DxO,KAAM,IAMH,6DACwDqN,EAAI,OALvD7Q,OAAOqS,KAAMf,EAAQC,QAAS/Q,KAAKwR,GACvCA,EAAe,KAAOV,EAAQC,OAAOS,GAAgB,iBACzDxO,KAAM,IAGwE,GAEnF,CAEA,OAAOyM,CAER,CAUAJ,qBAAAA,CAAuBvX,EAASga,GAE/B,IAAIvQ,EAAU,CACboP,OAAQ/P,KAAKD,OAAOO,YAAY6Q,kBAChC/B,SAAUpP,KAAKD,OAAOO,YAAY8Q,oBAClCzI,MAAO,GAMR,GAHAhI,EAAU7K,EAAQ6K,EAASuQ,GAGvBha,EAAQU,WAAa,CACxB,IAAIyZ,EAAqB1Z,EAAST,EAAQU,WAAY,8BAClDyZ,IACH1Q,EAAUX,KAAKyO,sBAAuB4C,EAAoB1Q,GAE5D,CAcA,OAZIzJ,EAAQ0P,QAAQuK,oBACnBxQ,EAAQoP,OAAS7Y,EAAQ0P,QAAQuK,mBAG9Bja,EAAQ0P,QAAQwK,sBACnBzQ,EAAQyO,SAAWpY,WAAYE,EAAQ0P,QAAQwK,sBAG5Cla,EAAQ0P,QAAQ0K,mBACnB3Q,EAAQgI,MAAQ3R,WAAYE,EAAQ0P,QAAQ0K,mBAGtC3Q,CAER,CASAsP,2BAAAA,CAA6BsB,EAAWra,EAAS4Y,GAEhD,IAAI/J,EAAS/F,KAAKD,OAAOO,YAErBkR,EAAa,CAAErB,OAAQ,IAG3B,IAAiC,IAA7BL,EAAeM,YAAgD,IAAzBN,EAAeO,MAAkB,CAC1E,IAAIoB,EAIJ,GAAsC,mBAA3B3B,EAAe4B,QACzBD,EAAS3B,EAAe4B,QAASxa,QAGjC,GAAI6O,EAAO4L,OAGVF,EAASva,EAAQ0a,4BAEb,CACJ,IAAIvB,EAAQrQ,KAAKD,OAAOwQ,WACxBkB,EAAS,CACRhT,EAAGvH,EAAQ2a,WAAaxB,EACxB7U,EAAGtE,EAAQ4a,UAAYzB,EACvBxN,MAAO3L,EAAQmW,YAAcgD,EAC7BvN,OAAQ5L,EAAQyW,aAAe0C,EAEjC,CAGDmB,EAAW/S,EAAIgT,EAAOhT,EACtB+S,EAAWhW,EAAIiW,EAAOjW,EACtBgW,EAAW3O,MAAQ4O,EAAO5O,MAC1B2O,EAAW1O,OAAS2O,EAAO3O,MAC5B,CAEA,MAAMiP,EAAiBnV,iBAAkB1F,GAgCzC,OA7BE4Y,EAAeK,QAAUpK,EAAOiM,mBAAoB3W,SAASjE,IAC9D,IAAIV,EAIiB,iBAAVU,IAAqBA,EAAQ,CAAE6a,SAAU7a,SAE1B,IAAfA,EAAMd,MAAsC,SAAdib,EACxC7a,EAAQ,CAAEA,MAAOU,EAAMd,KAAMya,eAAe,QAEhB,IAAb3Z,EAAM6X,IAAoC,OAAdsC,EAC3C7a,EAAQ,CAAEA,MAAOU,EAAM6X,GAAI8B,eAAe,IAInB,gBAAnB3Z,EAAM6a,WACTvb,EAAQM,WAAY+a,EAAe,gBAAmB/a,WAAY+a,EAAe,eAG9ExK,MAAM7Q,KACTA,EAAQqb,EAAe3a,EAAM6a,YAIjB,KAAVvb,IACH8a,EAAWrB,OAAO/Y,EAAM6a,UAAYvb,EACrC,IAGM8a,CAER,CAaA1C,yBAAAA,CAA2Bb,EAAWC,GAErC,IAEIgE,GAFgE,mBAA/ClS,KAAKD,OAAOO,YAAY6R,mBAAoCnS,KAAKD,OAAOO,YAAY6R,mBAAqBnS,KAAKoS,qBAE/G1a,KAAMsI,KAAMiO,EAAWC,GAEvCmE,EAAW,GAGf,OAAOH,EAAMjX,QAAQ,CAAEqX,EAAMC,KAC5B,IAAqC,IAAjCF,EAAStO,QAASuO,EAAKrD,IAE1B,OADAoD,EAAS/S,KAAMgT,EAAKrD,KACb,CACR,GAGF,CAQAmD,mBAAAA,CAAqBnE,EAAWC,GAE/B,IAAIgE,EAAQ,GAEZ,MACMM,EAAY,gCA0DlB,OAtDAxS,KAAKyS,uBAAwBP,EAAOjE,EAAWC,EAAS,aAAawE,GAC7DA,EAAKC,SAAW,MAAQD,EAAK5R,aAAc,aAInDd,KAAKyS,uBAAwBP,EAAOjE,EAAWC,EAASsE,GAAWE,GAC3DA,EAAKC,SAAW,MAAQD,EAAK3J,YAIrC/I,KAAKyS,uBAAwBP,EAAOjE,EAAWC,EAb5B,sBAaiDwE,GAC5DA,EAAKC,SAAW,OAAUD,EAAK5R,aAAc,QAAW4R,EAAK5R,aAAc,eAInFd,KAAKyS,uBAAwBP,EAAOjE,EAAWC,EApB7B,OAoBiDwE,GAC3DA,EAAKC,SAAW,MAAQD,EAAK3J,YAGrCmJ,EAAM7W,SAASiX,IAGVjb,EAASib,EAAKhc,KAAMkc,GACvBF,EAAK3R,QAAU,CAAE0P,OAAO,GAGhBhZ,EAASib,EAAKhc,KA/BN,SAmChBgc,EAAK3R,QAAU,CAAE0P,OAAO,EAAOF,OAAQ,CAAE,QAAS,WAGlDnQ,KAAKyS,uBAAwBP,EAAOI,EAAKhc,KAAMgc,EAAKrD,GAAI,uBAAuByD,GACvEA,EAAKE,aACV,CACFvC,OAAO,EACPF,OAAQ,GACRuB,QAAS1R,KAAK6S,oBAAoB3S,KAAMF,QAIzCA,KAAKyS,uBAAwBP,EAAOI,EAAKhc,KAAMgc,EAAKrD,GAAI,4CAA4CyD,GAC5FA,EAAK5R,aAAc,qBACxB,CACFuP,OAAO,EACPF,OAAQ,CAAE,SACVuB,QAAS1R,KAAK6S,oBAAoB3S,KAAMF,QAG1C,GAEEA,MAEIkS,CAER,CASAW,mBAAAA,CAAqB3b,GAEpB,MAAMoZ,EAAoBtQ,KAAKD,OAAOwQ,WAEtC,MAAO,CACN9R,EAAGrC,KAAKuU,MAASzZ,EAAQ2a,WAAavB,EAAsB,KAAQ,IACpE9U,EAAGY,KAAKuU,MAASzZ,EAAQ4a,UAAYxB,EAAsB,KAAQ,IACnEzN,MAAOzG,KAAKuU,MAASzZ,EAAQmW,YAAciD,EAAsB,KAAQ,IACzExN,OAAQ1G,KAAKuU,MAASzZ,EAAQyW,aAAe2C,EAAsB,KAAQ,IAG7E,CAaAmC,sBAAAA,CAAwBP,EAAOY,EAAWC,EAAS3c,EAAU4c,EAAYxE,GAExE,IAAIyE,EAAc,CAAA,EACdC,EAAY,CAAA,EAEhB,GAAGxY,MAAMhD,KAAMob,EAAUvc,iBAAkBH,IAAaiF,SAAS,CAAEnE,EAASjB,KAC3E,MAAMkd,EAAMH,EAAY9b,GACL,iBAARic,GAAoBA,EAAIxa,SAClCsa,EAAYE,GAAOF,EAAYE,IAAQ,GACvCF,EAAYE,GAAK7T,KAAMpI,GACxB,IAGD,GAAGwD,MAAMhD,KAAMqb,EAAQxc,iBAAkBH,IAAaiF,SAAS,CAAEnE,EAASjB,KACzE,MAAMkd,EAAMH,EAAY9b,GAIxB,IAAIkc,EAGJ,GANAF,EAAUC,GAAOD,EAAUC,IAAQ,GACnCD,EAAUC,GAAK7T,KAAMpI,GAKjB+b,EAAYE,GAAO,CACtB,MAAME,EAAeH,EAAUC,GAAKxa,OAAS,EACvC2a,EAAiBL,EAAYE,GAAKxa,OAAS,EAI7Csa,EAAYE,GAAME,IACrBD,EAAcH,EAAYE,GAAME,GAChCJ,EAAYE,GAAME,GAAiB,MAI3BJ,EAAYE,GAAMG,KAC1BF,EAAcH,EAAYE,GAAMG,GAChCL,EAAYE,GAAMG,GAAmB,KAEvC,CAGIF,GACHlB,EAAM5S,KAAK,CACVhJ,KAAM8c,EACNnE,GAAI/X,EACJyJ,QAAS6N,GAEX,GAGF,CAcAc,+BAAAA,CAAiCiE,GAEhC,MAAO,GAAG7Y,MAAMhD,KAAM6b,EAAYC,UAAWC,QAAQ,CAAEC,EAAQxc,KAE9D,MAAMyc,EAA2Bzc,EAAQgM,cAAe,8BAaxD,OARKhM,EAAQsJ,aAAc,6BAAiCmT,GAC3DD,EAAOpU,KAAMpI,GAGVA,EAAQgM,cAAe,gCAC1BwQ,EAASA,EAAO3U,OAAQiB,KAAKsP,gCAAiCpY,KAGxDwc,CAAM,GAEX,GAEJ,ECpnBc,MAAME,EAEpB9T,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK7E,QAAS,EACd6E,KAAK6T,mBAAqB,GAE1B7T,KAAK8T,SAAW9T,KAAK8T,SAAS5T,KAAMF,KAErC,CAMA+T,QAAAA,GAEC,GAAI/T,KAAK7E,OAAS,OAElB,MAAM6Y,EAAwBhU,KAAKD,OAAOkU,WAE1CjU,KAAK7E,QAAS,EAId6E,KAAKkU,0BAA4BlU,KAAKD,OAAO8D,mBAAmByC,UAEhE,MAAM0G,EAAmB9W,EAAU8J,KAAKD,OAAO8F,mBAAoBN,GAC7D4O,EAAwBje,EAAU8J,KAAKD,OAAO8F,mBNtCP,kCM0C7C,IAAIuO,EAFJpU,KAAKqU,gBAAgB1d,UAAUC,IAAK,sBAAuB,iBAI3D,MAAM0d,EAAiB1U,OAAOhD,iBAAkBoD,KAAKqU,iBACjDC,GAAkBA,EAAenT,aACpCiT,EAAyBE,EAAenT,YAGzC,MAAMoT,EAAe,GACfC,EAAgBxH,EAAiB,GAAGpV,WAE1C,IAAI6c,EAIJ,MAAMC,EAAoBA,CAAEhU,EAAOnD,EAAG9B,EAAGkZ,KAExC,IAAIC,EAIJ,GAAIH,GAAiBzU,KAAKD,OAAO8U,yBAA0BJ,EAAe/T,GACzEkU,EAAmB7c,SAASU,cAAe,OAC3Cmc,EAAiBne,UAAY,+CAC7Bme,EAAiBxd,MAAM0F,QAAU,OACjC2X,EAAc9c,QAAS,wBAAyBC,WAAWkB,YAAa8b,OAEpE,CAGJ,MAAME,EAAO/c,SAASU,cAAe,OAOrC,GANAqc,EAAKre,UAAY,cACjB8d,EAAajV,KAAMwV,GAKfH,GAAcR,EAAsBxb,OAAS4E,EAAI,CACpD,MAAMwX,EAAkBZ,EAAsB5W,GACxCyX,EAAiBpV,OAAOhD,iBAAkBmY,GAE5CC,GAAkBA,EAAe7T,WACpC2T,EAAK1d,MAAM+J,WAAa6T,EAAe7T,WAE/BiT,IACRU,EAAK1d,MAAM+J,WAAaiT,EAEzB,MAAUA,IACVU,EAAK1d,MAAM+J,WAAaiT,GAGzB,MAAMa,EAAkBld,SAASU,cAAe,OAChDwc,EAAgBxe,UAAY,qBAC5Bqe,EAAKhc,YAAamc,GAElBL,EAAmB7c,SAASU,cAAe,OAC3Cmc,EAAiBne,UAAY,sBAC7Bwe,EAAgBnc,YAAa8b,EAC9B,CAEAA,EAAiB9b,YAAa4H,GAE9BA,EAAM/J,UAAUE,OAAQ,OAAQ,UAChC6J,EAAMG,aAAc,eAAgBtD,GACpCmD,EAAMG,aAAc,eAAgBpF,GAEhCiF,EAAMU,yBACTV,EAAMU,uBAAuBvK,OAAQ,OAAQ,UAC7C+d,EAAiBM,aAAcxU,EAAMU,uBAAwBV,IAG9D+T,EAAgB/T,CAAK,EAKtBsM,EAAiB3R,SAAS,CAAE8Z,EAAiB5X,KAExCyC,KAAKD,OAAOqV,gBAAiBD,GAChCA,EAAgB5e,iBAAkB,WAAY8E,SAAS,CAAEga,EAAe5Z,KACvEiZ,EAAmBW,EAAe9X,EAAG9B,GAAG,EAAM,IAI/CiZ,EAAmBS,EAAiB5X,EAAG,EACxC,GAEEyC,MAEHA,KAAKsV,oBAGLpf,EAAU8J,KAAKD,OAAO8F,mBAAoB,UAAWxK,SAASka,GAASA,EAAM1e,WAG7E0d,EAAalZ,SAASyZ,GAAQN,EAAc1b,YAAagc,KAGzD9U,KAAKD,OAAO2M,aAAavJ,OAAQnD,KAAKD,OAAO8D,oBAE7C7D,KAAKD,OAAOoD,SACZnD,KAAKD,OAAOyV,SAAUxB,GAEtBhU,KAAK6T,mBAAmBxY,SAASoa,GAAYA,MAC7CzV,KAAK6T,mBAAqB,GAE1B7T,KAAK0V,wBAEL1V,KAAKqU,gBAAgB1d,UAAUE,OAAQ,uBACvCmJ,KAAKqU,gBAAgB5P,iBAAkB,SAAUzE,KAAK8T,SAAU,CAAE6B,SAAS,GAE5E,CAMAC,UAAAA,GAEC,IAAK5V,KAAK7E,OAAS,OAEnB,MAAM0a,EAA0B7V,KAAKD,OAAOkU,WAE5CjU,KAAK7E,QAAS,EAEd6E,KAAKqU,gBAAgB3P,oBAAqB,SAAU1E,KAAK8T,UACzD9T,KAAKqU,gBAAgB1d,UAAUE,OAAQ,iBAEvCmJ,KAAK8V,oBAEL9V,KAAKD,OAAO8D,mBAAmByC,UAAYtG,KAAKkU,0BAChDlU,KAAKD,OAAO4K,OACZ3K,KAAKD,OAAOyV,SAAUK,GAEtB7V,KAAKkU,0BAA4B,IAElC,CAEA6B,MAAAA,CAAQC,GAEiB,kBAAbA,EACVA,EAAWhW,KAAK+T,WAAa/T,KAAK4V,aAGlC5V,KAAKiW,WAAajW,KAAK4V,aAAe5V,KAAK+T,UAG7C,CAKAkC,QAAAA,GAEC,OAAOjW,KAAK7E,MAEb,CAKAma,iBAAAA,GAECtV,KAAKkW,YAAcne,SAASU,cAAe,OAC3CuH,KAAKkW,YAAYzf,UAAY,YAE7BuJ,KAAKmW,iBAAmBpe,SAASU,cAAe,OAChDuH,KAAKmW,iBAAiB1f,UAAY,kBAClCuJ,KAAKkW,YAAYpd,YAAakH,KAAKmW,kBAEnCnW,KAAKoW,oBAAsBre,SAASU,cAAe,OACnDuH,KAAKoW,oBAAoB3f,UAAY,qBACrCuJ,KAAKmW,iBAAiBrd,YAAakH,KAAKoW,qBAExCpW,KAAKqU,gBAAgBa,aAAclV,KAAKkW,YAAalW,KAAKqU,gBAAgBgC,YAE1E,MAAMC,EAA4B3R,IAEjC,IAAI4R,GAAa5R,EAAM6R,QAAUxW,KAAKmW,iBAAiBvE,wBAAwB6E,KAAQzW,KAAK0W,kBAC5FH,EAAWna,KAAKE,IAAKF,KAAKC,IAAKka,EAAU,GAAK,GAE9CvW,KAAKqU,gBAAgBsC,UAAYJ,GAAavW,KAAKqU,gBAAgBuC,aAAe5W,KAAKqU,gBAAgB1G,aAAc,EAIhHkJ,EAA0BlS,IAE/B3E,KAAK8W,qBAAsB,EAC3B9W,KAAK+W,kBAELhf,SAAS2M,oBAAqB,YAAa4R,GAC3Cve,SAAS2M,oBAAqB,UAAWmS,EAAuB,EAiBjE7W,KAAKmW,iBAAiB1R,iBAAkB,aAbdE,IAEzBA,EAAMqS,iBAENhX,KAAK8W,qBAAsB,EAE3B/e,SAAS0M,iBAAkB,YAAa6R,GACxCve,SAAS0M,iBAAkB,UAAWoS,GAEtCP,EAAyB3R,EAAO,GAMlC,CAEAmR,iBAAAA,GAEK9V,KAAKkW,cACRlW,KAAKkW,YAAYrf,SACjBmJ,KAAKkW,YAAc,KAGrB,CAEA/S,MAAAA,GAEKnD,KAAKiW,aACRjW,KAAKiX,YACLjX,KAAKkX,qBAGP,CAMAD,SAAAA,GAEC,MAAMlR,EAAS/F,KAAKD,OAAOO,YAErB6W,EAAYnX,KAAKD,OAAOqX,qBAAsBxX,OAAOyX,WAAYzX,OAAO0X,aACxEjH,EAAQrQ,KAAKD,OAAOwQ,WACpBgH,EAA2C,YAAxBxR,EAAOyR,aAE1BC,EAAiBzX,KAAKqU,gBAAgB1G,aACtC+J,EAAgBP,EAAUrU,OAASuN,EACnCsH,EAAaJ,EAAmBG,EAAgBD,EAGtDzX,KAAK4X,oBAAsBL,EAAmBG,EAAgBD,EAE9DzX,KAAKqU,gBAAgBjd,MAAMygB,YAAa,gBAAiBF,EAAa,MACtE3X,KAAKqU,gBAAgBjd,MAAM0gB,eAA8C,iBAAtB/R,EAAOgS,WAA2B,KAAIhS,EAAOgS,aAAe,GAG/G/X,KAAKgY,cAAgB,GAErB,MAAMzD,EAAele,MAAMC,KAAM0J,KAAKD,OAAO8F,mBAAmBtP,iBAAkB,iBAElFyJ,KAAKiY,MAAQ1D,EAAanV,KAAK8Y,IAC9B,MAAMpD,EAAO9U,KAAKmY,WAAW,CAC5BD,cACAE,aAAcF,EAAYhV,cAAe,WACzCmV,cAAeH,EAAYhV,cAAe,uBAC1CwH,eAAgBwN,EAAYhV,cAAe,wBAC3CoV,kBAAmBJ,EAAYhV,cAAe,qBAC9C8L,oBAAqBkJ,EAAY3hB,iBAAkB,6BACnDgiB,iBAAkB,KAGnBzD,EAAKoD,YAAY9gB,MAAMygB,YAAa,kBAAoC,IAAlB9R,EAAO4L,OAAkB,OAASwF,EAAUrU,OAAS,MAE3G9C,KAAKgY,cAAc1Y,KAAK,CACvBwV,KAAMA,EACNf,SAAUA,IAAM/T,KAAKwY,aAAc1D,GACnCc,WAAYA,IAAM5V,KAAKyY,eAAgB3D,KAIxC9U,KAAK0Y,8BAA+B5D,GAGhCA,EAAK9F,oBAAoBrW,OAAS,GACrCqH,KAAK2Y,iCAAkC7D,GAGxC,IAAI8D,EAA0Bxc,KAAKE,IAAKwY,EAAK+D,eAAelgB,OAAS,EAAG,GAIxEigB,GAA2B9D,EAAKyD,iBAAiB9E,QAAQ,CAAEqF,EAAOhE,IAC1DgE,EAAQ1c,KAAKE,IAAKwY,EAAK+D,eAAelgB,OAAS,EAAG,IACvDmc,EAAKyD,iBAAiB5f,QAGzBmc,EAAKoD,YAAY3hB,iBAAkB,sBAAuB8E,SAASlF,GAAMA,EAAGU,WAO5E,IAAK,IAAIZ,EAAI,EAAGA,EAAI2iB,EAA0B,EAAG3iB,IAAM,CACtD,MAAM8iB,EAAehhB,SAASU,cAAe,OAC7CsgB,EAAatiB,UAAY,oBACzBsiB,EAAa3hB,MAAM0L,OAAS9C,KAAK4X,oBAAsB,KACvDmB,EAAa3hB,MAAM4hB,gBAAkBzB,EAAmB,SAAW,QACnEzC,EAAKoD,YAAYpf,YAAaigB,GAEpB,IAAN9iB,IACH8iB,EAAa3hB,MAAM6hB,WAAajZ,KAAK4X,oBAAsB,KAE7D,CAiCA,OA5BIL,GAAoBzC,EAAK+D,eAAelgB,OAAS,GACpDmc,EAAK6C,WAAaF,EAClB3C,EAAKoD,YAAY9gB,MAAMygB,YAAa,gBAAiBJ,EAAiB,QAGtE3C,EAAK6C,WAAaA,EAClB7C,EAAKoD,YAAY9gB,MAAM8hB,eAAgB,kBAIxCpE,EAAKqE,cAAgBnZ,KAAK4X,oBAAsBgB,EAGhD9D,EAAKsE,YAActE,EAAK6C,WAAa7C,EAAKqE,cAG1CrE,EAAKoD,YAAY9gB,MAAMygB,YAAa,wBAAyB/C,EAAKqE,cAAgB,MAG9EP,EAA0B,GAC7B9D,EAAKuD,cAAcjhB,MAAMiiB,SAAW,SACpCvE,EAAKuD,cAAcjhB,MAAMqf,IAAMra,KAAKE,KAAOmb,EAAiB3C,EAAK6C,YAAe,EAAG,GAAM,OAGzF7C,EAAKuD,cAAcjhB,MAAMiiB,SAAW,WACpCvE,EAAKoD,YAAY9gB,MAAM4hB,gBAAkBlE,EAAK6C,WAAaF,EAAiB,SAAW,SAGjF3C,CAAI,IAGZ9U,KAAKsZ,mBAaLtZ,KAAKqU,gBAAgBxT,aAAc,iBAAkBkF,EAAOwT,gBAExDxT,EAAOwT,gBAAkBvZ,KAAK4Y,wBAA0B,GAEtD5Y,KAAKkW,aAAclW,KAAKsV,oBAE7BtV,KAAKwZ,mBAGLxZ,KAAK8V,mBAGP,CAMAwD,gBAAAA,GAGCtZ,KAAK4Y,wBAA0B5Y,KAAKgY,cAAcvE,QAAQ,CAAEqF,EAAOW,IAC3DX,EAAQ1c,KAAKE,IAAKmd,EAAQ3E,KAAK+D,eAAelgB,OAAQ,IAC3D,GAEH,IAAI+gB,EAAa,EAIjB1Z,KAAKgY,cAAc3c,SAAS,CAAEoe,EAASxjB,KACtCwjB,EAAQE,MAAQ,CACfD,EACAA,EAAatd,KAAKE,IAAKmd,EAAQ3E,KAAK+D,eAAelgB,OAAQ,GAAMqH,KAAK4Y,yBAGvE,MAAMgB,GAA6BH,EAAQE,MAAM,GAAKF,EAAQE,MAAM,IAAOF,EAAQ3E,KAAK+D,eAAelgB,OAEvG8gB,EAAQ3E,KAAK+D,eAAexd,SAAS,CAAEwe,EAAe5jB,KACrD4jB,EAAcF,MAAQ,CACrBD,EAAazjB,EAAI2jB,EACjBF,GAAezjB,EAAI,GAAM2jB,EACzB,IAGFF,EAAaD,EAAQE,MAAM,EAAE,GAG/B,CAOAjB,6BAAAA,CAA+B5D,EAAMsD,GAEpCA,EAAeA,GAAgBtD,EAAKsD,aAKpC,MAAM0B,EAAiB9Z,KAAKD,OAAOga,UAAUC,KAAM5B,EAAa7hB,iBAAkB,cAAe,GAyBjG,OAtBIujB,EAAenhB,SAClBmc,EAAKiF,UAAY/Z,KAAKD,OAAOga,UAAUC,KAAM5B,EAAa7hB,iBAAkB,6BAC5Eue,EAAK+D,eAAevZ,KAEnB,CACCyU,SAAUA,KACT/T,KAAKD,OAAOga,UAAU1T,QAAS,EAAGyO,EAAKiF,UAAW3B,EAAc,IAMnE0B,EAAeze,SAAS,CAAE0e,EAAW9jB,KACpC6e,EAAK+D,eAAevZ,KAAK,CACxByU,SAAUA,KACT/T,KAAKD,OAAOga,UAAU1T,OAAQpQ,EAAG6e,EAAKiF,UAAW3B,EAAc,GAE/D,KAKGtD,EAAK+D,eAAelgB,MAE5B,CAQAggB,gCAAAA,CAAkC7D,GAE7BA,EAAK9F,oBAAoBrW,OAAS,GAGrCqH,KAAKgY,cAAc1Y,QAASjJ,MAAMC,KAAMwe,EAAK9F,qBAAsB5P,KAAK,CAAE6a,EAAoBhkB,KAC7F,IAAIikB,EAAkBla,KAAKmY,WAAW,CACrCC,aAAc6B,EAAmB/W,cAAe,WAChDwH,eAAgBuP,EAChB3B,kBAAmB2B,EAAmB/W,cAAe,uBAStD,OALAlD,KAAK0Y,8BAA+BwB,EAAiBA,EAAgB9B,cAErEtD,EAAKyD,iBAAiBjZ,KAAM4a,GAGrB,CACNpF,KAAMoF,EACNnG,SAAUA,IAAM/T,KAAKwY,aAAc0B,GACnCtE,WAAYA,IAAM5V,KAAKyY,eAAgByB,GACvC,IAIJ,CAMA/B,UAAAA,CAAYrD,GAMX,OAJAA,EAAK+D,eAAiB,GACtB/D,EAAKqF,OAAS5R,SAAUuM,EAAKsD,aAAatX,aAAc,gBAAkB,IAC1EgU,EAAK3I,OAAS5D,SAAUuM,EAAKsD,aAAatX,aAAc,gBAAkB,IAEnEgU,CAER,CAMA0E,eAAAA,GAECxZ,KAAKmW,iBAAiB5f,iBAAkB,oBAAqB8E,SAASqF,GAASA,EAAM7J,WAErF,MAAM+f,EAAe5W,KAAKqU,gBAAgBuC,aACpCa,EAAiBzX,KAAKqU,gBAAgB1G,aACtCyM,EAAuB3C,EAAiBb,EAE9C5W,KAAK0W,kBAAoB1W,KAAKmW,iBAAiBxI,aAC/C3N,KAAKqa,eAAiBje,KAAKE,IAAK8d,EAAuBpa,KAAK0W,kBAriBlC,GAsiB1B1W,KAAKsa,4BAA8Bta,KAAK0W,kBAAoB1W,KAAKqa,eAEjE,MAAME,EAAwB9C,EAAiBb,EAAe5W,KAAK0W,kBAC7D8D,EAAUpe,KAAKC,IAAKke,EAAwB,EA3iBvB,GA6iB3Bva,KAAKoW,oBAAoBhf,MAAM0L,OAAS9C,KAAKqa,eAAiBG,EAAU,KAGpED,EA/iB8B,EAijBjCva,KAAKgY,cAAc3c,SAASof,IAE3B,MAAM3F,KAAEA,GAAS2F,EAGjB3F,EAAK4F,iBAAmB3iB,SAASU,cAAe,OAChDqc,EAAK4F,iBAAiBjkB,UAAY,kBAClCqe,EAAK4F,iBAAiBtjB,MAAMqf,IAAMgE,EAAad,MAAM,GAAK3Z,KAAK0W,kBAAoB,KACnF5B,EAAK4F,iBAAiBtjB,MAAM0L,QAAW2X,EAAad,MAAM,GAAKc,EAAad,MAAM,IAAO3Z,KAAK0W,kBAAoB8D,EAAU,KAC5H1F,EAAK4F,iBAAiB/jB,UAAUof,OAAQ,eAAgBjB,EAAK+D,eAAelgB,OAAS,GACrFqH,KAAKmW,iBAAiBrd,YAAagc,EAAK4F,kBAGxC5F,EAAK6F,sBAAwB7F,EAAK+D,eAAezZ,KAAK,CAAEqa,EAASxjB,KAEhE,MAAM2kB,EAAiB7iB,SAASU,cAAe,OAQ/C,OAPAmiB,EAAenkB,UAAY,oBAC3BmkB,EAAexjB,MAAMqf,KAAQgD,EAAQE,MAAM,GAAKc,EAAad,MAAM,IAAO3Z,KAAK0W,kBAAoB,KACnGkE,EAAexjB,MAAM0L,QAAW2W,EAAQE,MAAM,GAAKF,EAAQE,MAAM,IAAO3Z,KAAK0W,kBAAoB8D,EAAU,KAC3G1F,EAAK4F,iBAAiB5hB,YAAa8hB,GAEzB,IAAN3kB,IAAU2kB,EAAexjB,MAAM0F,QAAU,QAEtC8d,CAAc,GAEnB,IAOJ5a,KAAKiY,MAAM5c,SAASyZ,GAAQA,EAAK4F,iBAAmB,MAItD,CAMAxD,kBAAAA,GAEC,MAAMO,EAAiBzX,KAAKqU,gBAAgB1G,aACtCyM,EAAuB3C,EAAiBzX,KAAKqU,gBAAgBuC,aAE7DD,EAAY3W,KAAKqU,gBAAgBsC,UACjCC,EAAe5W,KAAKqU,gBAAgBuC,aAAea,EACnD8B,EAAiBnd,KAAKE,IAAKF,KAAKC,IAAKsa,EAAYC,EAAc,GAAK,GACpEiE,EAAoBze,KAAKE,IAAKF,KAAKC,KAAOsa,EAAYc,EAAiB,GAAMzX,KAAKqU,gBAAgBuC,aAAc,GAAK,GAE3H,IAAIkE,EAEJ9a,KAAKgY,cAAc3c,SAAWoe,IAC7B,MAAM3E,KAAEA,GAAS2E,EAEKF,GAAkBE,EAAQE,MAAM,GAA0B,EAArBS,GAChDb,GAAkBE,EAAQE,MAAM,GAA0B,EAArBS,IAG1BtF,EAAKiG,QAC1BjG,EAAKiG,QAAS,EACd/a,KAAKD,OAAO2M,aAAajM,KAAMqU,EAAKsD,eAE5BtD,EAAKiG,SACbjG,EAAKiG,QAAS,EACd/a,KAAKD,OAAO2M,aAAapJ,OAAQwR,EAAKsD,eAInCmB,GAAkBE,EAAQE,MAAM,IAAMJ,GAAkBE,EAAQE,MAAM,IACzE3Z,KAAKgb,gBAAiBvB,GACtBqB,EAAarB,EAAQ3E,MAGb2E,EAAQte,QAChB6E,KAAKib,kBAAmBxB,EACzB,IAKGqB,GACHA,EAAWjC,eAAexd,SAAWoe,IAChCoB,GAAqBpB,EAAQE,MAAM,IAAMkB,GAAqBpB,EAAQE,MAAM,GAC/E3Z,KAAKgb,gBAAiBvB,GAEdA,EAAQte,QAChB6E,KAAKib,kBAAmBxB,EACzB,IAKFzZ,KAAKkb,oBAAqBvE,GAAc3W,KAAKqU,gBAAgBuC,aAAea,GAE7E,CAOAyD,mBAAAA,CAAqB3E,GAEhBvW,KAAKkW,cAERlW,KAAKoW,oBAAoBhf,MAAMD,UAAa,cAAaof,EAAWvW,KAAKsa,iCAEzEta,KAAKmb,cACHlgB,QAAQ6Z,GAAQA,EAAK4F,mBACrBrf,SAAWyZ,IACXA,EAAK4F,iBAAiB/jB,UAAUof,OAAQ,UAA0B,IAAhBjB,EAAK3Z,QAEvD2Z,EAAK+D,eAAexd,SAAS,CAAEoe,EAASxjB,KACvC6e,EAAK6F,sBAAsB1kB,GAAGU,UAAUof,OAAQ,UAA0B,IAAhBjB,EAAK3Z,SAAsC,IAAnBse,EAAQte,OAAiB,GACzG,IAGL6E,KAAK+W,kBAIP,CAMAA,eAAAA,GAEC/W,KAAKkW,YAAYvf,UAAUC,IAAK,WAEhC2H,aAAcyB,KAAKob,wBAE4B,SAA3Cpb,KAAKD,OAAOO,YAAYiZ,gBAA8BvZ,KAAK8W,sBAE9D9W,KAAKob,uBAAyB5c,YAAY,KACrCwB,KAAKkW,aACRlW,KAAKkW,YAAYvf,UAAUE,OAAQ,UACpC,GAhsB2B,KAqsB9B,CAKAwkB,IAAAA,GAECrb,KAAKqU,gBAAgBsC,WAAa3W,KAAK4X,mBAExC,CAKA0D,IAAAA,GAECtb,KAAKqU,gBAAgBsC,WAAa3W,KAAK4X,mBAExC,CAOA2D,aAAAA,CAAenD,GAGd,GAAKpY,KAAK7E,OAGL,CAEJ,MAAMse,EAAUzZ,KAAKwb,wBAAyBpD,GAE1CqB,IAEHzZ,KAAKqU,gBAAgBsC,UAAY8C,EAAQE,MAAM,IAAO3Z,KAAKqU,gBAAgBuC,aAAe5W,KAAKqU,gBAAgB1G,cAEjH,MAVC3N,KAAK6T,mBAAmBvU,MAAM,IAAMU,KAAKub,cAAenD,IAY1D,CAMAqD,mBAAAA,GAECld,aAAcyB,KAAK0b,4BAEnB1b,KAAK0b,2BAA6Bld,YAAY,KAC7Cmd,eAAeC,QAAS,oBAAqB5b,KAAKqU,gBAAgBsC,WAClEgF,eAAeC,QAAS,uBAAwBziB,SAAS0iB,OAAS1iB,SAAS2iB,UAE3E9b,KAAK0b,2BAA6B,IAAI,GACpC,GAEJ,CAKAhG,qBAAAA,GAEC,MAAMqG,EAAiBJ,eAAeK,QAAS,qBACzCC,EAAeN,eAAeK,QAAS,wBAEzCD,GAAkBE,IAAiB9iB,SAAS0iB,OAAS1iB,SAAS2iB,WACjE9b,KAAKqU,gBAAgBsC,UAAYpO,SAAUwT,EAAgB,IAG7D,CAQAvD,YAAAA,CAAc1D,GAEb,IAAKA,EAAK3Z,OAAS,CAElB2Z,EAAK3Z,QAAS,EAEd,MAAMid,aAAEA,EAAYE,kBAAEA,EAAiB5N,eAAEA,EAAcyP,OAAEA,EAAMhO,OAAEA,GAAW2I,EAE5EpK,EAAetT,MAAM0F,QAAU,QAE/Bsb,EAAazhB,UAAUC,IAAK,WAExB0hB,GACHA,EAAkB3hB,UAAUC,IAAK,WAGlCoJ,KAAKD,OAAOmc,qBAAsB9D,EAAc+B,EAAQhO,GACxDnM,KAAKD,OAAOoc,YAAY5Q,kCAAmC6M,EAAcpY,KAAKqU,iBAK9Ehe,MAAMC,KAAMoU,EAAe9S,WAAWrB,iBAAkB,yBAA2B8E,SAAS+gB,IACvFA,IAAY1R,IACf0R,EAAQhlB,MAAM0F,QAAU,OACzB,GAGF,CAED,CAOA2b,cAAAA,CAAgB3D,GAEXA,EAAK3Z,SAER2Z,EAAK3Z,QAAS,EACV2Z,EAAKsD,cAAetD,EAAKsD,aAAazhB,UAAUE,OAAQ,WACxDie,EAAKwD,mBAAoBxD,EAAKwD,kBAAkB3hB,UAAUE,OAAQ,WAIxE,CAEAmkB,eAAAA,CAAiBvB,GAEXA,EAAQte,SACZse,EAAQte,QAAS,EACjBse,EAAQ1F,WAGV,CAEAkH,iBAAAA,CAAmBxB,GAEdA,EAAQte,SACXse,EAAQte,QAAS,EAEbse,EAAQ7D,YACX6D,EAAQ7D,aAIX,CAUAyG,iBAAAA,CAAmB9e,EAAG9B,GAErB,MAAMqZ,EAAO9U,KAAKmb,cAAcrS,MAAMgM,GAC9BA,EAAKqF,SAAW5c,GAAKuX,EAAK3I,SAAW1Q,IAG7C,OAAOqZ,EAAOA,EAAKsD,aAAe,IAEnC,CASAoD,uBAAAA,CAAyB9a,GAExB,OAAOV,KAAKgY,cAAclP,MAAM2Q,GAAWA,EAAQ3E,KAAKsD,eAAiB1X,GAE1E,CAQAya,WAAAA,GAEC,OAAOnb,KAAKiY,MAAMqE,SAASxH,GAAQ,CAACA,KAAUA,EAAKyD,kBAAoB,KAExE,CAEAzE,QAAAA,GAEC9T,KAAKkX,qBACLlX,KAAKyb,qBAEN,CAEA,mBAAIpH,GAEH,OAAOrU,KAAKD,OAAOwc,oBAEpB,EC94Bc,MAAMC,EAEpB1c,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAMA,cAAMgU,GAEL,MAAMhO,EAAS/F,KAAKD,OAAOO,YACrBmc,EAASvmB,EAAU8J,KAAKD,OAAO8F,mBAAoBP,GAGnDoX,EAAoB3W,EAAOG,aAAe,aAAa9L,KAAM2L,EAAOK,iBAEpE+Q,EAAYnX,KAAKD,OAAOqX,qBAAsBxX,OAAOyX,WAAYzX,OAAO0X,aAGxEqF,EAAYvgB,KAAKwgB,MAAOzF,EAAUtU,OAAU,EAAIkD,EAAO8W,SAC5DlF,EAAavb,KAAKwgB,MAAOzF,EAAUrU,QAAW,EAAIiD,EAAO8W,SAGpDzP,EAAa+J,EAAUtU,MAC5B6K,EAAcyJ,EAAUrU,aAEnB,IAAIga,QAAS/hB,uBAGnBxC,EAAkB,cAAeokB,EAAW,MAAOhF,EAAY,qBAG/Dpf,EAAkB,iFAAkF6U,EAAY,kBAAmBM,EAAa,OAEhJ3V,SAASC,gBAAgBrB,UAAUC,IAAK,eAAgB,aACxDmB,SAASglB,KAAK3lB,MAAMyL,MAAQ8Z,EAAY,KACxC5kB,SAASglB,KAAK3lB,MAAM0L,OAAS6U,EAAa,KAE1C,MAAMtD,EAAkBrU,KAAKD,OAAOwc,qBACpC,IAAInI,EACJ,GAAIC,EAAkB,CACrB,MAAMC,EAAiB1U,OAAOhD,iBAAkByX,GAC5CC,GAAkBA,EAAenT,aACpCiT,EAAyBE,EAAenT,WAE1C,OAGM,IAAI2b,QAAS/hB,uBACnBiF,KAAKD,OAAOid,oBAAqB5P,EAAYM,SAGvC,IAAIoP,QAAS/hB,uBAEnB,MAAMkiB,EAAqBR,EAAOrd,KAAKsB,GAASA,EAAMkW,eAEhDqB,EAAQ,GACRzD,EAAgBiI,EAAO,GAAG7kB,WAChC,IAAIsO,EAAc,EAGlBuW,EAAOphB,SAAS,SAAUqF,EAAO6R,GAIhC,IAA4C,IAAxC7R,EAAM/J,UAAU8U,SAAU,SAAsB,CAEnD,IAAIyR,GAASP,EAAYvP,GAAe,EACpCqJ,GAAQkB,EAAajK,GAAgB,EAEzC,MAAMyP,EAAgBF,EAAoB1K,GAC1C,IAAI6K,EAAgBhhB,KAAKE,IAAKF,KAAKihB,KAAMF,EAAgBxF,GAAc,GAGvEyF,EAAgBhhB,KAAKC,IAAK+gB,EAAerX,EAAOuX,sBAG1B,IAAlBF,GAAuBrX,EAAO4L,QAAUjR,EAAM/J,UAAU8U,SAAU,aACrEgL,EAAMra,KAAKE,KAAOqb,EAAawF,GAAkB,EAAG,IAKrD,MAAMrI,EAAO/c,SAASU,cAAe,OA0BrC,GAzBAwf,EAAM3Y,KAAMwV,GAEZA,EAAKre,UAAY,WACjBqe,EAAK1d,MAAM0L,QAAa6U,EAAa5R,EAAOwX,qBAAwBH,EAAkB,KAIlFhJ,IACHU,EAAK1d,MAAM+J,WAAaiT,GAGzBU,EAAKhc,YAAa4H,GAGlBA,EAAMtJ,MAAM8lB,KAAOA,EAAO,KAC1Bxc,EAAMtJ,MAAMqf,IAAMA,EAAM,KACxB/V,EAAMtJ,MAAMyL,MAAQuK,EAAa,KAEjCpN,KAAKD,OAAO2M,aAAavJ,OAAQzC,GAE7BA,EAAMU,wBACT0T,EAAKI,aAAcxU,EAAMU,uBAAwBV,GAI9CqF,EAAOyX,UAAY,CAGtB,MAAMC,EAAQzd,KAAKD,OAAO2d,cAAehd,GACzC,GAAI+c,EAAQ,CAEX,MAAME,EAAe,EACfC,EAA0C,iBAArB7X,EAAOyX,UAAyBzX,EAAOyX,UAAY,SACxEK,EAAe9lB,SAASU,cAAe,OAC7ColB,EAAalnB,UAAUC,IAAK,iBAC5BinB,EAAalnB,UAAUC,IAAK,qBAC5BinB,EAAahd,aAAc,cAAe+c,GAC1CC,EAAavX,UAAYmX,EAEL,kBAAhBG,EACH3F,EAAM3Y,KAAMue,IAGZA,EAAazmB,MAAM8lB,KAAOS,EAAe,KACzCE,EAAazmB,MAAM0mB,OAASH,EAAe,KAC3CE,EAAazmB,MAAMyL,MAAU8Z,EAAyB,EAAbgB,EAAmB,KAC5D7I,EAAKhc,YAAa+kB,GAGpB,CAED,CAGA,GAAInB,EAAoB,CACvB,MAAMqB,EAAgBhmB,SAASU,cAAe,OAC9CslB,EAAcpnB,UAAUC,IAAK,gBAC7BmnB,EAAcpnB,UAAUC,IAAK,oBAC7BmnB,EAAczX,UAAYJ,IAC1B4O,EAAKhc,YAAailB,EACnB,CAGA,GAAIhY,EAAOiY,qBAAuB,CAKjC,MAAMlE,EAAiB9Z,KAAKD,OAAOga,UAAUC,KAAMlF,EAAKve,iBAAkB,cAAe,GAEzF,IAAI0nB,EAEJnE,EAAeze,SAAS,SAAU0e,EAAWxH,GAGxC0L,GACHA,EAAqB5iB,SAAS,SAAU6iB,GACvCA,EAASvnB,UAAUE,OAAQ,mBAC5B,IAIDkjB,EAAU1e,SAAS,SAAU6iB,GAC5BA,EAASvnB,UAAUC,IAAK,UAAW,mBACnC,GAAEoJ,MAGH,MAAMme,EAAarJ,EAAKsJ,WAAW,GAGnC,GAAI1B,EAAoB,CACvB,MACM2B,EAAiB9L,EAAQ,EADT4L,EAAWjb,cAAe,qBAElCoD,WAAa,IAAM+X,CAClC,CAEApG,EAAM3Y,KAAM6e,GAEZF,EAAuBlE,CAEvB,GAAE/Z,MAGH8Z,EAAeze,SAAS,SAAU0e,GACjCA,EAAU1e,SAAS,SAAU6iB,GAC5BA,EAASvnB,UAAUE,OAAQ,UAAW,mBACvC,GACD,GAED,MAGCX,EAAU4e,EAAM,4BAA6BzZ,SAAS,SAAU6iB,GAC/DA,EAASvnB,UAAUC,IAAK,UACzB,GAGF,CAEA,GAAEoJ,YAEG,IAAI8c,QAAS/hB,uBAEnBkd,EAAM5c,SAASyZ,GAAQN,EAAc1b,YAAagc,KAGlD9U,KAAKD,OAAO2M,aAAavJ,OAAQnD,KAAKD,OAAO8D,oBAG7C7D,KAAKD,OAAO9C,cAAc,CAAEvE,KAAM,cAElC2b,EAAgB1d,UAAUE,OAAQ,sBAEnC,CAKAof,QAAAA,GAEC,MAAwC,UAAjCjW,KAAKD,OAAOO,YAAYge,IAEhC,ECrOc,MAAMC,EAEpBze,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAKA+F,SAAAA,CAAWC,EAAQC,IAEO,IAArBD,EAAOgU,UACV/Z,KAAKwe,WAE2B,IAAxBxY,EAAU+T,WAClB/Z,KAAKye,QAGP,CAMAD,OAAAA,GAECtoB,EAAU8J,KAAKD,OAAO8D,mBAAoB,aAAcxI,SAASnE,IAChEA,EAAQP,UAAUC,IAAK,WACvBM,EAAQP,UAAUE,OAAQ,mBAAoB,GAGhD,CAMA4nB,MAAAA,GAECvoB,EAAU8J,KAAKD,OAAO8D,mBAAoB,aAAcxI,SAASnE,IAChEA,EAAQP,UAAUE,OAAQ,WAC1BK,EAAQP,UAAUE,OAAQ,mBAAoB,GAGhD,CAQA6nB,eAAAA,GAEC,IAAI/S,EAAe3L,KAAKD,OAAOyG,kBAC/B,GAAImF,GAAgB3L,KAAKD,OAAOO,YAAYyZ,UAAY,CACvD,IAAIA,EAAYpO,EAAapV,iBAAkB,4BAC3CooB,EAAkBhT,EAAapV,iBAAkB,0CAErD,MAAO,CACN8kB,KAAMtB,EAAUphB,OAASgmB,EAAgBhmB,OAAS,EAClD2iB,OAAQqD,EAAgBhmB,OAE1B,CAEC,MAAO,CAAE0iB,MAAM,EAAOC,MAAM,EAG9B,CAqBAtB,IAAAA,CAAMD,EAAW6E,GAAU,GAE1B7E,EAAY1jB,MAAMC,KAAMyjB,GAExB,IAAI8E,EAAU,GACbC,EAAY,GACZC,EAAS,GAGVhF,EAAU1e,SAAS6iB,IAClB,GAAIA,EAAS1d,aAAc,uBAA0B,CACpD,IAAI+R,EAAQhK,SAAU2V,EAASpd,aAAc,uBAAyB,IAEjE+d,EAAQtM,KACZsM,EAAQtM,GAAS,IAGlBsM,EAAQtM,GAAOjT,KAAM4e,EACtB,MAECY,EAAUxf,KAAM,CAAE4e,GACnB,IAKDW,EAAUA,EAAQ9f,OAAQ+f,GAI1B,IAAIvM,EAAQ,EAaZ,OATAsM,EAAQxjB,SAAS2jB,IAChBA,EAAM3jB,SAAS6iB,IACda,EAAOzf,KAAM4e,GACbA,EAASrd,aAAc,sBAAuB0R,EAAO,IAGtDA,GAAQ,KAGU,IAAZqM,EAAmBC,EAAUE,CAErC,CAMAE,OAAAA,GAECjf,KAAKD,OAAO2G,sBAAsBrL,SAAS8Z,IAE1C,IAAIlI,EAAiB/W,EAAUif,EAAiB,WAChDlI,EAAe5R,SAAS,CAAEga,EAAe7Z,KAExCwE,KAAKga,KAAM3E,EAAc9e,iBAAkB,aAAe,GAExDyJ,MAE2B,IAA1BiN,EAAetU,QAAeqH,KAAKga,KAAM7E,EAAgB5e,iBAAkB,aAAe,GAIhG,CAYA8P,MAAAA,CAAQkM,EAAOwH,EAAWrZ,EAAQV,KAAKD,OAAOyG,mBAE7C,IAAI0Y,EAAmB,CACtBC,MAAO,GACPC,OAAQ,IAGT,GAAI1e,GAASV,KAAKD,OAAOO,YAAYyZ,YAEpCA,EAAYA,GAAa/Z,KAAKga,KAAMtZ,EAAMnK,iBAAkB,eAE9CoC,OAAS,CAEtB,IAAI0mB,EAAW,EAEf,GAAqB,iBAAV9M,EAAqB,CAC/B,IAAI+M,EAAkBtf,KAAKga,KAAMtZ,EAAMnK,iBAAkB,sBAAwBiD,MAC7E8lB,IACH/M,EAAQhK,SAAU+W,EAAgBxe,aAAc,wBAA2B,EAAG,IAEhF,CAEAzK,MAAMC,KAAMyjB,GAAY1e,SAAS,CAAElF,EAAIF,KAStC,GAPIE,EAAGqK,aAAc,yBACpBvK,EAAIsS,SAAUpS,EAAG2K,aAAc,uBAAyB,KAGzDue,EAAWjjB,KAAKE,IAAK+iB,EAAUppB,GAG3BA,GAAKsc,EAAQ,CAChB,IAAIgN,EAAappB,EAAGQ,UAAU8U,SAAU,WACxCtV,EAAGQ,UAAUC,IAAK,WAClBT,EAAGQ,UAAUE,OAAQ,oBAEjBZ,IAAMsc,IAETvS,KAAKD,OAAOyf,eAAgBxf,KAAKD,OAAO0f,cAAetpB,IAEvDA,EAAGQ,UAAUC,IAAK,oBAClBoJ,KAAKD,OAAO2M,aAAa1I,qBAAsB7N,IAG3CopB,IACJL,EAAiBC,MAAM7f,KAAMnJ,GAC7B6J,KAAKD,OAAO9C,cAAc,CACzB3F,OAAQnB,EACRuC,KAAM,UACNgnB,SAAS,IAGZ,KAEK,CACJ,IAAIH,EAAappB,EAAGQ,UAAU8U,SAAU,WACxCtV,EAAGQ,UAAUE,OAAQ,WACrBV,EAAGQ,UAAUE,OAAQ,oBAEjB0oB,IACHvf,KAAKD,OAAO2M,aAAavH,oBAAqBhP,GAC9C+oB,EAAiBE,OAAO9f,KAAMnJ,GAC9B6J,KAAKD,OAAO9C,cAAc,CACzB3F,OAAQnB,EACRuC,KAAM,SACNgnB,SAAS,IAGZ,KAODnN,EAAyB,iBAAVA,EAAqBA,GAAS,EAC7CA,EAAQnW,KAAKE,IAAKF,KAAKC,IAAKkW,EAAO8M,IAAa,GAChD3e,EAAMG,aAAc,gBAAiB0R,EAEtC,CAwBD,OApBI2M,EAAiBE,OAAOzmB,QAC3BqH,KAAKD,OAAO9C,cAAc,CACzBvE,KAAM,iBACNkS,KAAM,CACLsT,SAAUgB,EAAiBE,OAAO,GAClCrF,UAAWmF,EAAiBE,UAK3BF,EAAiBC,MAAMxmB,QAC1BqH,KAAKD,OAAO9C,cAAc,CACzBvE,KAAM,gBACNkS,KAAM,CACLsT,SAAUgB,EAAiBC,MAAM,GACjCpF,UAAWmF,EAAiBC,SAKxBD,CAER,CAUAvU,IAAAA,CAAMjK,EAAQV,KAAKD,OAAOyG,mBAEzB,OAAOxG,KAAKga,KAAMtZ,EAAMnK,iBAAkB,aAE3C,CAaAopB,IAAAA,CAAMpN,EAAOqN,EAAS,GAErB,IAAIjU,EAAe3L,KAAKD,OAAOyG,kBAC/B,GAAImF,GAAgB3L,KAAKD,OAAOO,YAAYyZ,UAAY,CAEvD,IAAIA,EAAY/Z,KAAKga,KAAMrO,EAAapV,iBAAkB,6BAC1D,GAAIwjB,EAAUphB,OAAS,CAGtB,GAAqB,iBAAV4Z,EAAqB,CAC/B,IAAIsN,EAAsB7f,KAAKga,KAAMrO,EAAapV,iBAAkB,qCAAuCiD,MAG1G+Y,EADGsN,EACKtX,SAAUsX,EAAoB/e,aAAc,wBAA2B,EAAG,KAGzE,CAEX,CAGAyR,GAASqN,EAET,IAAIV,EAAmBlf,KAAKqG,OAAQkM,EAAOwH,GAS3C,OAPA/Z,KAAKD,OAAOyE,SAAS6B,SACrBrG,KAAKD,OAAOwW,SAASlQ,SAEjBrG,KAAKD,OAAOO,YAAYwf,eAC3B9f,KAAKD,OAAO5G,SAAS4mB,cAGXb,EAAiBC,MAAMxmB,SAAUumB,EAAiBE,OAAOzmB,OAErE,CAED,CAEA,OAAO,CAER,CAQA2iB,IAAAA,GAEC,OAAOtb,KAAK2f,KAAM,KAAM,EAEzB,CAQAtE,IAAAA,GAEC,OAAOrb,KAAK2f,KAAM,MAAO,EAE1B,EC7Wc,MAAMK,EAEpBlgB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK7E,QAAS,EAEd6E,KAAKigB,eAAiBjgB,KAAKigB,eAAe/f,KAAMF,KAEjD,CAMA+T,QAAAA,GAGC,GAAI/T,KAAKD,OAAOO,YAAY4f,WAAalgB,KAAKD,OAAOK,iBAAmBJ,KAAKiW,WAAa,CAEzFjW,KAAK7E,QAAS,EAEd6E,KAAKD,OAAO8F,mBAAmBlP,UAAUC,IAAK,YAG9CoJ,KAAKD,OAAOogB,kBAIZngB,KAAKD,OAAO8D,mBAAmB/K,YAAakH,KAAKD,OAAOqgB,yBAGxDlqB,EAAU8J,KAAKD,OAAO8F,mBAAoBP,GAAkBjK,SAASqF,IAC/DA,EAAM/J,UAAU8U,SAAU,UAC9B/K,EAAM+D,iBAAkB,QAASzE,KAAKigB,gBAAgB,EACvD,IAID,MAAMpD,EAAS,GACT1F,EAAYnX,KAAKD,OAAOqX,uBAC9BpX,KAAKqgB,mBAAqBlJ,EAAUtU,MAAQga,EAC5C7c,KAAKsgB,oBAAsBnJ,EAAUrU,OAAS+Z,EAG1C7c,KAAKD,OAAOO,YAAYwL,MAC3B9L,KAAKqgB,oBAAsBrgB,KAAKqgB,oBAGjCrgB,KAAKD,OAAOwgB,yBAEZvgB,KAAKmD,SACLnD,KAAKqG,SAELrG,KAAKD,OAAOoD,SAEZ,MAAM6D,EAAUhH,KAAKD,OAAOkH,aAG5BjH,KAAKD,OAAO9C,cAAc,CACzBvE,KAAM,gBACNkS,KAAM,CACLuP,OAAUnT,EAAQzJ,EAClB4O,OAAUnF,EAAQvL,EAClBkQ,aAAgB3L,KAAKD,OAAOyG,oBAI/B,CAED,CAMArD,MAAAA,GAGCnD,KAAKD,OAAO2G,sBAAsBrL,SAAS,CAAEmlB,EAAQjjB,KACpDijB,EAAO3f,aAAc,eAAgBtD,GACrCtG,EAAkBupB,EAAQ,eAAmBjjB,EAAIyC,KAAKqgB,mBAAuB,aAEzEG,EAAO7pB,UAAU8U,SAAU,UAE9BvV,EAAUsqB,EAAQ,WAAYnlB,SAAS,CAAEolB,EAAQhlB,KAChDglB,EAAO5f,aAAc,eAAgBtD,GACrCkjB,EAAO5f,aAAc,eAAgBpF,GAErCxE,EAAkBwpB,EAAQ,kBAAsBhlB,EAAIuE,KAAKsgB,oBAAwB,SAAU,GAG7F,IAIDjqB,MAAMC,KAAM0J,KAAKD,OAAOqgB,wBAAwBpU,YAAa3Q,SAAS,CAAEqlB,EAAanjB,KACpFtG,EAAkBypB,EAAa,eAAmBnjB,EAAIyC,KAAKqgB,mBAAuB,aAElFnqB,EAAUwqB,EAAa,qBAAsBrlB,SAAS,CAAEslB,EAAallB,KACpExE,EAAkB0pB,EAAa,kBAAsBllB,EAAIuE,KAAKsgB,oBAAwB,SAAU,GAC9F,GAGL,CAMAja,MAAAA,GAEC,MAAMua,EAAOxkB,KAAKC,IAAKuD,OAAOyX,WAAYzX,OAAO0X,aAC3CjH,EAAQjU,KAAKE,IAAKskB,EAAO,EAAG,KAAQA,EACpC5Z,EAAUhH,KAAKD,OAAOkH,aAE5BjH,KAAKD,OAAO8gB,gBAAiB,CAC5BX,SAAU,CACT,SAAU7P,EAAO,IACjB,eAAkBrJ,EAAQzJ,EAAIyC,KAAKqgB,mBAAsB,MACzD,eAAkBrZ,EAAQvL,EAAIuE,KAAKsgB,oBAAuB,OACzDle,KAAM,MAGV,CAMAwT,UAAAA,GAGC,GAAI5V,KAAKD,OAAOO,YAAY4f,SAAW,CAEtClgB,KAAK7E,QAAS,EAEd6E,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,YAKjDmJ,KAAKD,OAAO8F,mBAAmBlP,UAAUC,IAAK,yBAE9C4H,YAAY,KACXwB,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,wBAAyB,GACxE,GAGHmJ,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAKD,OAAOqgB,yBAGxDlqB,EAAU8J,KAAKD,OAAO8F,mBAAoBP,GAAkBjK,SAASqF,IACpEzJ,EAAkByJ,EAAO,IAEzBA,EAAMgE,oBAAqB,QAAS1E,KAAKigB,gBAAgB,EAAM,IAIhE/pB,EAAU8J,KAAKD,OAAOqgB,wBAAyB,qBAAsB/kB,SAAS8F,IAC7ElK,EAAkBkK,EAAY,GAAI,IAGnCnB,KAAKD,OAAO8gB,gBAAiB,CAAEX,SAAU,KAEzC,MAAMlZ,EAAUhH,KAAKD,OAAOkH,aAE5BjH,KAAKD,OAAOW,MAAOsG,EAAQzJ,EAAGyJ,EAAQvL,GACtCuE,KAAKD,OAAOoD,SACZnD,KAAKD,OAAO+gB,eAGZ9gB,KAAKD,OAAO9C,cAAc,CACzBvE,KAAM,iBACNkS,KAAM,CACLuP,OAAUnT,EAAQzJ,EAClB4O,OAAUnF,EAAQvL,EAClBkQ,aAAgB3L,KAAKD,OAAOyG,oBAI/B,CACD,CASAuP,MAAAA,CAAQC,GAEiB,kBAAbA,EACVA,EAAWhW,KAAK+T,WAAa/T,KAAK4V,aAGlC5V,KAAKiW,WAAajW,KAAK4V,aAAe5V,KAAK+T,UAG7C,CAQAkC,QAAAA,GAEC,OAAOjW,KAAK7E,MAEb,CAOA8kB,cAAAA,CAAgBtb,GAEf,GAAI3E,KAAKiW,WAAa,CACrBtR,EAAMqS,iBAEN,IAAI9f,EAAUyN,EAAMrN,OAEpB,KAAOJ,IAAYA,EAAQyb,SAAS5b,MAAO,cAC1CG,EAAUA,EAAQU,WAGnB,GAAIV,IAAYA,EAAQP,UAAU8U,SAAU,cAE3CzL,KAAK4V,aAED1e,EAAQyb,SAAS5b,MAAO,cAAgB,CAC3C,IAAIwG,EAAIgL,SAAUrR,EAAQ4J,aAAc,gBAAkB,IACzDrF,EAAI8M,SAAUrR,EAAQ4J,aAAc,gBAAkB,IAEvDd,KAAKD,OAAOW,MAAOnD,EAAG9B,EACvB,CAGF,CAED,ECvPc,MAAMslB,EAEpBjhB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAIdC,KAAKghB,UAAY,GAGjBhhB,KAAKihB,SAAW,GAEhBjhB,KAAKkhB,kBAAoBlhB,KAAKkhB,kBAAkBhhB,KAAMF,KAEvD,CAKA8F,SAAAA,CAAWC,EAAQC,GAEY,WAA1BD,EAAOob,gBACVnhB,KAAKghB,UAAU,mDAAqD,aACpEhhB,KAAKghB,UAAU,yCAAqD,mBAGpEhhB,KAAKghB,UAAU,eAAmB,aAClChhB,KAAKghB,UAAU,qBAAmC,iBAClDhhB,KAAKghB,UAAU,iBAAmB,gBAClChhB,KAAKghB,UAAU,iBAAmB,iBAClChhB,KAAKghB,UAAU,iBAAmB,cAClChhB,KAAKghB,UAAU,iBAAmB,iBAGnChhB,KAAKghB,UAAU,wCAAiD,6BAChEhhB,KAAKghB,UAAU,0CAAiD,2BAChEhhB,KAAKghB,UAAU,WAAmC,QAClDhhB,KAAKghB,UAAa,EAAgC,aAClDhhB,KAAKghB,UAAa,EAAgC,gBAClDhhB,KAAKghB,UAAU,UAAmC,gBAEnD,CAKA9gB,IAAAA,GAECnI,SAAS0M,iBAAkB,UAAWzE,KAAKkhB,mBAAmB,EAE/D,CAKAE,MAAAA,GAECrpB,SAAS2M,oBAAqB,UAAW1E,KAAKkhB,mBAAmB,EAElE,CAMAG,aAAAA,CAAeC,EAAS7L,GAEA,iBAAZ6L,GAAwBA,EAAQpY,QAC1ClJ,KAAKihB,SAASK,EAAQpY,SAAW,CAChCuM,SAAUA,EACVtC,IAAKmO,EAAQnO,IACboO,YAAaD,EAAQC,aAItBvhB,KAAKihB,SAASK,GAAW,CACxB7L,SAAUA,EACVtC,IAAK,KACLoO,YAAa,KAIhB,CAKAC,gBAAAA,CAAkBtY,UAEVlJ,KAAKihB,SAAS/X,EAEtB,CAOAuY,UAAAA,CAAYvY,GAEXlJ,KAAKkhB,kBAAmB,CAAEhY,WAE3B,CAQAwY,wBAAAA,CAA0BvO,EAAKzc,GAE9BsJ,KAAKghB,UAAU7N,GAAOzc,CAEvB,CAEAirB,YAAAA,GAEC,OAAO3hB,KAAKghB,SAEb,CAEAY,WAAAA,GAEC,OAAO5hB,KAAKihB,QAEb,CAOAC,iBAAAA,CAAmBvc,GAElB,IAAIoB,EAAS/F,KAAKD,OAAOO,YAIzB,GAAwC,mBAA7ByF,EAAO8b,oBAAwE,IAApC9b,EAAO8b,kBAAkBld,GAC9E,OAAO,EAKR,GAAiC,YAA7BoB,EAAO8b,oBAAoC7hB,KAAKD,OAAO+hB,YAC1D,OAAO,EAIR,IAAI5Y,EAAUvE,EAAMuE,QAGhB6Y,GAAsB/hB,KAAKD,OAAOiiB,gBAEtChiB,KAAKD,OAAOkiB,YAAatd,GAGzB,IAAIud,EAAoBnqB,SAASoqB,gBAA8D,IAA7CpqB,SAASoqB,cAAcC,kBACrEC,EAAuBtqB,SAASoqB,eAAiBpqB,SAASoqB,cAAcvhB,SAAW,kBAAkBxG,KAAMrC,SAASoqB,cAAcvhB,SAClI0hB,EAAuBvqB,SAASoqB,eAAiBpqB,SAASoqB,cAAc1rB,WAAa,iBAAiB2D,KAAMrC,SAASoqB,cAAc1rB,WAMnI8rB,KAH0F,IAApE,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAAKxe,QAASY,EAAMuE,UAG/BvE,EAAM6d,UAAY7d,EAAM8d,UAChE9d,EAAM6d,UAAY7d,EAAM8d,QAAU9d,EAAM+d,SAAW/d,EAAMge,SAIjE,GAAIT,GAAqBG,GAAwBC,GAAwBC,EAAiB,OAG1F,IACIpP,EADAyP,EAAiB,CAAC,GAAG,GAAG,IAAI,IAAI,KAIpC,GAA+B,iBAApB7c,EAAO8c,SACjB,IAAK1P,KAAOpN,EAAO8c,SACW,gBAAzB9c,EAAO8c,SAAS1P,IACnByP,EAAetjB,KAAMiJ,SAAU4K,EAAK,KAKvC,GAAInT,KAAKD,OAAO+iB,aAAqD,IAAvCF,EAAe7e,QAASmF,GACrD,OAAO,EAKR,IAAI6Z,EAA0C,WAA1Bhd,EAAOob,iBAAgCnhB,KAAKD,OAAOijB,wBAA0BhjB,KAAKD,OAAOkjB,oBAEzGC,GAAY,EAGhB,GAA+B,iBAApBnd,EAAO8c,SAEjB,IAAK1P,KAAOpN,EAAO8c,SAGlB,GAAIta,SAAU4K,EAAK,MAASjK,EAAU,CAErC,IAAIxS,EAAQqP,EAAO8c,SAAU1P,GAGR,mBAAVzc,EACVA,EAAM4B,MAAO,KAAM,CAAEqM,IAGI,iBAAVjO,GAAsD,mBAAzBsJ,KAAKD,OAAQrJ,IACzDsJ,KAAKD,OAAQrJ,GAAQgB,OAGtBwrB,GAAY,CAEb,CAOF,IAAkB,IAAdA,EAEH,IAAK/P,KAAOnT,KAAKihB,SAGhB,GAAI1Y,SAAU4K,EAAK,MAASjK,EAAU,CAErC,IAAIia,EAASnjB,KAAKihB,SAAU9N,GAAMsC,SAGZ,mBAAX0N,EACVA,EAAO7qB,MAAO,KAAM,CAAEqM,IAGI,iBAAXwe,GAAwD,mBAA1BnjB,KAAKD,OAAQojB,IAC1DnjB,KAAKD,OAAQojB,GAASzrB,OAGvBwrB,GAAY,CACb,EAKgB,IAAdA,IAGHA,GAAY,EAGI,KAAZha,GAA8B,KAAZA,EACrBlJ,KAAKD,OAAOsb,KAAK,CAAC+H,cAAeze,EAAM8d,SAGnB,KAAZvZ,GAA8B,KAAZA,EAC1BlJ,KAAKD,OAAOub,KAAK,CAAC8H,cAAeze,EAAM8d,SAGnB,KAAZvZ,GAA8B,KAAZA,EACtBvE,EAAM6d,SACTxiB,KAAKD,OAAOW,MAAO,IAEVV,KAAKD,OAAOmgB,SAASjK,YAAc8M,EACxChd,EAAO+F,IACV9L,KAAKD,OAAOub,KAAK,CAAC8H,cAAeze,EAAM8d,SAGvCziB,KAAKD,OAAOsb,KAAK,CAAC+H,cAAeze,EAAM8d,SAIxCziB,KAAKD,OAAOmd,KAAK,CAACkG,cAAeze,EAAM8d,SAIpB,KAAZvZ,GAA8B,KAAZA,EACtBvE,EAAM6d,SACTxiB,KAAKD,OAAOW,MAAOV,KAAKD,OAAO2G,sBAAsB/N,OAAS,IAErDqH,KAAKD,OAAOmgB,SAASjK,YAAc8M,EACxChd,EAAO+F,IACV9L,KAAKD,OAAOsb,KAAK,CAAC+H,cAAeze,EAAM8d,SAGvCziB,KAAKD,OAAOub,KAAK,CAAC8H,cAAeze,EAAM8d,SAIxCziB,KAAKD,OAAOsjB,MAAM,CAACD,cAAeze,EAAM8d,SAIrB,KAAZvZ,GAA8B,KAAZA,EACtBvE,EAAM6d,SACTxiB,KAAKD,OAAOW,WAAO4iB,EAAW,IAErBtjB,KAAKD,OAAOmgB,SAASjK,YAAc8M,EAC5C/iB,KAAKD,OAAOsb,KAAK,CAAC+H,cAAeze,EAAM8d,SAGvCziB,KAAKD,OAAOwjB,GAAG,CAACH,cAAeze,EAAM8d,SAIlB,KAAZvZ,GAA8B,KAAZA,EACtBvE,EAAM6d,SACTxiB,KAAKD,OAAOW,WAAO4iB,EAAWE,OAAOC,YAE5BzjB,KAAKD,OAAOmgB,SAASjK,YAAc8M,EAC5C/iB,KAAKD,OAAOub,KAAK,CAAC8H,cAAeze,EAAM8d,SAGvCziB,KAAKD,OAAO2jB,KAAK,CAACN,cAAeze,EAAM8d,SAIpB,KAAZvZ,EACRlJ,KAAKD,OAAOW,MAAO,GAGC,KAAZwI,EACRlJ,KAAKD,OAAOW,MAAOV,KAAKD,OAAO2G,sBAAsB/N,OAAS,GAG1C,KAAZuQ,GACJlJ,KAAKD,OAAOmgB,SAASjK,YACxBjW,KAAKD,OAAOmgB,SAAStK,aAElBjR,EAAM6d,SACTxiB,KAAKD,OAAOsb,KAAK,CAAC+H,cAAeze,EAAM8d,SAGvCziB,KAAKD,OAAOub,KAAK,CAAC8H,cAAeze,EAAM8d,UAIhC,CAAC,GAAI,GAAI,GAAI,GAAI,KAAKkB,SAAUza,IAA2B,MAAZA,IAAoBvE,EAAM6d,SACjFxiB,KAAKD,OAAO6jB,cAGQ,KAAZ1a,EACRrR,EAAiBkO,EAAO8d,SAAW7jB,KAAKD,OAAOwc,qBAAuBxkB,SAASC,iBAG3D,KAAZkR,EACJnD,EAAO+d,oBACV9jB,KAAKD,OAAOgkB,gBAAiBhC,GAIV,KAAZ7Y,EACJnD,EAAOie,aACVhkB,KAAKD,OAAOkkB,oBAIS,KAAZ/a,GAA8B,MAAZA,IAAqBvE,EAAM6d,SAInC,MAAZtZ,EACRlJ,KAAKD,OAAOmkB,aAGZhB,GAAY,EAPZljB,KAAKD,OAAOmkB,cAcVhB,EACHve,EAAMqS,gBAAkBrS,EAAMqS,iBAGV,KAAZ9N,GAA8B,KAAZA,KACS,IAA/BlJ,KAAKD,OAAOokB,gBACfnkB,KAAKD,OAAOmgB,SAASnK,SAGtBpR,EAAMqS,gBAAkBrS,EAAMqS,kBAK/BhX,KAAKD,OAAO+gB,cAEb,EC1Yc,MAAMsD,EAIpBC,4BAA8B,IAE9BvkB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAGdC,KAAKskB,gBAAkB,EAEvBtkB,KAAKukB,sBAAwB,EAE7BvkB,KAAKwkB,mBAAqBxkB,KAAKwkB,mBAAmBtkB,KAAMF,KAEzD,CAEAE,IAAAA,GAECN,OAAO6E,iBAAkB,aAAczE,KAAKwkB,oBAAoB,EAEjE,CAEApD,MAAAA,GAECxhB,OAAO8E,oBAAqB,aAAc1E,KAAKwkB,oBAAoB,EAEpE,CAUAhc,kBAAAA,CAAoBic,EAAK7kB,OAAOzG,SAASsrB,KAAM9jB,EAAQ,IAGtD,IAAI+jB,EAAOD,EAAKprB,QAAS,QAAS,IAC9BsrB,EAAOD,EAAKprB,MAAO,KAIvB,GAAK,WAAWc,KAAMuqB,EAAK,MAAQD,EAAK/rB,OAwBnC,CACJ,MAAMoN,EAAS/F,KAAKD,OAAOO,YAC3B,IAKC1E,EALGgpB,EAAgB7e,EAAO8e,mBAAqBlkB,EAAQ8H,cAAgB,EAAI,EAGxElL,EAAMgL,SAAUoc,EAAK,GAAI,IAAOC,GAAmB,EACtDnpB,EAAM8M,SAAUoc,EAAK,GAAI,IAAOC,GAAmB,EAUpD,OAPI7e,EAAO+Z,gBACVlkB,EAAI2M,SAAUoc,EAAK,GAAI,IACnBpd,MAAO3L,KACVA,OAAI0nB,IAIC,CAAE/lB,IAAG9B,IAAGG,IAChB,CAzCiD,CAChD,IAAI8E,EAEA9E,EAGA,aAAaxB,KAAMsqB,KACtB9oB,EAAI2M,SAAUmc,EAAKprB,MAAO,KAAME,MAAO,IACvCoC,EAAI2L,MAAM3L,QAAK0nB,EAAY1nB,EAC3B8oB,EAAOA,EAAKprB,MAAO,KAAMC,SAI1B,IACCmH,EAAQ3I,SACN+sB,eAAgBC,mBAAoBL,IACpC/sB,QAAQ,kBACX,CACA,MAAQqtB,GAAU,CAElB,GAAItkB,EACH,MAAO,IAAKV,KAAKD,OAAOkH,WAAYvG,GAAS9E,IAE/C,CAqBA,OAAO,IAER,CAKAqpB,OAAAA,GAEC,MAAMC,EAAiBllB,KAAKD,OAAOkH,aAC7Bke,EAAanlB,KAAKwI,qBAEpB2c,EACGA,EAAW5nB,IAAM2nB,EAAe3nB,GAAK4nB,EAAW1pB,IAAMypB,EAAezpB,QAAsB6nB,IAAjB6B,EAAWvpB,GACzFoE,KAAKD,OAAOW,MAAOykB,EAAW5nB,EAAG4nB,EAAW1pB,EAAG0pB,EAAWvpB,GAM5DoE,KAAKD,OAAOW,MAAOwkB,EAAe3nB,GAAK,EAAG2nB,EAAezpB,GAAK,EAGhE,CASAskB,QAAAA,CAAUpX,GAET,IAAI5C,EAAS/F,KAAKD,OAAOO,YACrBqL,EAAe3L,KAAKD,OAAOyG,kBAM/B,GAHAjI,aAAcyB,KAAKskB,iBAGE,iBAAV3b,EACV3I,KAAKskB,gBAAkB9lB,WAAYwB,KAAK+f,SAAUpX,QAE9C,GAAIgD,EAAe,CAEvB,IAAI8Y,EAAOzkB,KAAKoH,UAIZrB,EAAOqf,QACVxlB,OAAOzG,SAASsrB,KAAOA,EAIf1e,EAAO0e,OAEF,MAATA,EACHzkB,KAAKqlB,sBAAuBzlB,OAAOzG,SAAS2iB,SAAWlc,OAAOzG,SAASC,QAGvE4G,KAAKqlB,sBAAuB,IAAMZ,GAcrC,CAED,CAEAa,YAAAA,CAAcxjB,GAEblC,OAAOwlB,QAAQE,aAAc,KAAM,KAAMxjB,GACzC9B,KAAKukB,sBAAwBgB,KAAKC,KAEnC,CAEAH,qBAAAA,CAAuBvjB,GAEtBvD,aAAcyB,KAAKylB,qBAEfF,KAAKC,MAAQxlB,KAAKukB,sBAAwBvkB,KAAKqkB,4BAClDrkB,KAAKslB,aAAcxjB,GAGnB9B,KAAKylB,oBAAsBjnB,YAAY,IAAMwB,KAAKslB,aAAcxjB,IAAO9B,KAAKqkB,4BAG9E,CAOAjd,OAAAA,CAAS1G,GAER,IAAIoB,EAAM,IAGN9G,EAAI0F,GAASV,KAAKD,OAAOyG,kBACzBiJ,EAAKzU,EAAIA,EAAE8F,aAAc,MAAS,KAClC2O,IACHA,EAAKiW,mBAAoBjW,IAG1B,IAAI8C,EAAQvS,KAAKD,OAAOkH,WAAYvG,GAOpC,GANKV,KAAKD,OAAOO,YAAYwf,gBAC5BvN,EAAM3W,OAAI0nB,GAKO,iBAAP7T,GAAmBA,EAAG9W,OAChCmJ,EAAM,IAAM2N,EAIR8C,EAAM3W,GAAK,IAAIkG,GAAO,IAAMyQ,EAAM3W,OAGlC,CACJ,IAAIgpB,EAAgB5kB,KAAKD,OAAOO,YAAYukB,kBAAoB,EAAI,GAChEtS,EAAMhV,EAAI,GAAKgV,EAAM9W,EAAI,GAAK8W,EAAM3W,GAAK,KAAIkG,GAAOyQ,EAAMhV,EAAIqnB,IAC9DrS,EAAM9W,EAAI,GAAK8W,EAAM3W,GAAK,KAAIkG,GAAO,KAAOyQ,EAAM9W,EAAImpB,IACtDrS,EAAM3W,GAAK,IAAIkG,GAAO,IAAMyQ,EAAM3W,EACvC,CAEA,OAAOkG,CAER,CAOA0iB,kBAAAA,CAAoB7f,GAEnB3E,KAAKilB,SAEN,ECpOc,MAAMU,EAEpB7lB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK4lB,sBAAwB5lB,KAAK4lB,sBAAsB1lB,KAAMF,MAC9DA,KAAK6lB,uBAAyB7lB,KAAK6lB,uBAAuB3lB,KAAMF,MAChEA,KAAK8lB,oBAAsB9lB,KAAK8lB,oBAAoB5lB,KAAMF,MAC1DA,KAAK+lB,sBAAwB/lB,KAAK+lB,sBAAsB7lB,KAAMF,MAC9DA,KAAKgmB,sBAAwBhmB,KAAKgmB,sBAAsB9lB,KAAMF,MAC9DA,KAAKimB,sBAAwBjmB,KAAKimB,sBAAsB/lB,KAAMF,MAC9DA,KAAKkmB,kBAAoBlmB,KAAKkmB,kBAAkBhmB,KAAMF,KAEvD,CAEA4F,MAAAA,GAEC,MAAMkG,EAAM9L,KAAKD,OAAOO,YAAYwL,IAC9Bqa,EAAgBnmB,KAAKD,OAAO8F,mBAElC7F,KAAK9I,QAAUa,SAASU,cAAe,SACvCuH,KAAK9I,QAAQT,UAAY,WACzBuJ,KAAK9I,QAAQoP,UACX,6CAA6CwF,EAAM,aAAe,mHACrBA,EAAM,iBAAmB,8QAIxE9L,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,SAGjD8I,KAAKomB,aAAelwB,EAAUiwB,EAAe,kBAC7CnmB,KAAKqmB,cAAgBnwB,EAAUiwB,EAAe,mBAC9CnmB,KAAKsmB,WAAapwB,EAAUiwB,EAAe,gBAC3CnmB,KAAKumB,aAAerwB,EAAUiwB,EAAe,kBAC7CnmB,KAAKwmB,aAAetwB,EAAUiwB,EAAe,kBAC7CnmB,KAAKymB,aAAevwB,EAAUiwB,EAAe,kBAC7CnmB,KAAK0mB,mBAAqBxwB,EAAUiwB,EAAe,qBAGnDnmB,KAAK2mB,mBAAqB3mB,KAAK9I,QAAQgM,cAAe,mBACtDlD,KAAK4mB,kBAAoB5mB,KAAK9I,QAAQgM,cAAe,kBACrDlD,KAAK6mB,kBAAoB7mB,KAAK9I,QAAQgM,cAAe,iBAEtD,CAKA4C,SAAAA,CAAWC,EAAQC,GAElBhG,KAAK9I,QAAQE,MAAM0F,QAAUiJ,EAAOvB,SAAW,QAAU,OAEzDxE,KAAK9I,QAAQ2J,aAAc,uBAAwBkF,EAAO+gB,gBAC1D9mB,KAAK9I,QAAQ2J,aAAc,4BAA6BkF,EAAOghB,mBAEhE,CAEA7mB,IAAAA,GAIC,IAAI8mB,EAAgB,CAAE,aAAc,SAIhCzsB,IACHysB,EAAgB,CAAE,eAGnBA,EAAc3rB,SAAS4rB,IACtBjnB,KAAKomB,aAAa/qB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAK4lB,uBAAuB,KAC7F5lB,KAAKqmB,cAAchrB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAK6lB,wBAAwB,KAC/F7lB,KAAKsmB,WAAWjrB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAK8lB,qBAAqB,KACzF9lB,KAAKumB,aAAalrB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAK+lB,uBAAuB,KAC7F/lB,KAAKwmB,aAAanrB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAKgmB,uBAAuB,KAC7FhmB,KAAKymB,aAAaprB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAKimB,uBAAuB,KAC7FjmB,KAAK0mB,mBAAmBrrB,SAASlF,GAAMA,EAAGsO,iBAAkBwiB,EAAWjnB,KAAKkmB,mBAAmB,IAAS,GAG1G,CAEA9E,MAAAA,GAEC,CAAE,aAAc,SAAU/lB,SAAS4rB,IAClCjnB,KAAKomB,aAAa/qB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAK4lB,uBAAuB,KAChG5lB,KAAKqmB,cAAchrB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAK6lB,wBAAwB,KAClG7lB,KAAKsmB,WAAWjrB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAK8lB,qBAAqB,KAC5F9lB,KAAKumB,aAAalrB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAK+lB,uBAAuB,KAChG/lB,KAAKwmB,aAAanrB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAKgmB,uBAAuB,KAChGhmB,KAAKymB,aAAaprB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAKimB,uBAAuB,KAChGjmB,KAAK0mB,mBAAmBrrB,SAASlF,GAAMA,EAAGuO,oBAAqBuiB,EAAWjnB,KAAKkmB,mBAAmB,IAAS,GAG7G,CAKA7f,MAAAA,GAEC,IAAI6gB,EAASlnB,KAAKD,OAAO2e,kBAGzB,IAAI1e,KAAKomB,gBAAiBpmB,KAAKqmB,iBAAkBrmB,KAAKsmB,cAAetmB,KAAKumB,gBAAiBvmB,KAAKwmB,gBAAiBxmB,KAAKymB,cAAcprB,SAASqX,IAC5IA,EAAK/b,UAAUE,OAAQ,UAAW,cAGlC6b,EAAK7R,aAAc,WAAY,WAAY,IAIxCqmB,EAAOhK,MAAOld,KAAKomB,aAAa/qB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,IAChHmmB,EAAO7D,OAAQrjB,KAAKqmB,cAAchrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,IAClHmmB,EAAO3D,IAAKvjB,KAAKsmB,WAAWjrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,IAC5GmmB,EAAOxD,MAAO1jB,KAAKumB,aAAalrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,KAGhHmmB,EAAOhK,MAAQgK,EAAO3D,KAAKvjB,KAAKwmB,aAAanrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,KAC7HmmB,EAAO7D,OAAS6D,EAAOxD,OAAO1jB,KAAKymB,aAAaprB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,WAAaT,EAAG4K,gBAAiB,WAAY,IAGpI,IAAI4K,EAAe3L,KAAKD,OAAOyG,kBAC/B,GAAImF,EAAe,CAElB,IAAIwb,EAAkBnnB,KAAKD,OAAOga,UAAU2E,kBAGxCyI,EAAgB9L,MAAOrb,KAAKwmB,aAAanrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,IACvIomB,EAAgB7L,MAAOtb,KAAKymB,aAAaprB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,IAIvIf,KAAKD,OAAOoH,gBAAiBwE,IAC5Bwb,EAAgB9L,MAAOrb,KAAKsmB,WAAWjrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,IACrIomB,EAAgB7L,MAAOtb,KAAKumB,aAAalrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,MAGvIomB,EAAgB9L,MAAOrb,KAAKomB,aAAa/qB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,IACvIomB,EAAgB7L,MAAOtb,KAAKqmB,cAAchrB,SAASlF,IAAQA,EAAGQ,UAAUC,IAAK,aAAc,WAAaT,EAAG4K,gBAAiB,WAAY,IAG9I,CAEA,GAAIf,KAAKD,OAAOO,YAAY8mB,iBAAmB,CAE9C,IAAIpgB,EAAUhH,KAAKD,OAAOkH,cAIrBjH,KAAKD,OAAOsnB,0BAA4BH,EAAOxD,KACnD1jB,KAAK6mB,kBAAkBlwB,UAAUC,IAAK,cAGtCoJ,KAAK6mB,kBAAkBlwB,UAAUE,OAAQ,aAErCmJ,KAAKD,OAAOO,YAAYwL,KAEtB9L,KAAKD,OAAOunB,4BAA8BJ,EAAOhK,MAAsB,IAAdlW,EAAQvL,EACrEuE,KAAK4mB,kBAAkBjwB,UAAUC,IAAK,aAGtCoJ,KAAK4mB,kBAAkBjwB,UAAUE,OAAQ,cAKrCmJ,KAAKD,OAAOunB,4BAA8BJ,EAAO7D,OAAuB,IAAdrc,EAAQvL,EACtEuE,KAAK2mB,mBAAmBhwB,UAAUC,IAAK,aAGvCoJ,KAAK2mB,mBAAmBhwB,UAAUE,OAAQ,aAI9C,CACD,CAEA2Q,OAAAA,GAECxH,KAAKohB,SACLphB,KAAK9I,QAAQL,QAEd,CAKA+uB,qBAAAA,CAAuBjhB,GAEtBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEmC,WAA3CjiB,KAAKD,OAAOO,YAAY6gB,eAC3BnhB,KAAKD,OAAOsb,OAGZrb,KAAKD,OAAOmd,MAGd,CAEA2I,sBAAAA,CAAwBlhB,GAEvBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEmC,WAA3CjiB,KAAKD,OAAOO,YAAY6gB,eAC3BnhB,KAAKD,OAAOub,OAGZtb,KAAKD,OAAOsjB,OAGd,CAEAyC,mBAAAA,CAAqBnhB,GAEpBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEZjiB,KAAKD,OAAOwjB,IAEb,CAEAwC,qBAAAA,CAAuBphB,GAEtBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEZjiB,KAAKD,OAAO2jB,MAEb,CAEAsC,qBAAAA,CAAuBrhB,GAEtBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEZjiB,KAAKD,OAAOsb,MAEb,CAEA4K,qBAAAA,CAAuBthB,GAEtBA,EAAMqS,iBACNhX,KAAKD,OAAOkiB,cAEZjiB,KAAKD,OAAOub,MAEb,CAEA4K,iBAAAA,CAAmBvhB,GAElB,MAAMoB,EAAS/F,KAAKD,OAAOO,YACrBinB,EAAWvnB,KAAKD,OAAOwc,qBAE7B1kB,EAAiBkO,EAAO8d,SAAW0D,EAAWA,EAASC,cAExD,ECjRc,MAAMC,EAEpB3nB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK0nB,kBAAoB1nB,KAAK0nB,kBAAkBxnB,KAAMF,KAEvD,CAEA4F,MAAAA,GAEC5F,KAAK9I,QAAUa,SAASU,cAAe,OACvCuH,KAAK9I,QAAQT,UAAY,WACzBuJ,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,SAEjD8I,KAAK2nB,IAAM5vB,SAASU,cAAe,QACnCuH,KAAK9I,QAAQ4B,YAAakH,KAAK2nB,IAEhC,CAKA7hB,SAAAA,CAAWC,EAAQC,GAElBhG,KAAK9I,QAAQE,MAAM0F,QAAUiJ,EAAOwQ,SAAW,QAAU,MAE1D,CAEArW,IAAAA,GAEKF,KAAKD,OAAOO,YAAYiW,UAAYvW,KAAK9I,SAC5C8I,KAAK9I,QAAQuN,iBAAkB,QAASzE,KAAK0nB,mBAAmB,EAGlE,CAEAtG,MAAAA,GAEMphB,KAAKD,OAAOO,YAAYiW,UAAYvW,KAAK9I,SAC7C8I,KAAK9I,QAAQwN,oBAAqB,QAAS1E,KAAK0nB,mBAAmB,EAGrE,CAKArhB,MAAAA,GAGC,GAAIrG,KAAKD,OAAOO,YAAYiW,UAAYvW,KAAK2nB,IAAM,CAElD,IAAItX,EAAQrQ,KAAKD,OAAO6nB,cAGpB5nB,KAAKD,OAAOgH,iBAAmB,IAClCsJ,EAAQ,GAGTrQ,KAAK2nB,IAAIvwB,MAAMD,UAAY,UAAWkZ,EAAO,GAE9C,CAED,CAEAwX,WAAAA,GAEC,OAAO7nB,KAAKD,OAAO8F,mBAAmBwH,WAEvC,CAUAqa,iBAAAA,CAAmB/iB,GAElB3E,KAAKD,OAAOkiB,YAAatd,GAEzBA,EAAMqS,iBAEN,IAAIyF,EAASzc,KAAKD,OAAOuI,YACrBwf,EAAcrL,EAAO9jB,OACrBovB,EAAa3rB,KAAKwgB,MAASjY,EAAMqjB,QAAUhoB,KAAK6nB,cAAkBC,GAElE9nB,KAAKD,OAAOO,YAAYwL,MAC3Bic,EAAaD,EAAcC,GAG5B,IAAIE,EAAgBjoB,KAAKD,OAAOkH,WAAWwV,EAAOsL,IAClD/nB,KAAKD,OAAOW,MAAOunB,EAAc1qB,EAAG0qB,EAAcxsB,EAEnD,CAEA+L,OAAAA,GAECxH,KAAK9I,QAAQL,QAEd,ECxGc,MAAMqxB,EAEpBpoB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAGdC,KAAKmoB,mBAAqB,EAG1BnoB,KAAKooB,cAAe,EAGpBpoB,KAAKqoB,sBAAwB,EAE7BroB,KAAKsoB,uBAAyBtoB,KAAKsoB,uBAAuBpoB,KAAMF,MAChEA,KAAKuoB,sBAAwBvoB,KAAKuoB,sBAAsBroB,KAAMF,KAE/D,CAKA8F,SAAAA,CAAWC,EAAQC,GAEdD,EAAOyiB,WACVzwB,SAAS0M,iBAAkB,QAASzE,KAAKuoB,uBAAuB,GAGhExwB,SAAS2M,oBAAqB,QAAS1E,KAAKuoB,uBAAuB,GAIhExiB,EAAO0iB,oBACV1wB,SAAS0M,iBAAkB,YAAazE,KAAKsoB,wBAAwB,GACrEvwB,SAAS0M,iBAAkB,YAAazE,KAAKsoB,wBAAwB,KAGrEtoB,KAAK0oB,aAEL3wB,SAAS2M,oBAAqB,YAAa1E,KAAKsoB,wBAAwB,GACxEvwB,SAAS2M,oBAAqB,YAAa1E,KAAKsoB,wBAAwB,GAG1E,CAMAI,UAAAA,GAEK1oB,KAAKooB,eACRpoB,KAAKooB,cAAe,EACpBpoB,KAAKD,OAAO8F,mBAAmBzO,MAAMuxB,OAAS,GAGhD,CAMAC,UAAAA,IAE2B,IAAtB5oB,KAAKooB,eACRpoB,KAAKooB,cAAe,EACpBpoB,KAAKD,OAAO8F,mBAAmBzO,MAAMuxB,OAAS,OAGhD,CAEAnhB,OAAAA,GAECxH,KAAK0oB,aAEL3wB,SAAS2M,oBAAqB,QAAS1E,KAAKuoB,uBAAuB,GACnExwB,SAAS2M,oBAAqB,YAAa1E,KAAKsoB,wBAAwB,GACxEvwB,SAAS2M,oBAAqB,YAAa1E,KAAKsoB,wBAAwB,EAEzE,CAQAA,sBAAAA,CAAwB3jB,GAEvB3E,KAAK0oB,aAELnqB,aAAcyB,KAAKqoB,uBAEnBroB,KAAKqoB,sBAAwB7pB,WAAYwB,KAAK4oB,WAAW1oB,KAAMF,MAAQA,KAAKD,OAAOO,YAAYuoB,eAEhG,CAQAN,qBAAAA,CAAuB5jB,GAEtB,GAAI4gB,KAAKC,MAAQxlB,KAAKmoB,mBAAqB,IAAO,CAEjDnoB,KAAKmoB,mBAAqB5C,KAAKC,MAE/B,IAAIhV,EAAQ7L,EAAMxH,SAAWwH,EAAMmkB,WAC/BtY,EAAQ,EACXxQ,KAAKD,OAAOub,OAEJ9K,EAAQ,GAChBxQ,KAAKD,OAAOsb,MAGd,CAED,ECpHM,MAAM0N,EAAaA,CAAEjnB,EAAK2T,KAEhC,MAAMuT,EAASjxB,SAASU,cAAe,UACvCuwB,EAAOtwB,KAAO,kBACdswB,EAAOC,OAAQ,EACfD,EAAOE,OAAQ,EACfF,EAAOllB,IAAMhC,EAEW,mBAAb2T,IAGVuT,EAAOG,OAASH,EAAOI,mBAAqBzkB,KACxB,SAAfA,EAAMjM,MAAmB,kBAAkB0B,KAAM4uB,EAAO5kB,eAG3D4kB,EAAOG,OAASH,EAAOI,mBAAqBJ,EAAOK,QAAU,KAE7D5T,IAED,EAIDuT,EAAOK,QAAUC,IAGhBN,EAAOG,OAASH,EAAOI,mBAAqBJ,EAAOK,QAAU,KAE7D5T,EAAU,IAAI8T,MAAO,0BAA4BP,EAAOllB,IAAM,KAAOwlB,GAAO,GAO9E,MAAMtwB,EAAOjB,SAASmL,cAAe,QACrClK,EAAKkc,aAAc8T,EAAQhwB,EAAKwwB,UAAW,ECtC7B,MAAMC,EAEpB3pB,WAAAA,CAAa4pB,GAEZ1pB,KAAKD,OAAS2pB,EAGd1pB,KAAK2pB,MAAQ,OAGb3pB,KAAK4pB,kBAAoB,GAEzB5pB,KAAK6pB,kBAAoB,EAE1B,CAeAppB,IAAAA,CAAMqpB,EAASC,GAMd,OAJA/pB,KAAK2pB,MAAQ,UAEbG,EAAQzuB,QAAS2E,KAAKgqB,eAAe9pB,KAAMF,OAEpC,IAAI8c,SAASmN,IAEnB,IAAIC,EAAU,GACbC,EAAgB,EAcjB,GAZAJ,EAAa1uB,SAASL,IAEhBA,EAAEovB,YAAapvB,EAAEovB,cACjBpvB,EAAEiuB,MACLjpB,KAAK6pB,kBAAkBvqB,KAAMtE,GAG7BkvB,EAAQ5qB,KAAMtE,GAEhB,IAGGkvB,EAAQvxB,OAAS,CACpBwxB,EAAgBD,EAAQvxB,OAExB,MAAM0xB,EAAwBrvB,IACzBA,GAA2B,mBAAfA,EAAEya,UAA0Bza,EAAEya,WAEtB,KAAlB0U,GACLnqB,KAAKsqB,cAAcC,KAAMN,EAC1B,EAIDC,EAAQ7uB,SAASL,IACI,iBAATA,EAAEyU,IACZzP,KAAKgqB,eAAgBhvB,GACrBqvB,EAAsBrvB,IAEG,iBAAVA,EAAE8I,IACjBilB,EAAY/tB,EAAE8I,KAAK,IAAMumB,EAAqBrvB,MAG9CwvB,QAAQC,KAAM,6BAA8BzvB,GAC5CqvB,IACD,GAEF,MAECrqB,KAAKsqB,cAAcC,KAAMN,EAC1B,GAIF,CAMAK,WAAAA,GAEC,OAAO,IAAIxN,SAASmN,IAEnB,IAAIS,EAAe9rB,OAAO+rB,OAAQ3qB,KAAK4pB,mBACnCgB,EAAsBF,EAAa/xB,OAGvC,GAA4B,IAAxBiyB,EACH5qB,KAAK6qB,YAAYN,KAAMN,OAGnB,CAEJ,IAAIa,EAEAC,EAAuBA,KACI,KAAxBH,EACL5qB,KAAK6qB,YAAYN,KAAMN,GAGvBa,GACD,EAGG70B,EAAI,EAGR60B,EAAiBA,KAEhB,IAAIE,EAASN,EAAaz0B,KAG1B,GAA2B,mBAAhB+0B,EAAOC,KAAsB,CACvC,IAAI3mB,EAAU0mB,EAAOC,KAAMjrB,KAAKD,QAG5BuE,GAAmC,mBAAjBA,EAAQimB,KAC7BjmB,EAAQimB,KAAMQ,GAGdA,GAEF,MAECA,GACD,EAIDD,GAED,IAIF,CAKAD,SAAAA,GAUC,OARA7qB,KAAK2pB,MAAQ,SAET3pB,KAAK6pB,kBAAkBlxB,QAC1BqH,KAAK6pB,kBAAkBxuB,SAASL,IAC/B+tB,EAAY/tB,EAAE8I,IAAK9I,EAAEya,SAAU,IAI1BqH,QAAQmN,SAEhB,CASAD,cAAAA,CAAgBgB,GAIU,IAArBrrB,UAAUhH,QAAwC,iBAAjBgH,UAAU,IAC9CqrB,EAASrrB,UAAU,IACZ8P,GAAK9P,UAAU,GAII,mBAAXqrB,IACfA,EAASA,KAGV,IAAIvb,EAAKub,EAAOvb,GAEE,iBAAPA,EACV+a,QAAQC,KAAM,mDAAqDO,QAE5B1H,IAA/BtjB,KAAK4pB,kBAAkBna,IAC/BzP,KAAK4pB,kBAAkBna,GAAMub,EAIV,WAAfhrB,KAAK2pB,OAA6C,mBAAhBqB,EAAOC,MAC5CD,EAAOC,KAAMjrB,KAAKD,SAInByqB,QAAQC,KAAM,eAAgBhb,EAAI,uCAGpC,CAOAyb,SAAAA,CAAWzb,GAEV,QAASzP,KAAK4pB,kBAAkBna,EAEjC,CAQA0b,SAAAA,CAAW1b,GAEV,OAAOzP,KAAK4pB,kBAAkBna,EAE/B,CAEA2b,oBAAAA,GAEC,OAAOprB,KAAK4pB,iBAEb,CAEApiB,OAAAA,GAEC5I,OAAO+rB,OAAQ3qB,KAAK4pB,mBAAoBvuB,SAAS2vB,IAClB,mBAAnBA,EAAOxjB,SACjBwjB,EAAOxjB,SACR,IAGDxH,KAAK4pB,kBAAoB,GACzB5pB,KAAK6pB,kBAAoB,EAE1B,EClPc,MAAMwB,EAEpBvrB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAGdC,KAAKsrB,YAAc,EACnBtrB,KAAKurB,YAAc,EACnBvrB,KAAKwrB,gBAAkB,EACvBxrB,KAAKyrB,eAAgB,EAErBzrB,KAAK0rB,cAAgB1rB,KAAK0rB,cAAcxrB,KAAMF,MAC9CA,KAAK2rB,cAAgB3rB,KAAK2rB,cAAczrB,KAAMF,MAC9CA,KAAK4rB,YAAc5rB,KAAK4rB,YAAY1rB,KAAMF,MAC1CA,KAAK6rB,aAAe7rB,KAAK6rB,aAAa3rB,KAAMF,MAC5CA,KAAK8rB,YAAc9rB,KAAK8rB,YAAY5rB,KAAMF,MAC1CA,KAAK+rB,WAAa/rB,KAAK+rB,WAAW7rB,KAAMF,KAEzC,CAKAE,IAAAA,GAEC,IAAIimB,EAAgBnmB,KAAKD,OAAO8F,mBAE5B,kBAAmBjG,QAEtBumB,EAAc1hB,iBAAkB,cAAezE,KAAK0rB,eAAe,GACnEvF,EAAc1hB,iBAAkB,cAAezE,KAAK2rB,eAAe,GACnExF,EAAc1hB,iBAAkB,YAAazE,KAAK4rB,aAAa,IAEvDhsB,OAAO3F,UAAU+xB,kBAEzB7F,EAAc1hB,iBAAkB,gBAAiBzE,KAAK0rB,eAAe,GACrEvF,EAAc1hB,iBAAkB,gBAAiBzE,KAAK2rB,eAAe,GACrExF,EAAc1hB,iBAAkB,cAAezE,KAAK4rB,aAAa,KAIjEzF,EAAc1hB,iBAAkB,aAAczE,KAAK6rB,cAAc,GACjE1F,EAAc1hB,iBAAkB,YAAazE,KAAK8rB,aAAa,GAC/D3F,EAAc1hB,iBAAkB,WAAYzE,KAAK+rB,YAAY,GAG/D,CAKA3K,MAAAA,GAEC,IAAI+E,EAAgBnmB,KAAKD,OAAO8F,mBAEhCsgB,EAAczhB,oBAAqB,cAAe1E,KAAK0rB,eAAe,GACtEvF,EAAczhB,oBAAqB,cAAe1E,KAAK2rB,eAAe,GACtExF,EAAczhB,oBAAqB,YAAa1E,KAAK4rB,aAAa,GAElEzF,EAAczhB,oBAAqB,gBAAiB1E,KAAK0rB,eAAe,GACxEvF,EAAczhB,oBAAqB,gBAAiB1E,KAAK2rB,eAAe,GACxExF,EAAczhB,oBAAqB,cAAe1E,KAAK4rB,aAAa,GAEpEzF,EAAczhB,oBAAqB,aAAc1E,KAAK6rB,cAAc,GACpE1F,EAAczhB,oBAAqB,YAAa1E,KAAK8rB,aAAa,GAClE3F,EAAczhB,oBAAqB,WAAY1E,KAAK+rB,YAAY,EAEjE,CAMAE,gBAAAA,CAAkB30B,GAGjB,GAAID,EAASC,EAAQ,oCAAuC,OAAO,EAEnE,KAAOA,GAAyC,mBAAxBA,EAAOkJ,cAA8B,CAC5D,GAAIlJ,EAAOkJ,aAAc,sBAAyB,OAAO,EACzDlJ,EAASA,EAAOM,UACjB,CAEA,OAAO,CAER,CAQAi0B,YAAAA,CAAclnB,GAIb,GAFA3E,KAAKyrB,eAAgB,EAEjBzrB,KAAKisB,iBAAkBtnB,EAAMrN,QAAW,OAAO,EAEnD0I,KAAKsrB,YAAc3mB,EAAMunB,QAAQ,GAAGlE,QACpChoB,KAAKurB,YAAc5mB,EAAMunB,QAAQ,GAAG1V,QACpCxW,KAAKwrB,gBAAkB7mB,EAAMunB,QAAQvzB,MAEtC,CAOAmzB,WAAAA,CAAannB,GAEZ,GAAI3E,KAAKisB,iBAAkBtnB,EAAMrN,QAAW,OAAO,EAEnD,IAAIyO,EAAS/F,KAAKD,OAAOO,YAGzB,GAAKN,KAAKyrB,cA8EDlxB,GACRoK,EAAMqS,qBA/EmB,CACzBhX,KAAKD,OAAOkiB,YAAatd,GAEzB,IAAIwnB,EAAWxnB,EAAMunB,QAAQ,GAAGlE,QAC5BoE,EAAWznB,EAAMunB,QAAQ,GAAG1V,QAGhC,GAA6B,IAAzB7R,EAAMunB,QAAQvzB,QAAyC,IAAzBqH,KAAKwrB,gBAAwB,CAE9D,IAAI9M,EAAkB1e,KAAKD,OAAO2e,gBAAgB,CAAE2N,kBAAkB,IAElEC,EAASH,EAAWnsB,KAAKsrB,YAC5BiB,EAASH,EAAWpsB,KAAKurB,YAEtBe,EA1IgB,IA0IYlwB,KAAKowB,IAAKF,GAAWlwB,KAAKowB,IAAKD,IAC9DvsB,KAAKyrB,eAAgB,EACS,WAA1B1lB,EAAOob,eACNpb,EAAO+F,IACV9L,KAAKD,OAAOub,OAGZtb,KAAKD,OAAOsb,OAIbrb,KAAKD,OAAOmd,QAGLoP,GAxJW,IAwJkBlwB,KAAKowB,IAAKF,GAAWlwB,KAAKowB,IAAKD,IACpEvsB,KAAKyrB,eAAgB,EACS,WAA1B1lB,EAAOob,eACNpb,EAAO+F,IACV9L,KAAKD,OAAOsb,OAGZrb,KAAKD,OAAOub,OAIbtb,KAAKD,OAAOsjB,SAGLkJ,EAtKW,IAsKiB7N,EAAgB6E,IACpDvjB,KAAKyrB,eAAgB,EACS,WAA1B1lB,EAAOob,eACVnhB,KAAKD,OAAOsb,OAGZrb,KAAKD,OAAOwjB,MAGLgJ,GA/KW,IA+KkB7N,EAAgBgF,OACrD1jB,KAAKyrB,eAAgB,EACS,WAA1B1lB,EAAOob,eACVnhB,KAAKD,OAAOub,OAGZtb,KAAKD,OAAO2jB,QAMV3d,EAAO8d,UACN7jB,KAAKyrB,eAAiBzrB,KAAKD,OAAOoH,oBACrCxC,EAAMqS,iBAMPrS,EAAMqS,gBAGR,CACD,CAOD,CAOA+U,UAAAA,CAAYpnB,GAEX3E,KAAKyrB,eAAgB,CAEtB,CAOAC,aAAAA,CAAe/mB,GAEVA,EAAM8nB,cAAgB9nB,EAAM+nB,sBAA8C,UAAtB/nB,EAAM8nB,cAC7D9nB,EAAMunB,QAAU,CAAC,CAAElE,QAASrjB,EAAMqjB,QAASxR,QAAS7R,EAAM6R,UAC1DxW,KAAK6rB,aAAclnB,GAGrB,CAOAgnB,aAAAA,CAAehnB,GAEVA,EAAM8nB,cAAgB9nB,EAAM+nB,sBAA8C,UAAtB/nB,EAAM8nB,cAC7D9nB,EAAMunB,QAAU,CAAC,CAAElE,QAASrjB,EAAMqjB,QAASxR,QAAS7R,EAAM6R,UAC1DxW,KAAK8rB,YAAannB,GAGpB,CAOAinB,WAAAA,CAAajnB,GAERA,EAAM8nB,cAAgB9nB,EAAM+nB,sBAA8C,UAAtB/nB,EAAM8nB,cAC7D9nB,EAAMunB,QAAU,CAAC,CAAElE,QAASrjB,EAAMqjB,QAASxR,QAAS7R,EAAM6R,UAC1DxW,KAAK+rB,WAAYpnB,GAGnB,EC7PD,MAAMgoB,EAAc,QACdC,EAAa,OAEJ,MAAMC,EAEpB/sB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,EAEdC,KAAK8sB,oBAAsB9sB,KAAK8sB,oBAAoB5sB,KAAMF,MAC1DA,KAAK+sB,sBAAwB/sB,KAAK+sB,sBAAsB7sB,KAAMF,KAE/D,CAKA8F,SAAAA,CAAWC,EAAQC,GAEdD,EAAO8d,SACV7jB,KAAKgtB,QAGLhtB,KAAKiI,QACLjI,KAAKohB,SAGP,CAEAlhB,IAAAA,GAEKF,KAAKD,OAAOO,YAAYujB,UAC3B7jB,KAAKD,OAAO8F,mBAAmBpB,iBAAkB,cAAezE,KAAK8sB,qBAAqB,EAG5F,CAEA1L,MAAAA,GAECphB,KAAKD,OAAO8F,mBAAmBnB,oBAAqB,cAAe1E,KAAK8sB,qBAAqB,GAC7F/0B,SAAS2M,oBAAqB,cAAe1E,KAAK+sB,uBAAuB,EAE1E,CAEA9kB,KAAAA,GAEKjI,KAAK2pB,QAAUgD,IAClB3sB,KAAKD,OAAO8F,mBAAmBlP,UAAUC,IAAK,WAC9CmB,SAAS0M,iBAAkB,cAAezE,KAAK+sB,uBAAuB,IAGvE/sB,KAAK2pB,MAAQgD,CAEd,CAEAK,IAAAA,GAEKhtB,KAAK2pB,QAAUiD,IAClB5sB,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,WACjDkB,SAAS2M,oBAAqB,cAAe1E,KAAK+sB,uBAAuB,IAG1E/sB,KAAK2pB,MAAQiD,CAEd,CAEA9K,SAAAA,GAEC,OAAO9hB,KAAK2pB,QAAUgD,CAEvB,CAEAnlB,OAAAA,GAECxH,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,UAElD,CAEAi2B,mBAAAA,CAAqBnoB,GAEpB3E,KAAKiI,OAEN,CAEA8kB,qBAAAA,CAAuBpoB,GAEtB,IAAIwhB,EAAgBxuB,EAASgN,EAAMrN,OAAQ,WACtC6uB,GAAiBA,IAAkBnmB,KAAKD,OAAO8F,oBACnD7F,KAAKgtB,MAGP,ECjGc,MAAMC,EAEpBntB,WAAAA,CAAaC,GAEZC,KAAKD,OAASA,CAEf,CAEA6F,MAAAA,GAEC5F,KAAK9I,QAAUa,SAASU,cAAe,OACvCuH,KAAK9I,QAAQT,UAAY,gBACzBuJ,KAAK9I,QAAQ2J,aAAc,qBAAsB,IACjDb,KAAK9I,QAAQ2J,aAAc,WAAY,KACvCb,KAAKD,OAAO8F,mBAAmB/M,YAAakH,KAAK9I,QAElD,CAKA4O,SAAAA,CAAWC,EAAQC,GAEdD,EAAOyX,WACVxd,KAAK9I,QAAQ2J,aAAc,cAA2C,iBAArBkF,EAAOyX,UAAyBzX,EAAOyX,UAAY,SAGtG,CAQAnX,MAAAA,GAEKrG,KAAKD,OAAOO,YAAYkd,WAC3Bxd,KAAK9I,SAAW8I,KAAKD,OAAOyG,oBAC3BxG,KAAKD,OAAOK,iBACZJ,KAAKD,OAAOoG,gBAEbnG,KAAK9I,QAAQoP,UAAYtG,KAAK0d,iBAAmB,iEAGnD,CAQAwP,gBAAAA,GAEKltB,KAAKD,OAAOO,YAAYkd,WAC3Bxd,KAAKmtB,aACJntB,KAAKD,OAAOK,iBACZJ,KAAKD,OAAOoG,cAEbnG,KAAKD,OAAO8F,mBAAmBlP,UAAUC,IAAK,cAG9CoJ,KAAKD,OAAO8F,mBAAmBlP,UAAUE,OAAQ,aAGnD,CAMAs2B,QAAAA,GAEC,OAAOntB,KAAKD,OAAO8D,mBAAmBtN,iBAAkB,6BAA8BoC,OAAS,CAEhG,CAQAy0B,oBAAAA,GAEC,QAASxtB,OAAOzG,SAASC,OAAOrC,MAAO,aAExC,CAWA2mB,aAAAA,CAAehd,EAAQV,KAAKD,OAAOyG,mBAGlC,GAAI9F,EAAMF,aAAc,cACvB,OAAOE,EAAMI,aAAc,cAI5B,IAAIusB,EAAgB3sB,EAAMnK,iBAAkB,eAC5C,OAAI82B,EACIh3B,MAAMC,KAAK+2B,GAAejuB,KAAKye,GAAgBA,EAAavX,YAAYlE,KAAM,MAG/E,IAER,CAEAoF,OAAAA,GAECxH,KAAK9I,QAAQL,QAEd,ECvHc,MAAMy2B,EASpBxtB,WAAAA,CAAa2K,EAAW8iB,GAGvBvtB,KAAKwtB,SAAW,IAChBxtB,KAAKytB,UAAYztB,KAAKwtB,SAAS,EAC/BxtB,KAAK0tB,UAAY,EAGjB1tB,KAAK2tB,SAAU,EAGf3tB,KAAKuW,SAAW,EAGhBvW,KAAK4tB,eAAiB,EAEtB5tB,KAAKyK,UAAYA,EACjBzK,KAAKutB,cAAgBA,EAErBvtB,KAAK6tB,OAAS91B,SAASU,cAAe,UACtCuH,KAAK6tB,OAAOp3B,UAAY,WACxBuJ,KAAK6tB,OAAOhrB,MAAQ7C,KAAKwtB,SACzBxtB,KAAK6tB,OAAO/qB,OAAS9C,KAAKwtB,SAC1BxtB,KAAK6tB,OAAOz2B,MAAMyL,MAAQ7C,KAAKytB,UAAY,KAC3CztB,KAAK6tB,OAAOz2B,MAAM0L,OAAS9C,KAAKytB,UAAY,KAC5CztB,KAAK8tB,QAAU9tB,KAAK6tB,OAAOE,WAAY,MAEvC/tB,KAAKyK,UAAU3R,YAAakH,KAAK6tB,QAEjC7tB,KAAK4F,QAEN,CAEAooB,UAAAA,CAAYt3B,GAEX,MAAMu3B,EAAajuB,KAAK2tB,QAExB3tB,KAAK2tB,QAAUj3B,GAGVu3B,GAAcjuB,KAAK2tB,QACvB3tB,KAAKkuB,UAGLluB,KAAK4F,QAGP,CAEAsoB,OAAAA,GAEC,MAAMC,EAAiBnuB,KAAKuW,SAE5BvW,KAAKuW,SAAWvW,KAAKutB,gBAIjBY,EAAiB,IAAOnuB,KAAKuW,SAAW,KAC3CvW,KAAK4tB,eAAiB5tB,KAAKuW,UAG5BvW,KAAK4F,SAED5F,KAAK2tB,SACR5yB,sBAAuBiF,KAAKkuB,QAAQhuB,KAAMF,MAG5C,CAKA4F,MAAAA,GAEC,IAAI2Q,EAAWvW,KAAK2tB,QAAU3tB,KAAKuW,SAAW,EAC7C6X,EAAWpuB,KAAKytB,UAAcztB,KAAK0tB,UACnCjvB,EAAIuB,KAAKytB,UACTjyB,EAAIwE,KAAKytB,UACTY,EAAW,GAGZruB,KAAK4tB,gBAAgD,IAA5B,EAAI5tB,KAAK4tB,gBAElC,MAAMU,GAAelyB,KAAKmyB,GAAK,EAAQhY,GAAuB,EAAVna,KAAKmyB,IACnDC,GAAiBpyB,KAAKmyB,GAAK,EAAQvuB,KAAK4tB,gBAA6B,EAAVxxB,KAAKmyB,IAEtEvuB,KAAK8tB,QAAQW,OACbzuB,KAAK8tB,QAAQY,UAAW,EAAG,EAAG1uB,KAAKwtB,SAAUxtB,KAAKwtB,UAGlDxtB,KAAK8tB,QAAQa,YACb3uB,KAAK8tB,QAAQc,IAAKnwB,EAAGjD,EAAG4yB,EAAS,EAAG,EAAa,EAAVhyB,KAAKmyB,IAAQ,GACpDvuB,KAAK8tB,QAAQe,UAAY,uBACzB7uB,KAAK8tB,QAAQgB,OAGb9uB,KAAK8tB,QAAQa,YACb3uB,KAAK8tB,QAAQc,IAAKnwB,EAAGjD,EAAG4yB,EAAQ,EAAa,EAAVhyB,KAAKmyB,IAAQ,GAChDvuB,KAAK8tB,QAAQiB,UAAY/uB,KAAK0tB,UAC9B1tB,KAAK8tB,QAAQkB,YAAc,6BAC3BhvB,KAAK8tB,QAAQmB,SAETjvB,KAAK2tB,UAER3tB,KAAK8tB,QAAQa,YACb3uB,KAAK8tB,QAAQc,IAAKnwB,EAAGjD,EAAG4yB,EAAQI,EAAYF,GAAU,GACtDtuB,KAAK8tB,QAAQiB,UAAY/uB,KAAK0tB,UAC9B1tB,KAAK8tB,QAAQkB,YAAc,OAC3BhvB,KAAK8tB,QAAQmB,UAGdjvB,KAAK8tB,QAAQ1d,UAAW3R,EAAM4vB,GAAgB7yB,EAAM6yB,IAGhDruB,KAAK2tB,SACR3tB,KAAK8tB,QAAQe,UAAY,OACzB7uB,KAAK8tB,QAAQoB,SAAU,EAAG,EAAGb,GAAkBA,GAC/CruB,KAAK8tB,QAAQoB,SAAUb,GAAkB,EAAGA,GAAkBA,KAG9DruB,KAAK8tB,QAAQa,YACb3uB,KAAK8tB,QAAQ1d,UAAW,EAAG,GAC3BpQ,KAAK8tB,QAAQqB,OAAQ,EAAG,GACxBnvB,KAAK8tB,QAAQsB,OAAQf,GAAcA,IACnCruB,KAAK8tB,QAAQsB,OAAQ,EAAGf,GACxBruB,KAAK8tB,QAAQe,UAAY,OACzB7uB,KAAK8tB,QAAQgB,QAGd9uB,KAAK8tB,QAAQuB,SAEd,CAEAC,EAAAA,CAAI52B,EAAM62B,GACTvvB,KAAK6tB,OAAOppB,iBAAkB/L,EAAM62B,GAAU,EAC/C,CAEAC,GAAAA,CAAK92B,EAAM62B,GACVvvB,KAAK6tB,OAAOnpB,oBAAqBhM,EAAM62B,GAAU,EAClD,CAEA/nB,OAAAA,GAECxH,KAAK2tB,SAAU,EAEX3tB,KAAK6tB,OAAOj2B,YACfoI,KAAKyK,UAAUoF,YAAa7P,KAAK6tB,OAGnC,EC/Jc,IAAA4B,EAAA,CAId5sB,MAAO,IACPC,OAAQ,IAGR+Z,OAAQ,IAGR6S,SAAU,GACVC,SAAU,EAGVnrB,UAAU,EAIV4iB,kBAAkB,EAGlBN,eAAgB,eAIhBC,mBAAoB,QAGpBxQ,UAAU,EAgBVrQ,aAAa,EAMbE,gBAAiB,MAIjBye,mBAAmB,EAInBJ,MAAM,EAGNmL,sBAAsB,EAGtB5L,aAAa,EAGboB,SAAS,EAGTvC,UAAU,EAMVhB,kBAAmB,KAInBgO,eAAe,EAGf3P,UAAU,EAGVvO,QAAQ,EAGRme,OAAO,EAGPC,MAAM,EAGNjkB,KAAK,EA0BLqV,eAAgB,UAGhB6O,SAAS,EAGTjW,WAAW,EAIX+F,eAAe,EAIf+D,UAAU,EAIVoM,MAAM,EAGN5qB,OAAO,EAGPmY,WAAW,EAGX0S,kBAAkB,EAMlBhsB,cAAe,KAOf3D,eAAgB,KAGhBmO,aAAa,EAIbyD,mBAAoB,KAIpBhB,kBAAmB,OACnBC,oBAAqB,EACrBlC,sBAAsB,EAKtB8C,kBAAmB,CAClB,UACA,QACA,mBACA,UACA,YACA,cACA,iBACA,eACA,eACA,gBACA,UACA,kBAQDme,UAAW,EAGXrM,oBAAoB,EAGpBsM,gBAAiB,KAKjBC,cAAe,KAGf7H,YAAY,EAKZ8H,cAAc,EAGdprB,aAAa,EAGbqrB,mBAAmB,EAGnBC,iCAAiC,EAGjCC,WAAY,QAGZC,gBAAiB,UAGjB3lB,qBAAsB,OAGtBb,wBAAyB,GAGzBE,uBAAwB,GAGxBE,yBAA0B,GAG1BE,2BAA4B,GAG5B+C,6BAA8B,KAC9BM,2BAA4B,KAM5ByQ,KAAM,KAMN9G,aAAc,OAQdO,WAAY,YAMZwB,eAAgB,OAIhBoX,sBAAuB,IAIvBrT,oBAAqBkG,OAAOoN,kBAG5B5S,sBAAsB,EAOtBT,qBAAsB,EAGtBsT,aAAc,EAKdC,mBAAoB,EAGpBh0B,QAAS,QAGT2rB,oBAAoB,EAGpBI,eAAgB,IAIhBkI,qBAAqB,EAGrBhH,aAAc,GAGdD,QAAS,ICzSH,MAAMkH,EAAU,QASR,SAAAC,EAAU9K,EAAexlB,GAInChB,UAAUhH,OAAS,IACtBgI,EAAUhB,UAAU,GACpBwmB,EAAgBpuB,SAASmL,cAAe,YAGzC,MAAMnD,EAAS,CAAA,EAGXgG,IASHoU,EACAhO,EAGAsI,EACA9I,EAiCAulB,EA/CGnrB,EAAS,CAAA,EAGZorB,GAAc,EAGdC,GAAQ,EAWRC,EAAoB,CACnB/J,0BAA0B,EAC1BD,wBAAwB,GAMzBsC,EAAQ,GAGRtZ,EAAQ,EAIRihB,EAAkB,CAAEnuB,OAAQ,GAAI+c,SAAU,IAG1CqR,EAAM,CAAA,EAMNd,EAAa,OAGbN,EAAY,EAIZqB,EAAmB,EACnBC,GAAsB,EACtBC,GAAkB,EAKlBhlB,GAAe,IAAI7M,EAAcE,GACjCmG,GAAc,IAAIP,EAAa5F,GAC/BikB,GAAc,IAAIvc,EAAa1H,GAC/B2O,GAAc,IAAIX,EAAahO,GAC/Boc,GAAc,IAAIvS,EAAa7J,GAC/B4xB,GAAa,IAAI/d,EAAY7T,GAC7B6xB,GAAY,IAAIpV,EAAWzc,GAC3Bga,GAAY,IAAIwE,EAAWxe,GAC3BmgB,GAAW,IAAIF,EAAUjgB,GACzB8iB,GAAW,IAAI9B,EAAUhhB,GACzB5G,GAAW,IAAIirB,EAAUrkB,GACzByE,GAAW,IAAImhB,EAAU5lB,GACzBwW,GAAW,IAAIkR,EAAU1nB,GACzB8xB,GAAU,IAAI3J,EAASnoB,GACvB+pB,GAAU,IAAIL,EAAS1pB,GACvBkI,GAAQ,IAAI4kB,EAAO9sB,GACnB+vB,GAAQ,IAAIzE,EAAOtrB,GACnB0d,GAAQ,IAAIwP,EAAOltB,GAmEpB,SAAS+xB,KAERV,GAAQ,EAoGHrrB,EAAOmqB,kBACX6B,EAAeR,EAAIS,QAAS,qCAAsC32B,SAASqF,IAC1E,MAAMuxB,EAASvxB,EAAM9I,WAKY,IAA7Bq6B,EAAOC,mBAA2B,WAAW93B,KAAM63B,EAAOtf,UAC7Dsf,EAAOp7B,SAGP6J,EAAM7J,QACP,IAYH,WAGC06B,EAAI9U,OAAO9lB,UAAUC,IAAK,iBAEtBu7B,EACHZ,EAAIS,QAAQr7B,UAAUC,IAAK,YAG3B26B,EAAIS,QAAQr7B,UAAUE,OAAQ,YAG/BslB,GAAYvW,SACZM,GAAYN,SACZoe,GAAYpe,SACZpB,GAASoB,SACT2Q,GAAS3Q,SACT6X,GAAM7X,SAGN2rB,EAAIa,a1BrK6BC,EAAE5nB,EAAW6nB,EAASC,EAAWjsB,EAAU,MAG7E,IAAIksB,EAAQ/nB,EAAUlU,iBAAkB,IAAMg8B,GAI9C,IAAK,IAAIt8B,EAAI,EAAGA,EAAIu8B,EAAM75B,OAAQ1C,IAAM,CACvC,IAAIw8B,EAAWD,EAAMv8B,GACrB,GAAIw8B,EAAS76B,aAAe6S,EAC3B,OAAOgoB,CAET,CAGA,IAAI/f,EAAO3a,SAASU,cAAe65B,GAKnC,OAJA5f,EAAKjc,UAAY87B,EACjB7f,EAAKpM,UAAYA,EACjBmE,EAAU3R,YAAa4Z,GAEhBA,CAAI,E0BiJSqf,CAA0BR,EAAIS,QAAS,MAAO,gBAAiBjsB,EAAOvB,SAAW,6DAA+D,MAEnK+sB,EAAImB,cAYL,WAEC,IAAIA,EAAgBnB,EAAIS,QAAQ9uB,cAAe,gBAC1CwvB,IACJA,EAAgB36B,SAASU,cAAe,OACxCi6B,EAAct7B,MAAMiiB,SAAW,WAC/BqZ,EAAct7B,MAAM0L,OAAS,MAC7B4vB,EAAct7B,MAAMyL,MAAQ,MAC5B6vB,EAAct7B,MAAMu7B,SAAW,SAC/BD,EAAct7B,MAAMw7B,KAAO,6BAC3BF,EAAc/7B,UAAUC,IAAK,eAC7B87B,EAAc7xB,aAAc,YAAa,UACzC6xB,EAAc7xB,aAAc,cAAc,QAC1C0wB,EAAIS,QAAQl5B,YAAa45B,IAE1B,OAAOA,CAER,CA7BqBG,GAEpBtB,EAAIS,QAAQnxB,aAAc,OAAQ,cACnC,CA/ICiyB,GAmQI/sB,EAAOb,aACVtF,OAAO6E,iBAAkB,UAAWsuB,IAAe,GAnCpDC,aAAa,OACPrB,GAAW1b,YAAwC,IAA1Bsb,EAAIS,QAAQrb,WAA8C,IAA3B4a,EAAIS,QAAQiB,cACxE1B,EAAIS,QAAQrb,UAAY,EACxB4a,EAAIS,QAAQiB,WAAa,EAC1B,GACE,KAYHl7B,SAAS0M,iBAAkB,mBAAoByuB,IAC/Cn7B,SAAS0M,iBAAkB,yBAA0ByuB,IA0wCrDxsB,KAAsBrL,SAAS8Z,IAE9B4c,EAAe5c,EAAiB,WAAY9Z,SAAS,CAAEga,EAAe7Z,KAEjEA,EAAI,IACP6Z,EAAc1e,UAAUE,OAAQ,WAChCwe,EAAc1e,UAAUE,OAAQ,QAChCwe,EAAc1e,UAAUC,IAAK,UAC7Bye,EAAcxU,aAAc,cAAe,QAC5C,GAEE,IAz/CJiF,KAGAqW,GAAY9V,QAAQ,GAgCrB,WAEC,MAAM8sB,EAAoC,UAAhBptB,EAAOuY,KAC3B8U,EAAqC,WAAhBrtB,EAAOuY,MAAqC,WAAhBvY,EAAOuY,MAE1D6U,GAAqBC,KAEpBD,EACHE,KAGAvD,GAAM1O,SAIPmQ,EAAIhK,SAAS5wB,UAAUC,IAAK,uBAExBu8B,EAGyB,aAAxBp7B,SAASqM,WACZwtB,GAAU7d,WAGVnU,OAAO6E,iBAAkB,QAAQ,IAAMmtB,GAAU7d,aAIlD4d,GAAW5d,WAId,CA7DCuf,GAGAn6B,GAAS8rB,UAITzmB,YAAY,KAEX+yB,EAAI9U,OAAO9lB,UAAUE,OAAQ,iBAE7B06B,EAAIS,QAAQr7B,UAAUC,IAAK,SAE3BqG,GAAc,CACbvE,KAAM,QACNkS,KAAM,CACLuP,SACAhO,SACAR,iBAEA,GACA,EAEJ,CAkIA,SAAS6T,GAAgB9oB,GAExB66B,EAAImB,cAAc9f,YAAclc,CAEjC,CAOA,SAAS+oB,GAAe/M,GAEvB,IAAI6gB,EAAO,GAGX,GAAsB,IAAlB7gB,EAAK8gB,SACRD,GAAQ7gB,EAAKE,iBAGT,GAAsB,IAAlBF,EAAK8gB,SAAiB,CAE9B,IAAIC,EAAe/gB,EAAK5R,aAAc,eAClC4yB,EAAiE,SAA/C9zB,OAAOhD,iBAAkB8V,GAAgB,QAC1C,SAAjB+gB,GAA4BC,GAE/Br9B,MAAMC,KAAMoc,EAAK1G,YAAa3Q,SAASs4B,IACtCJ,GAAQ9T,GAAekU,EAAO,GAKjC,CAIA,OAFAJ,EAAOA,EAAK3xB,OAEI,KAAT2xB,EAAc,GAAKA,EAAO,GAElC,CA2DA,SAASztB,GAAWnF,GAEnB,MAAMqF,EAAY,IAAKD,GAQvB,GAJuB,iBAAZpF,GAAuBoxB,EAAahsB,EAAQpF,IAI7B,IAAtBZ,EAAO6zB,UAAuB,OAElC,MAAMC,EAAiBtC,EAAIS,QAAQz7B,iBAAkB+O,GAAkB3M,OAGvE44B,EAAIS,QAAQr7B,UAAUE,OAAQmP,EAAUyqB,YACxCc,EAAIS,QAAQr7B,UAAUC,IAAKmP,EAAO0qB,YAElCc,EAAIS,QAAQnxB,aAAc,wBAAyBkF,EAAO2qB,iBAC1Da,EAAIS,QAAQnxB,aAAc,6BAA8BkF,EAAOgF,sBAG/DwmB,EAAIhK,SAASnwB,MAAMygB,YAAa,gBAAyC,iBAAjB9R,EAAOlD,MAAqBkD,EAAOlD,MAASkD,EAAOlD,MAAQ,MACnH0uB,EAAIhK,SAASnwB,MAAMygB,YAAa,iBAA2C,iBAAlB9R,EAAOjD,OAAsBiD,EAAOjD,OAAUiD,EAAOjD,OAAS,MAEnHiD,EAAOiqB,SACVA,KAGD+B,EAAkBR,EAAIS,QAAS,WAAYjsB,EAAO8d,UAClDkO,EAAkBR,EAAIS,QAAS,MAAOjsB,EAAO+F,KAC7CimB,EAAkBR,EAAIS,QAAS,SAAUjsB,EAAO4L,SAG3B,IAAjB5L,EAAOV,OACVyuB,KAIG/tB,EAAOuqB,cACVyD,KACAC,GAAqB,+BAGrBA,KACAD,GAAoB,uDAIrBrlB,GAAYP,QAGR+iB,IACHA,EAAgB1pB,UAChB0pB,EAAkB,MAIf2C,EAAiB,GAAK9tB,EAAOoqB,WAAapqB,EAAO+d,qBACpDoN,EAAkB,IAAI5D,EAAUiE,EAAIS,SAAS,IACrC51B,KAAKC,IAAKD,KAAKE,KAAOipB,KAAKC,MAAQiM,GAAuBtB,EAAW,GAAK,KAGlFe,EAAgB5B,GAAI,QAAS2E,IAC7BvC,GAAkB,GAIW,YAA1B3rB,EAAOob,eACVoQ,EAAIS,QAAQnxB,aAAc,uBAAwBkF,EAAOob,gBAGzDoQ,EAAIS,QAAQjxB,gBAAiB,wBAG9B0c,GAAM3X,UAAWC,EAAQC,GACzBiC,GAAMnC,UAAWC,EAAQC,GACzB6rB,GAAQ/rB,UAAWC,EAAQC,GAC3BxB,GAASsB,UAAWC,EAAQC,GAC5BuQ,GAASzQ,UAAWC,EAAQC,GAC5B6c,GAAS/c,UAAWC,EAAQC,GAC5B+T,GAAUjU,UAAWC,EAAQC,GAC7BE,GAAYJ,UAAWC,EAAQC,GAE/B2E,IAED,CAKA,SAASupB,KAIRt0B,OAAO6E,iBAAkB,SAAU0vB,IAAgB,GAE/CpuB,EAAO+pB,OAAQA,GAAM5vB,OACrB6F,EAAO8c,UAAWA,GAAS3iB,OAC3B6F,EAAOwQ,UAAWA,GAASrW,OAC3B6F,EAAO6pB,sBAAuBz2B,GAAS+G,OAC3CsE,GAAStE,OACT+H,GAAM/H,OAENqxB,EAAI9U,OAAOhY,iBAAkB,QAAS2vB,IAAiB,GACvD7C,EAAI9U,OAAOhY,iBAAkB,gBAAiB4vB,IAAiB,GAC/D9C,EAAIa,aAAa3tB,iBAAkB,QAASqvB,IAAQ,GAEhD/tB,EAAOyqB,iCACVz4B,SAAS0M,iBAAkB,mBAAoB6vB,IAAwB,EAGzE,CAKA,SAASjB,KAIRvD,GAAM1O,SACNnZ,GAAMmZ,SACNyB,GAASzB,SACT5c,GAAS4c,SACT7K,GAAS6K,SACTjoB,GAASioB,SAETxhB,OAAO8E,oBAAqB,SAAUyvB,IAAgB,GAEtD5C,EAAI9U,OAAO/X,oBAAqB,QAAS0vB,IAAiB,GAC1D7C,EAAI9U,OAAO/X,oBAAqB,gBAAiB2vB,IAAiB,GAClE9C,EAAIa,aAAa1tB,oBAAqB,QAASovB,IAAQ,EAExD,CAsEA,SAASxE,GAAI52B,EAAM62B,EAAUgF,GAE5BpO,EAAc1hB,iBAAkB/L,EAAM62B,EAAUgF,EAEjD,CAKA,SAAS/E,GAAK92B,EAAM62B,EAAUgF,GAE7BpO,EAAczhB,oBAAqBhM,EAAM62B,EAAUgF,EAEpD,CASA,SAAS1T,GAAiB2T,GAGQ,iBAAtBA,EAAWrxB,SAAsBmuB,EAAgBnuB,OAASqxB,EAAWrxB,QAC7C,iBAAxBqxB,EAAWtU,WAAwBoR,EAAgBpR,SAAWsU,EAAWtU,UAGhFoR,EAAgBnuB,OACnB4uB,EAAuBR,EAAI9U,OAAQ6U,EAAgBnuB,OAAS,IAAMmuB,EAAgBpR,UAGlF6R,EAAuBR,EAAI9U,OAAQ6U,EAAgBpR,SAGrD,CAMA,SAASjjB,IAAc3F,OAAEA,EAAOi6B,EAAIS,QAAOt5B,KAAEA,EAAIkS,KAAEA,EAAI8U,QAAEA,GAAQ,IAEhE,IAAI/a,EAAQ5M,SAAS08B,YAAa,aAAc,EAAG,GAWnD,OAVA9vB,EAAM+vB,UAAWh8B,EAAMgnB,GAAS,GAChCqS,EAAaptB,EAAOiG,GACpBtT,EAAO2F,cAAe0H,GAElBrN,IAAWi6B,EAAIS,SAGlB2C,GAAqBj8B,GAGfiM,CAER,CAOA,SAASiwB,GAAsB/Y,GAE9B5e,GAAc,CACbvE,KAAM,eACNkS,KAAM,CACLuP,SACAhO,SACAsI,gBACA9I,eACAkQ,WAIH,CAKA,SAAS8Y,GAAqBj8B,EAAMkS,GAEnC,GAAI7E,EAAOwqB,mBAAqB3wB,OAAOqyB,SAAWryB,OAAOi1B,KAAO,CAC/D,IAAIC,EAAU,CACbC,UAAW,SACX9N,UAAWvuB,EACXixB,MAAO1V,MAGR8d,EAAa+C,EAASlqB,GAEtBhL,OAAOqyB,OAAO/sB,YAAa8vB,KAAKC,UAAWH,GAAW,IACvD,CAED,CAOA,SAASf,GAAoB39B,EAAW,KAEvCC,MAAMC,KAAMi7B,EAAIS,QAAQz7B,iBAAkBH,IAAaiF,SAASnE,IAC3D,gBAAgBkD,KAAMlD,EAAQ4J,aAAc,UAC/C5J,EAAQuN,iBAAkB,QAASywB,IAAsB,EAC1D,GAGF,CAKA,SAASlB,GAAqB59B,EAAW,KAExCC,MAAMC,KAAMi7B,EAAIS,QAAQz7B,iBAAkBH,IAAaiF,SAASnE,IAC3D,gBAAgBkD,KAAMlD,EAAQ4J,aAAc,UAC/C5J,EAAQwN,oBAAqB,QAASwwB,IAAsB,EAC7D,GAGF,CAOA,SAASC,GAAarzB,GAErBqiB,KAEAoN,EAAI6D,QAAUr9B,SAASU,cAAe,OACtC84B,EAAI6D,QAAQz+B,UAAUC,IAAK,WAC3B26B,EAAI6D,QAAQz+B,UAAUC,IAAK,mBAC3B26B,EAAIS,QAAQl5B,YAAay4B,EAAI6D,SAE7B7D,EAAI6D,QAAQ9uB,UACV,iHAE4BxE,6JAIbA,uNAMjByvB,EAAI6D,QAAQlyB,cAAe,UAAWuB,iBAAkB,QAAQE,IAC/D4sB,EAAI6D,QAAQz+B,UAAUC,IAAK,SAAU,IACnC,GAEH26B,EAAI6D,QAAQlyB,cAAe,UAAWuB,iBAAkB,SAASE,IAChEwf,KACAxf,EAAMqS,gBAAgB,IACpB,GAEHua,EAAI6D,QAAQlyB,cAAe,aAAcuB,iBAAkB,SAASE,IACnEwf,IAAc,IACZ,EAEJ,CA2BA,SAASkR,KAER,GAAItvB,EAAOkqB,KAAO,CAEjB9L,KAEAoN,EAAI6D,QAAUr9B,SAASU,cAAe,OACtC84B,EAAI6D,QAAQz+B,UAAUC,IAAK,WAC3B26B,EAAI6D,QAAQz+B,UAAUC,IAAK,gBAC3B26B,EAAIS,QAAQl5B,YAAay4B,EAAI6D,SAE7B,IAAIE,EAAO,+CAEPtU,EAAY6B,GAASlB,eACxBV,EAAW4B,GAASjB,cAErB0T,GAAQ,qCACR,IAAK,IAAIniB,KAAO6N,EACfsU,GAAS,WAAUniB,aAAe6N,EAAW7N,eAI9C,IAAK,IAAImO,KAAWL,EACfA,EAASK,GAASnO,KAAO8N,EAASK,GAASC,cAC9C+T,GAAS,WAAUrU,EAASK,GAASnO,eAAe8N,EAASK,GAASC,yBAIxE+T,GAAQ,WAER/D,EAAI6D,QAAQ9uB,UAAa,oLAKOgvB,kCAIhC/D,EAAI6D,QAAQlyB,cAAe,UAAWuB,iBAAkB,SAASE,IAChEwf,KACAxf,EAAMqS,gBAAgB,IACpB,EAEJ,CAED,CAKA,SAASmN,KAER,QAAIoN,EAAI6D,UACP7D,EAAI6D,QAAQx9B,WAAWiY,YAAa0hB,EAAI6D,SACxC7D,EAAI6D,QAAU,MACP,EAKT,CAMA,SAASjyB,KAER,GAAIouB,EAAIS,UAAYJ,GAAU3b,WAAa,CAE1C,MAAMsf,EAAgBhE,EAAIhK,SAASla,YAC7BoK,EAAiB8Z,EAAIhK,SAAS5Z,aAEpC,IAAK5H,EAAO8pB,cAAgB,CAQvBsC,IAAoBpsB,EAAO8d,UAC9B9rB,SAASC,gBAAgBZ,MAAMygB,YAAa,OAA+B,IAArBjY,OAAO0X,YAAuB,MAGrF,MAAMke,EAAO7D,GAAW1b,WACpBmB,GAAsBme,EAAe9d,GACrCL,KAEEqe,EAAWplB,EAGjB2M,GAAqBjX,EAAOlD,MAAOkD,EAAOjD,QAE1CyuB,EAAI9U,OAAOrlB,MAAMyL,MAAQ2yB,EAAK3yB,MAAQ,KACtC0uB,EAAI9U,OAAOrlB,MAAM0L,OAAS0yB,EAAK1yB,OAAS,KAGxCuN,EAAQjU,KAAKC,IAAKm5B,EAAKE,kBAAoBF,EAAK3yB,MAAO2yB,EAAKG,mBAAqBH,EAAK1yB,QAGtFuN,EAAQjU,KAAKE,IAAK+T,EAAOtK,EAAO2pB,UAChCrf,EAAQjU,KAAKC,IAAKgU,EAAOtK,EAAO4pB,UAIlB,IAAVtf,GAAeshB,GAAW1b,YAC7Bsb,EAAI9U,OAAOrlB,MAAMw+B,KAAO,GACxBrE,EAAI9U,OAAOrlB,MAAM8lB,KAAO,GACxBqU,EAAI9U,OAAOrlB,MAAMqf,IAAM,GACvB8a,EAAI9U,OAAOrlB,MAAM0mB,OAAS,GAC1ByT,EAAI9U,OAAOrlB,MAAMisB,MAAQ,GACzBxC,GAAiB,CAAE1d,OAAQ,OAG3BouB,EAAI9U,OAAOrlB,MAAMw+B,KAAO,GACxBrE,EAAI9U,OAAOrlB,MAAM8lB,KAAO,MACxBqU,EAAI9U,OAAOrlB,MAAMqf,IAAM,MACvB8a,EAAI9U,OAAOrlB,MAAM0mB,OAAS,OAC1ByT,EAAI9U,OAAOrlB,MAAMisB,MAAQ,OACzBxC,GAAiB,CAAE1d,OAAQ,+BAAgCkN,EAAO,OAInE,MAAMoM,EAASpmB,MAAMC,KAAMi7B,EAAIS,QAAQz7B,iBAAkB+O,IAEzD,IAAK,IAAIrP,EAAI,EAAG4/B,EAAMpZ,EAAO9jB,OAAQ1C,EAAI4/B,EAAK5/B,IAAM,CACnD,MAAMyK,EAAQ+b,EAAQxmB,GAGM,SAAxByK,EAAMtJ,MAAM0F,UAIViJ,EAAO4L,QAAUjR,EAAM/J,UAAU8U,SAAU,UAG5C/K,EAAM/J,UAAU8U,SAAU,SAC7B/K,EAAMtJ,MAAMqf,IAAM,EAGlB/V,EAAMtJ,MAAMqf,IAAMra,KAAKE,KAAOk5B,EAAK1yB,OAASpC,EAAMkW,cAAiB,EAAG,GAAM,KAI7ElW,EAAMtJ,MAAMqf,IAAM,GAGpB,CAEIgf,IAAaplB,GAChBpT,GAAc,CACbvE,KAAM,SACNkS,KAAM,CACL6qB,WACAplB,QACAmlB,SAIJ,EA2DF,WAQC,GACCjE,EAAIS,UACHjsB,EAAO8pB,gBACP+B,GAAU3b,YAC6B,iBAAjClQ,EAAO4qB,uBACE,WAAhB5qB,EAAOuY,KACN,CACD,MAAMkX,EAAOpe,KAEToe,EAAKE,kBAAoB,GAAKF,EAAKE,mBAAqB3vB,EAAO4qB,sBAC7DgB,GAAW1b,aACfkG,GAAYtS,SACZ8nB,GAAW5d,YAIR4d,GAAW1b,YAAa0b,GAAW/b,YAEzC,CAED,CArFEkgB,GAEAvE,EAAIhK,SAASnwB,MAAMygB,YAAa,gBAAiBxH,GACjDkhB,EAAIhK,SAASnwB,MAAMygB,YAAa,mBAAoB0d,EAAgB,MACpEhE,EAAIhK,SAASnwB,MAAMygB,YAAa,oBAAqBJ,EAAiB,MAEtEka,GAAWxuB,SAEXoT,GAASlQ,SACT8V,GAAYtP,iBAERqT,GAASjK,YACZiK,GAAS7Z,QAGX,CAED,CASA,SAAS2W,GAAqBna,EAAOC,GAEpCivB,EAAeR,EAAI9U,OAAQ,4CAA6CphB,SAASnE,IAGhF,IAAI6+B,E1BryB2BC,EAAE9+B,EAAS4L,EAAS,KAErD,GAAI5L,EAAU,CACb,IAAI++B,EAAWC,EAAYh/B,EAAQE,MAAM0L,OAkBzC,OAdA5L,EAAQE,MAAM0L,OAAS,MAIvB5L,EAAQU,WAAWR,MAAM0L,OAAS,OAElCmzB,EAAYnzB,EAAS5L,EAAQU,WAAW+V,aAGxCzW,EAAQE,MAAM0L,OAASozB,EAAY,KAGnCh/B,EAAQU,WAAWR,MAAM8hB,eAAe,UAEjC+c,CACR,CAEA,OAAOnzB,CAAM,E0B6wBWivB,CAAyB76B,EAAS4L,GAGxD,GAAI,gBAAgB1I,KAAMlD,EAAQyb,UAAa,CAC9C,MAAMwjB,EAAKj/B,EAAQk/B,cAAgBl/B,EAAQm/B,WACxCC,EAAKp/B,EAAQq/B,eAAiBr/B,EAAQs/B,YAEnCC,EAAKr6B,KAAKC,IAAKwG,EAAQszB,EAAIJ,EAAkBO,GAEnDp/B,EAAQE,MAAMyL,MAAUszB,EAAKM,EAAO,KACpCv/B,EAAQE,MAAM0L,OAAWwzB,EAAKG,EAAO,IAEtC,MAECv/B,EAAQE,MAAMyL,MAAQA,EAAQ,KAC9B3L,EAAQE,MAAM0L,OAASizB,EAAkB,IAC1C,GAIF,CA4CA,SAAS3e,GAAsBse,EAAmBC,GAEjD,IAAI9yB,EAAQkD,EAAOlD,MACfC,EAASiD,EAAOjD,OAEhBiD,EAAO8pB,gBACVhtB,EAAQ0uB,EAAI9U,OAAOpP,YACnBvK,EAASyuB,EAAI9U,OAAO9O,cAGrB,MAAM6nB,EAAO,CAEZ3yB,MAAOA,EACPC,OAAQA,EAGR4yB,kBAAmBA,GAAqBnE,EAAIS,QAAQ3kB,YACpDsoB,mBAAoBA,GAAsBpE,EAAIS,QAAQrkB,cAiBvD,OAbA6nB,EAAKE,mBAAuBF,EAAKE,kBAAoB3vB,EAAO8W,OAC5D2Y,EAAKG,oBAAwBH,EAAKG,mBAAqB5vB,EAAO8W,OAGpC,iBAAf2Y,EAAK3yB,OAAsB,KAAKzI,KAAMo7B,EAAK3yB,SACrD2yB,EAAK3yB,MAAQ0F,SAAUitB,EAAK3yB,MAAO,IAAO,IAAM2yB,EAAKE,mBAI3B,iBAAhBF,EAAK1yB,QAAuB,KAAK1I,KAAMo7B,EAAK1yB,UACtD0yB,EAAK1yB,OAASyF,SAAUitB,EAAK1yB,OAAQ,IAAO,IAAM0yB,EAAKG,oBAGjDH,CAER,CAUA,SAASkB,GAA0BnhB,EAAO9Z,GAEpB,iBAAV8Z,GAAoD,mBAAvBA,EAAM1U,cAC7C0U,EAAM1U,aAAc,uBAAwBpF,GAAK,EAGnD,CASA,SAASk7B,GAA0BphB,GAElC,GAAqB,iBAAVA,GAAoD,mBAAvBA,EAAM1U,cAA+B0U,EAAM5e,UAAU8U,SAAU,SAAY,CAElH,MAAMmrB,EAAgBrhB,EAAM/U,aAAc,qBAAwB,oBAAsB,uBAExF,OAAO+H,SAAUgN,EAAMzU,aAAc81B,IAAmB,EAAG,GAC5D,CAEA,OAAO,CAER,CAUA,SAASzvB,GAAiBzG,EAAQiL,GAEjC,OAAOjL,GAASA,EAAM9I,cAAgB8I,EAAM9I,WAAW+a,SAAS5b,MAAO,WAExE,CAmBA,SAAS8/B,KAER,SAAIlrB,IAAgBxE,GAAiBwE,MAEhCA,EAAamrB,kBAOnB,CAMA,SAASC,KAER,OAAkB,IAAX5c,GAA2B,IAAXhO,CAExB,CAQA,SAAS6qB,KAER,QAAIrrB,KAECA,EAAamrB,sBAGb3vB,GAAiBwE,KAAkBA,EAAa/T,WAAWk/B,oBAOjE,CAMA,SAASzxB,KAER,GAAIU,EAAOV,MAAQ,CAClB,MAAM4xB,EAAY1F,EAAIS,QAAQr7B,UAAU8U,SAAU,UAElD0U,KACAoR,EAAIS,QAAQr7B,UAAUC,IAAK,WAET,IAAdqgC,GACHh6B,GAAc,CAAEvE,KAAM,UAExB,CAED,CAKA,SAASo7B,KAER,MAAMmD,EAAY1F,EAAIS,QAAQr7B,UAAU8U,SAAU,UAClD8lB,EAAIS,QAAQr7B,UAAUE,OAAQ,UAE9BiqB,KAEImW,GACHh6B,GAAc,CAAEvE,KAAM,WAGxB,CAKA,SAASkrB,GAAa5N,GAEG,kBAAbA,EACVA,EAAW3Q,KAAUyuB,KAGrBhR,KAAagR,KAAWzuB,IAG1B,CAOA,SAASyd,KAER,OAAOyO,EAAIS,QAAQr7B,UAAU8U,SAAU,SAExC,CAyDA,SAAS/K,GAAOnD,EAAG9B,EAAGG,EAAGigB,GAaxB,GAVoB5e,GAAc,CACjCvE,KAAM,oBACNkS,KAAM,CACLuP,YAAcmJ,IAAN/lB,EAAkB4c,EAAS5c,EACnC4O,YAAcmX,IAAN7nB,EAAkB0Q,EAAS1Q,EACnCogB,YAKcqb,iBAAmB,OAGnCziB,EAAgB9I,EAGhB,MAAMqB,EAAmBukB,EAAIS,QAAQz7B,iBAAkBgP,GAIvD,GAAIosB,GAAW1b,WAAa,CAC3B,MAAMsF,EAAgBoW,GAAWtV,kBAAmB9e,EAAG9B,GAEvD,YADI8f,GAAgBoW,GAAWpW,cAAeA,GAE/C,CAGA,GAAgC,IAA5BvO,EAAiBrU,OAAe,YAI1B2qB,IAAN7nB,GAAoBykB,GAASjK,aAChCxa,EAAIk7B,GAA0B3pB,EAAkBzP,KAK7CkX,GAAiBA,EAAc7c,YAAc6c,EAAc7c,WAAWjB,UAAU8U,SAAU,UAC7FirB,GAA0BjiB,EAAc7c,WAAYuU,GAIrD,MAAMgrB,EAAcxN,EAAM5qB,SAG1B4qB,EAAMhxB,OAAS,EAEf,IAAIy+B,EAAejd,GAAU,EAC5Bkd,EAAelrB,GAAU,EAG1BgO,EAASmd,GAAc/xB,OAAkC+d,IAAN/lB,EAAkB4c,EAAS5c,GAC9E4O,EAASmrB,GAAc9xB,OAAgC8d,IAAN7nB,EAAkB0Q,EAAS1Q,GAG5E,IAAI87B,EAAiBpd,IAAWid,GAAgBjrB,IAAWkrB,EAGtDE,IAAe9iB,EAAgB,MAIpC,IAAI+iB,EAAyBxqB,EAAkBmN,GAC9Csd,EAAwBD,EAAuBjhC,iBAAkB,WAGlE4vB,EAAcxvB,UAAUof,OAAQ,oBAAqB0hB,EAAsB9+B,OAAS,GAGpFgT,EAAe8rB,EAAuBtrB,IAAYqrB,EAElD,IAAIE,GAAwB,EAGxBH,GAAgB9iB,GAAiB9I,IAAiBuU,GAASjK,aAC9Dwa,EAAa,UAEbiH,EAAwB7iB,GAA0BJ,EAAe9I,EAAcyrB,EAAcC,GAQzFK,GACHnG,EAAI9U,OAAO9lB,UAAUC,IAAK,8BAK5B2pB,KAEApd,KAGI+c,GAASjK,YACZiK,GAAS7Z,cAIO,IAANzK,GACVme,GAAU4F,KAAM/jB,GAMb6Y,GAAiBA,IAAkB9I,IACtC8I,EAAc9d,UAAUE,OAAQ,WAChC4d,EAAc5T,aAAc,cAAe,QAGvCk2B,MAEHv4B,YAAY,KAovBPuzB,EAAeR,EAAIS,QAASzsB,EAA6B,UAnvBzClK,SAASqF,IAC5Bg2B,GAA0Bh2B,EAAO,EAAG,GAClC,GACD,IAKLi3B,EAAW,IAAK,IAAI1hC,EAAI,EAAG4/B,EAAMlM,EAAMhxB,OAAQ1C,EAAI4/B,EAAK5/B,IAAM,CAG7D,IAAK,IAAI2hC,EAAI,EAAGA,EAAIT,EAAYx+B,OAAQi/B,IACvC,GAAIT,EAAYS,KAAOjO,EAAM1zB,GAAK,CACjCkhC,EAAYU,OAAQD,EAAG,GACvB,SAASD,CACV,CAGDpG,EAAIhK,SAAS5wB,UAAUC,IAAK+yB,EAAM1zB,IAGlCgH,GAAc,CAAEvE,KAAMixB,EAAM1zB,IAC7B,CAGA,KAAOkhC,EAAYx+B,QAClB44B,EAAIhK,SAAS5wB,UAAUE,OAAQsgC,EAAY39B,OAGxC+9B,GACH3C,GAAsB/Y,IAInB0b,GAAiB9iB,IACpB/H,GAAavH,oBAAqBsP,GAClC/H,GAAa1I,qBAAsB2H,IAMpC5Q,uBAAuB,KACtBykB,GAAgBC,GAAe9T,GAAgB,IAGhD4K,GAASlQ,SACT7B,GAAS6B,SACToX,GAAMpX,SACN8V,GAAY9V,SACZ8V,GAAYtP,iBACZ3G,GAAYG,SACZ0T,GAAU1T,SAGVlN,GAAS4mB,WAETe,KAGI4W,IAEHl5B,YAAY,KACX+yB,EAAI9U,OAAO9lB,UAAUE,OAAQ,4BAA6B,GACxD,GAECkP,EAAO2I,aAEVA,GAAYV,IAAKyG,EAAe9I,GAKnC,CAaA,SAASkJ,GAA0B5G,EAAWC,EAASkpB,EAAcC,GAEpE,OAAQppB,EAAUzN,aAAc,sBAAyB0N,EAAQ1N,aAAc,sBAC7EyN,EAAUnN,aAAc,0BAA6BoN,EAAQpN,aAAc,2BACtEqZ,EAASid,GAAgBjrB,EAASkrB,EAAiBnpB,EAAUD,GAAYzN,aAAc,4BAE/F,CAqDA,SAASmK,KAGR0oB,KACAa,KAGA/wB,KAGAgtB,EAAYpqB,EAAOoqB,UAGnBrP,KAGA3E,GAAYtS,SAGZ1Q,GAAS4mB,YAE0B,IAA/Bha,EAAOgrB,qBACVhX,GAAUkF,UAGXza,GAAS6B,SACTkQ,GAASlQ,SAETka,KAEA9C,GAAMpX,SACNoX,GAAMyP,mBACN/Q,GAAY9V,QAAQ,GACpBH,GAAYG,SACZqG,GAAalJ,yBAGgB,IAAzBuC,EAAO7B,cACVwI,GAAavH,oBAAqBwG,EAAc,CAAEvG,eAAe,IAGjEsH,GAAa1I,qBAAsB2H,GAGhCuU,GAASjK,YACZiK,GAAS/c,QAGX,CAkDA,SAAS6sB,GAASvT,EAAS/V,MAE1B+V,EAAOphB,SAAS,CAAEqF,EAAOzK,KAKxB,IAAI6hC,EAAcrb,EAAQrgB,KAAKwgB,MAAOxgB,KAAK27B,SAAWtb,EAAO9jB,SACzDm/B,EAAYlgC,aAAe8I,EAAM9I,YACpC8I,EAAM9I,WAAWsd,aAAcxU,EAAOo3B,GAIvC,IAAI7qB,EAAiBvM,EAAMnK,iBAAkB,WACzC0W,EAAetU,QAClBq3B,GAAS/iB,EACV,GAIF,CAeA,SAASqqB,GAAclhC,EAAUmc,GAIhC,IAAIkK,EAASsV,EAAeR,EAAIS,QAAS57B,GACxC4hC,EAAevb,EAAO9jB,OAEnBs/B,EAAYtG,GAAW1b,YAAc2b,GAAU3b,WAC/CiiB,GAAiB,EACjBC,GAAkB,EAEtB,GAAIH,EAAe,CAGdjyB,EAAOgqB,OACNxd,GAASylB,IAAeE,GAAiB,IAE7C3lB,GAASylB,GAEG,IACXzlB,EAAQylB,EAAezlB,EACvB4lB,GAAkB,IAKpB5lB,EAAQnW,KAAKE,IAAKF,KAAKC,IAAKkW,EAAOylB,EAAe,GAAK,GAEvD,IAAK,IAAI/hC,EAAI,EAAGA,EAAI+hC,EAAc/hC,IAAM,CACvC,IAAIiB,EAAUulB,EAAOxmB,GAEjBmiC,EAAUryB,EAAO+F,MAAQ3E,GAAiBjQ,GAG9CA,EAAQP,UAAUE,OAAQ,QAC1BK,EAAQP,UAAUE,OAAQ,WAC1BK,EAAQP,UAAUE,OAAQ,UAG1BK,EAAQ2J,aAAc,SAAU,IAChC3J,EAAQ2J,aAAc,cAAe,QAGjC3J,EAAQgM,cAAe,YAC1BhM,EAAQP,UAAUC,IAAK,SAIpBqhC,EACH/gC,EAAQP,UAAUC,IAAK,WAIpBX,EAAIsc,GAEPrb,EAAQP,UAAUC,IAAKwhC,EAAU,SAAW,QAExCryB,EAAOgU,WAEVse,GAAiBnhC,IAGVjB,EAAIsc,GAEZrb,EAAQP,UAAUC,IAAKwhC,EAAU,OAAS,UAEtCryB,EAAOgU,WAEVue,GAAiBphC,IAKVjB,IAAMsc,GAASxM,EAAOgU,YAC1Bme,EACHI,GAAiBphC,GAETihC,GACRE,GAAiBnhC,GAGpB,CAEA,IAAIwJ,EAAQ+b,EAAOlK,GACfgmB,EAAa73B,EAAM/J,UAAU8U,SAAU,WAG3C/K,EAAM/J,UAAUC,IAAK,WACrB8J,EAAMK,gBAAiB,UACvBL,EAAMK,gBAAiB,eAElBw3B,GAEJt7B,GAAc,CACb3F,OAAQoJ,EACRhI,KAAM,UACNgnB,SAAS,IAMX,IAAI8Y,EAAa93B,EAAMI,aAAc,cACjC03B,IACH7O,EAAQA,EAAM5qB,OAAQy5B,EAAWl/B,MAAO,MAG1C,MAICiZ,EAAQ,EAGT,OAAOA,CAER,CAKA,SAAS8lB,GAAiB5tB,GAEzBsnB,EAAetnB,EAAW,aAAcpP,SAAS6iB,IAChDA,EAASvnB,UAAUC,IAAK,WACxBsnB,EAASvnB,UAAUE,OAAQ,mBAAoB,GAGjD,CAKA,SAASyhC,GAAiB7tB,GAEzBsnB,EAAetnB,EAAW,qBAAsBpP,SAAS6iB,IACxDA,EAASvnB,UAAUE,OAAQ,UAAW,mBAAoB,GAG5D,CAMA,SAAS0pB,KAIR,IAECkY,EACAC,EAHG1rB,EAAmBtG,KACtBiyB,EAAyB3rB,EAAiBrU,OAI3C,GAAIggC,QAA4C,IAAXxe,EAAyB,CAI7D,IAAI0W,EAAe3Q,GAASjK,WAAa,GAAKlQ,EAAO8qB,aAIjDsB,IACHtB,EAAe3Q,GAASjK,WAAa,EAAIlQ,EAAO+qB,oBAI7Cc,GAAU3b,aACb4a,EAAerN,OAAOC,WAGvB,IAAK,IAAIhlB,EAAI,EAAGA,EAAIk6B,EAAwBl6B,IAAM,CACjD,IAAI0W,EAAkBnI,EAAiBvO,GAEnCwO,EAAiB8kB,EAAe5c,EAAiB,WACpDyjB,EAAuB3rB,EAAetU,OAmBvC,GAhBA8/B,EAAYr8B,KAAKowB,KAAOrS,GAAU,GAAM1b,IAAO,EAI3CsH,EAAOgqB,OACV0I,EAAYr8B,KAAKowB,MAASrS,GAAU,GAAM1b,IAAQk6B,EAAyB9H,KAAoB,GAI5F4H,EAAY5H,EACfnkB,GAAajM,KAAM0U,GAGnBzI,GAAapJ,OAAQ6R,GAGlByjB,EAAuB,CAE1B,IAAIC,EAAKlC,GAA0BxhB,GAEnC,IAAK,IAAI3Z,EAAI,EAAGA,EAAIo9B,EAAsBp9B,IAAM,CAC/C,IAAI6Z,EAAgBpI,EAAezR,GAEnCk9B,EAAYj6B,KAAQ0b,GAAU,GAAM/d,KAAKowB,KAAOrgB,GAAU,GAAM3Q,GAAMY,KAAKowB,IAAKhxB,EAAIq9B,GAEhFJ,EAAYC,EAAY7H,EAC3BnkB,GAAajM,KAAM4U,GAGnB3I,GAAapJ,OAAQ+R,EAEvB,CAED,CACD,CAGI4N,KACHsO,EAAIS,QAAQr7B,UAAUC,IAAK,uBAG3B26B,EAAIS,QAAQr7B,UAAUE,OAAQ,uBAI3BmsB,KACHuO,EAAIS,QAAQr7B,UAAUC,IAAK,yBAG3B26B,EAAIS,QAAQr7B,UAAUE,OAAQ,wBAGhC,CAED,CAOA,SAAS6nB,IAAgB2N,iBAAEA,GAAmB,GAAU,IAEvD,IAAIrf,EAAmBukB,EAAIS,QAAQz7B,iBAAkBgP,GACpD0H,EAAiBskB,EAAIS,QAAQz7B,iBAAkBiP,GAE5C0hB,EAAS,CACZhK,KAAM/C,EAAS,EACfkJ,MAAOlJ,EAASnN,EAAiBrU,OAAS,EAC1C4qB,GAAIpX,EAAS,EACbuX,KAAMvX,EAASc,EAAetU,OAAS,GAyBxC,GApBIoN,EAAOgqB,OACN/iB,EAAiBrU,OAAS,IAC7BuuB,EAAOhK,MAAO,EACdgK,EAAO7D,OAAQ,GAGZpW,EAAetU,OAAS,IAC3BuuB,EAAO3D,IAAK,EACZ2D,EAAOxD,MAAO,IAIX1W,EAAiBrU,OAAS,GAA+B,WAA1BoN,EAAOob,iBAC1C+F,EAAO7D,MAAQ6D,EAAO7D,OAAS6D,EAAOxD,KACtCwD,EAAOhK,KAAOgK,EAAOhK,MAAQgK,EAAO3D,KAMZ,IAArB8I,EAA4B,CAC/B,IAAIyM,EAAiB/e,GAAU2E,kBAC/BwI,EAAOhK,KAAOgK,EAAOhK,MAAQ4b,EAAezd,KAC5C6L,EAAO3D,GAAK2D,EAAO3D,IAAMuV,EAAezd,KACxC6L,EAAOxD,KAAOwD,EAAOxD,MAAQoV,EAAexd,KAC5C4L,EAAO7D,MAAQ6D,EAAO7D,OAASyV,EAAexd,IAC/C,CAGA,GAAIvV,EAAO+F,IAAM,CAChB,IAAIoR,EAAOgK,EAAOhK,KAClBgK,EAAOhK,KAAOgK,EAAO7D,MACrB6D,EAAO7D,MAAQnG,CAChB,CAEA,OAAOgK,CAER,CAUA,SAASpgB,GAAmBpG,EAAQiL,GAEnC,IAAIqB,EAAmBtG,KAGnBqyB,EAAY,EAGhBC,EAAU,IAAK,IAAI/iC,EAAI,EAAGA,EAAI+W,EAAiBrU,OAAQ1C,IAAM,CAE5D,IAAIkf,EAAkBnI,EAAiB/W,GACnCgX,EAAiBkI,EAAgB5e,iBAAkB,WAEvD,IAAK,IAAIqhC,EAAI,EAAGA,EAAI3qB,EAAetU,OAAQi/B,IAAM,CAGhD,GAAI3qB,EAAe2qB,KAAOl3B,EACzB,MAAMs4B,EAIsC,cAAzC/rB,EAAe2qB,GAAGhxB,QAAQC,YAC7BkyB,GAGF,CAGA,GAAI5jB,IAAoBzU,EACvB,OAKqD,IAAlDyU,EAAgBxe,UAAU8U,SAAU,UAA8D,cAAvC0J,EAAgBvO,QAAQC,YACtFkyB,GAGF,CAEA,OAAOA,CAER,CA+CA,SAAS9xB,GAAYvG,GAGpB,IAEC9E,EAFG2B,EAAI4c,EACP1e,EAAI0Q,EAIL,GAAIzL,EAEH,GAAIixB,GAAW1b,WACd1Y,EAAIgL,SAAU7H,EAAMI,aAAc,gBAAkB,IAEhDJ,EAAMI,aAAc,kBACvBrF,EAAI8M,SAAU7H,EAAMI,aAAc,gBAAkB,SAGjD,CACJ,IAAI6T,EAAaxN,GAAiBzG,GAC9BoJ,EAAS6K,EAAajU,EAAM9I,WAAa8I,EAGzCsM,EAAmBtG,KAGvBnJ,EAAInB,KAAKE,IAAK0Q,EAAiBjJ,QAAS+F,GAAU,GAGlDrO,OAAI6nB,EAGA3O,IACHlZ,EAAIW,KAAKE,IAAKy1B,EAAerxB,EAAM9I,WAAY,WAAYmM,QAASrD,GAAS,GAE/E,CAGD,IAAKA,GAASiL,EAAe,CAE5B,GADmBA,EAAapV,iBAAkB,aAAcoC,OAAS,EACtD,CAClB,IAAI2mB,EAAkB3T,EAAazI,cAAe,qBAEjDtH,EADG0jB,GAAmBA,EAAgB9e,aAAc,uBAChD+H,SAAU+W,EAAgBxe,aAAc,uBAAyB,IAGjE6K,EAAapV,iBAAkB,qBAAsBoC,OAAS,CAEpE,CACD,CAEA,MAAO,CAAE4E,IAAG9B,IAAGG,IAEhB,CAKA,SAAS0M,KAER,OAAOypB,EAAeR,EAAIS,QAAS1sB,EAAkB,kDAEtD,CAOA,SAASoB,KAER,OAAOqrB,EAAeR,EAAIS,QAASzsB,EAEpC,CAKA,SAAS2H,KAER,OAAO6kB,EAAeR,EAAIS,QAAS,0BAEpC,CAcA,SAAShP,KAER,OAAOtc,KAAsB/N,OAAS,CACvC,CAKA,SAASsqB,KAER,OAAO/V,KAAoBvU,OAAS,CAErC,CA0BA,SAASoO,KAER,OAAOuB,KAAY3P,MAEpB,CAOA,SAASsgC,GAAUx6B,EAAGjD,GAErB,IAAI2Z,EAAkBzO,KAAuBjI,GACzCwO,EAAiBkI,GAAmBA,EAAgB5e,iBAAkB,WAE1E,OAAI0W,GAAkBA,EAAetU,QAAuB,iBAAN6C,EAC9CyR,EAAiBA,EAAgBzR,QAAM8nB,EAGxCnO,CAER,CA+BA,SAASlB,KAER,IAAIjN,EAAUC,KAEd,MAAO,CACNkT,OAAQnT,EAAQzJ,EAChB4O,OAAQnF,EAAQvL,EAChBy9B,OAAQlyB,EAAQpL,EAChBkJ,OAAQge,KACR5C,SAAUA,GAASjK,WAGrB,CA8BA,SAAS6K,KAIR,GAFAX,KAEIxU,IAAqC,IAArB5F,EAAOoqB,UAAsB,CAEhD,IAAIjS,EAAWvS,EAAazI,cAAe,qCAEvCi2B,EAAoBjb,EAAWA,EAASpd,aAAc,kBAAqB,KAC3Es4B,EAAkBztB,EAAa/T,WAAa+T,EAAa/T,WAAWkJ,aAAc,kBAAqB,KACvGu4B,EAAiB1tB,EAAa7K,aAAc,kBAO5Cq4B,EACHhJ,EAAY5nB,SAAU4wB,EAAmB,IAEjCE,EACRlJ,EAAY5nB,SAAU8wB,EAAgB,IAE9BD,EACRjJ,EAAY5nB,SAAU6wB,EAAiB,KAGvCjJ,EAAYpqB,EAAOoqB,UAOyC,IAAxDxkB,EAAapV,iBAAkB,aAAcoC,QAChDo5B,EAAepmB,EAAc,gBAAiBtQ,SAASlF,IAClDA,EAAGqK,aAAc,kBAChB2vB,GAA4B,IAAdh6B,EAAGiZ,SAAkBjZ,EAAGmjC,aAAiBnJ,IAC1DA,EAA4B,IAAdh6B,EAAGiZ,SAAkBjZ,EAAGmjC,aAAiB,IAEzD,MAWCnJ,GAAcuB,GAAoB5O,MAAe5C,GAASjK,YAAiB+gB,OAAiBjd,GAAU2E,kBAAkBpD,OAAwB,IAAhBvV,EAAOgqB,OAC1IyB,EAAmBhzB,YAAY,KACQ,mBAA3BuH,EAAOqqB,gBACjBrqB,EAAOqqB,kBAGPmJ,KAEDzY,IAAc,GACZqP,GACHsB,EAAqBlM,KAAKC,OAGvB0L,GACHA,EAAgBlD,YAAkC,IAAtBwD,EAG9B,CAED,CAKA,SAASrR,KAER5hB,aAAcizB,GACdA,GAAoB,CAErB,CAEA,SAASgI,KAEJrJ,IAAcuB,IACjBA,GAAkB,EAClBz0B,GAAc,CAAEvE,KAAM,oBACtB6F,aAAcizB,GAEVN,GACHA,EAAgBlD,YAAY,GAI/B,CAEA,SAASyL,KAEJtJ,GAAauB,IAChBA,GAAkB,EAClBz0B,GAAc,CAAEvE,KAAM,qBACtBooB,KAGF,CAEA,SAAS4Y,IAAatW,cAACA,GAAc,GAAO,IAK3C,GAHAiO,EAAkB/J,0BAA2B,EAGzCqK,GAAW1b,WAAa,OAAO0b,GAAWtW,OAG1CtV,EAAO+F,KACJoU,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUuB,SAAsBoD,KAAkBxB,MAC/Fxc,GAAOyZ,EAAS,EAA6B,SAA1BpU,EAAOob,eAA4BhV,OAASmX,IAItDpD,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUsB,SAAsBqD,KAAkBxB,MACpGxc,GAAOyZ,EAAS,EAA6B,SAA1BpU,EAAOob,eAA4BhV,OAASmX,EAGjE,CAEA,SAASqW,IAAcvW,cAACA,GAAc,GAAO,IAK5C,GAHAiO,EAAkB/J,0BAA2B,EAGzCqK,GAAW1b,WAAa,OAAO0b,GAAWrW,OAG1CvV,EAAO+F,KACJoU,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUsB,SAAsBqD,KAAkB2E,OAC/F3iB,GAAOyZ,EAAS,EAA6B,SAA1BpU,EAAOob,eAA4BhV,OAASmX,IAItDpD,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUuB,SAAsBoD,KAAkB2E,OACpG3iB,GAAOyZ,EAAS,EAA6B,SAA1BpU,EAAOob,eAA4BhV,OAASmX,EAGjE,CAEA,SAASsW,IAAWxW,cAACA,GAAc,GAAO,IAGzC,GAAIuO,GAAW1b,WAAa,OAAO0b,GAAWtW,QAGxC6E,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUsB,SAAsBqD,KAAkB6E,IAC/F7iB,GAAOyZ,EAAQhO,EAAS,EAG1B,CAEA,SAAS0tB,IAAazW,cAACA,GAAc,GAAO,IAK3C,GAHAiO,EAAkBhK,wBAAyB,EAGvCsK,GAAW1b,WAAa,OAAO0b,GAAWrW,QAGxC4E,GAASjK,YAAcmN,IAAsC,IAArBrJ,GAAUuB,SAAsBoD,KAAkBgF,MAC/FhjB,GAAOyZ,EAAQhO,EAAS,EAG1B,CAQA,SAAS2tB,IAAa1W,cAACA,GAAc,GAAO,IAG3C,GAAIuO,GAAW1b,WAAa,OAAO0b,GAAWtW,OAG9C,GAAI+H,IAAsC,IAArBrJ,GAAUsB,OAC9B,GAAIqD,KAAkB6E,GACrBqW,GAAW,CAACxW,sBAER,CAEJ,IAAI3O,EAWJ,GARCA,EADG1O,EAAO+F,IACMimB,EAAeR,EAAIS,QAASzsB,EAA6B,WAAY/L,MAGrEu4B,EAAeR,EAAIS,QAASzsB,EAA6B,SAAU/L,MAKhFib,GAAiBA,EAAc9d,UAAU8U,SAAU,SAAY,CAClE,IAAIhQ,EAAMgZ,EAAcle,iBAAkB,WAAYoC,OAAS,QAAO2qB,EAEtE5iB,GADQyZ,EAAS,EACP1e,EACX,MACSsK,EAAO+F,IACf6tB,GAAc,CAACvW,kBAGfsW,GAAa,CAACtW,iBAEhB,CAGF,CAKA,SAASmW,IAAanW,cAACA,GAAc,GAAO,IAM3C,GAJAiO,EAAkB/J,0BAA2B,EAC7C+J,EAAkBhK,wBAAyB,EAGvCsK,GAAW1b,WAAa,OAAO0b,GAAWrW,OAG9C,GAAI8H,IAAsC,IAArBrJ,GAAUuB,OAAmB,CAEjD,IAAI4L,EAASxI,KAKTwI,EAAOxD,MAAQwD,EAAO7D,OAAStd,EAAOgqB,MAAQ8G,OACjD3P,EAAOxD,MAAO,GAGXwD,EAAOxD,KACVmW,GAAa,CAACzW,kBAENrd,EAAO+F,IACf4tB,GAAa,CAACtW,kBAGduW,GAAc,CAACvW,iBAEjB,CAED,CAwBA,SAAS2P,GAAepuB,GAEvB,IAAIiG,EAAOjG,EAAMiG,KAGjB,GAAoB,iBAATA,GAA0C,MAArBA,EAAKpB,OAAQ,IAAkD,MAAnCoB,EAAKpB,OAAQoB,EAAKjS,OAAS,KACtFiS,EAAOoqB,KAAK+E,MAAOnvB,GAGfA,EAAKovB,QAAyC,mBAAxBj6B,EAAO6K,EAAKovB,SAErC,IAA0D,IAAtDv0B,EAA8BrL,KAAMwQ,EAAKovB,QAAqB,CAEjE,MAAMtmB,EAAS3T,EAAO6K,EAAKovB,QAAQ1hC,MAAOyH,EAAQ6K,EAAKqvB,MAIvDtF,GAAqB,WAAY,CAAEqF,OAAQpvB,EAAKovB,OAAQtmB,OAAQA,GAEjE,MAEC8W,QAAQC,KAAM,eAAgB7f,EAAKovB,OAAQ,+CAM/C,CAOA,SAAS3F,GAAiB1vB,GAEN,YAAf8rB,GAA4B,YAAYr2B,KAAMuK,EAAMrN,OAAOqb,YAC9D8d,EAAa,OACbxzB,GAAc,CACbvE,KAAM,qBACNkS,KAAM,CAAEuP,SAAQhO,SAAQsI,gBAAe9I,kBAI1C,CAQA,SAASyoB,GAAiBzvB,GAEzB,MAAMu1B,EAASnI,EAAcptB,EAAMrN,OAAQ,gBAO3C,GAAI4iC,EAAS,CACZ,MAAMzV,EAAOyV,EAAOp5B,aAAc,QAC5BkG,EAAU7N,GAASqP,mBAAoBic,GAEzCzd,IACHjH,EAAOW,MAAOsG,EAAQzJ,EAAGyJ,EAAQvL,EAAGuL,EAAQpL,GAC5C+I,EAAMqS,iBAER,CAED,CAOA,SAASmd,GAAgBxvB,GAExBxB,IACD,CAOA,SAASmxB,GAAwB3vB,IAIR,IAApB5M,SAASqnB,QAAoBrnB,SAASoqB,gBAAkBpqB,SAASglB,OAEzB,mBAAhChlB,SAASoqB,cAAc6K,MACjCj1B,SAASoqB,cAAc6K,OAExBj1B,SAASglB,KAAK9U,QAGhB,CAOA,SAASirB,GAAoBvuB,IAEd5M,SAASoiC,mBAAqBpiC,SAASqiC,2BACrC7I,EAAIS,UACnBrtB,EAAMwE,2BAGN3K,YAAY,KACXuB,EAAOoD,SACPpD,EAAOkI,MAAMA,OAAO,GAClB,GAGL,CAQA,SAASitB,GAAsBvwB,GAE9B,GAAIA,EAAM01B,eAAiB11B,EAAM01B,cAAc75B,aAAc,QAAW,CACvE,IAAIsB,EAAM6C,EAAM01B,cAAcv5B,aAAc,QACxCgB,IACHqzB,GAAarzB,GACb6C,EAAMqS,iBAER,CAED,CAOA,SAASid,GAAwBtvB,GAG5BqyB,OAAiC,IAAhBjxB,EAAOgqB,MAC3BrvB,GAAO,EAAG,GACV+4B,MAGQ/H,EACR+H,KAIAD,IAGF,CAQA,MAAMc,GAAM,CACXtJ,UAEAuJ,WAlqFD,SAAqBC,GAEpB,IAAKrU,EAAgB,KAAM,2DAQ3B,GANAgL,GAAc,EAGdI,EAAIS,QAAU7L,EACdoL,EAAI9U,OAAS0J,EAAcjjB,cAAe,YAErCquB,EAAI9U,OAAS,KAAM,0DAwBxB,OAfA1W,EAAS,IAAK0pB,KAAkB1pB,KAAWpF,KAAY65B,KAAgBzI,KAGnE,cAAc33B,KAAMwF,OAAOzG,SAASC,UACvC2M,EAAOuY,KAAO,SAmBhB,YAGyB,IAApBvY,EAAO8d,SACV0N,EAAIhK,SAAWwK,EAAc5L,EAAe,qBAAwBA,GAIpEoL,EAAIhK,SAAWxvB,SAASglB,KACxBhlB,SAASC,gBAAgBrB,UAAUC,IAAK,qBAGzC26B,EAAIhK,SAAS5wB,UAAUC,IAAK,kBAE7B,CA9BC6jC,GAGA76B,OAAO6E,iBAAkB,OAAQtB,IAAQ,GAGzC2mB,GAAQrpB,KAAMsF,EAAO+jB,QAAS/jB,EAAOgkB,cAAeQ,KAAMuH,IAEnD,IAAIhV,SAASmN,GAAWlqB,EAAOuvB,GAAI,QAASrF,IAEpD,EA+nFCnkB,aACA0B,QAnsED,YAIqB,IAAhB2pB,IAEJkC,KACAlT,KACA6T,KAGAvW,GAAMjW,UACNS,GAAMT,UACNsiB,GAAQtiB,UACRqqB,GAAQrqB,UACRhD,GAASgD,UACT+O,GAAS/O,UACT2U,GAAY3U,UACZtB,GAAYsB,UACZwc,GAAYxc,UAGZzP,SAAS2M,oBAAqB,mBAAoBwuB,IAClDn7B,SAAS2M,oBAAqB,yBAA0BwuB,IACxDn7B,SAAS2M,oBAAqB,mBAAoB4vB,IAAwB,GAC1E10B,OAAO8E,oBAAqB,UAAWquB,IAAe,GACtDnzB,OAAO8E,oBAAqB,OAAQvB,IAAQ,GAGxCouB,EAAIa,cAAeb,EAAIa,aAAav7B,SACpC06B,EAAImB,eAAgBnB,EAAImB,cAAc77B,SAE1CkB,SAASC,gBAAgBrB,UAAUE,OAAQ,oBAE3C06B,EAAIS,QAAQr7B,UAAUE,OAAQ,QAAS,SAAU,wBAAyB,uBAC1E06B,EAAIS,QAAQjxB,gBAAiB,yBAC7BwwB,EAAIS,QAAQjxB,gBAAiB,8BAE7BwwB,EAAIhK,SAAS5wB,UAAUE,OAAQ,mBAC/B06B,EAAIhK,SAASnwB,MAAM8hB,eAAgB,iBACnCqY,EAAIhK,SAASnwB,MAAM8hB,eAAgB,kBAEnCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,SACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,UACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,QACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,QACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,OACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,UACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,SACjCqY,EAAI9U,OAAOrlB,MAAM8hB,eAAgB,aAEjC7iB,MAAMC,KAAMi7B,EAAIS,QAAQz7B,iBAAkB+O,IAAoBjK,SAASqF,IACtEA,EAAMtJ,MAAM8hB,eAAgB,WAC5BxY,EAAMtJ,MAAM8hB,eAAgB,OAC5BxY,EAAMK,gBAAiB,UACvBL,EAAMK,gBAAiB,cAAe,IAGxC,EA2oEC4J,QACA+vB,UAtnCD,SAAoBh6B,EAAQiL,GAE3BwQ,GAAYxR,KAAMjK,GAClBqZ,GAAUpP,KAAMjK,GAEhBgM,GAAajM,KAAMC,GAEnByb,GAAY9V,SACZoX,GAAMpX,QAEP,EA6mCCs0B,cAAe5gB,GAAUpP,KAAKzK,KAAM6Z,IAGpCrZ,SACAwc,KAAMwc,GACNrW,MAAOsW,GACPpW,GAAIqW,GACJlW,KAAMmW,GACNxe,KAAMye,GACNxe,KAAMie,GAGNG,gBAAcC,iBAAeC,cAAYC,gBAAcC,gBAAcP,gBAGrEqB,iBAAkB7gB,GAAU4F,KAAKzf,KAAM6Z,IACvC8gB,aAAc9gB,GAAUsB,KAAKnb,KAAM6Z,IACnC+gB,aAAc/gB,GAAUuB,KAAKpb,KAAM6Z,IAGnCuV,MACAE,OAGA/qB,iBAAkB6qB,GAClB5qB,oBAAqB8qB,GAGrBrsB,UAGA6sB,WAGAtR,mBAGAqc,mBAAoBhhB,GAAU2E,gBAAgBxe,KAAM6Z,IAGpDmK,WAhgED,SAAqBlO,GAEI,kBAAbA,EACVA,EAAWqf,KAAalR,KAGpBoN,EAAI6D,QACPjR,KAGAkR,IAGH,EAs/DC2F,eAAgB9a,GAASnK,OAAO7V,KAAMggB,IAGtC+a,iBAAkBtJ,GAAW5b,OAAO7V,KAAMyxB,IAG1C/N,eAGAG,gBAjhDD,SAA0B/N,GAED,kBAAbA,EACVA,EAAWyjB,KAAoBD,KAI/B9H,EAAkB+H,KAAoBD,IAGxC,EA0gDCvV,kBAtiDD,SAA4BjO,GAEH,kBAAbA,EACVA,EAAWgO,GAAYjc,OAASic,GAAY9b,OAG5C8b,GAAYnf,YAAcmf,GAAY9b,OAAS8b,GAAYjc,MAG7D,EAgiDCgvB,gBACAC,eACAH,uBACA1vB,mBACAiO,gBAnqDD,SAA0B1U,EAAQiL,GAEjC,OAAOjL,EAAM/J,UAAU8U,SAAU,WAAmD,OAArC/K,EAAMwC,cAAe,UAErE,EAkqDC4f,YACAd,cA9gDD,WAEC,SAAWmO,GAAcuB,EAE1B,EA2gDCrvB,eAAgBob,GAAM2P,qBAAqBltB,KAAMud,IACjDyd,WAAYhb,GAASjK,SAAS/V,KAAMggB,IACpC4B,UAAW7Z,GAAM6Z,UAAU5hB,KAAM+H,IAEjC7H,aAAcuxB,GAAW1b,SAAS/V,KAAMyxB,IACxCxrB,YAAayrB,GAAU3b,SAAS/V,KAAM0xB,IAGtCgC,QAASA,IAAMxC,EAGf+J,UAAWzuB,GAAajM,KAAKP,KAAMwM,IACnC0uB,YAAa1uB,GAAapJ,OAAOpD,KAAMwM,IAGvC1I,qBAAsBA,IAAM0I,GAAa1I,qBAAsB2H,GAC/DxG,oBAAqBA,IAAMuH,GAAavH,oBAAqBwG,EAAc,CAAEvG,eAAe,IAG5F+vB,eACAkG,YAAalX,GAGb+P,qBACAb,wBACAp2B,iBAGAgX,YACAuB,SA3jBD,SAAmBmU,GAElB,GAAqB,iBAAVA,EAAqB,CAC/BjpB,GAAOqxB,EAAkBpI,EAAMxP,QAAU4X,EAAkBpI,EAAMxd,QAAU4lB,EAAkBpI,EAAMuP,SAEnG,IAAIoC,EAAavJ,EAAkBpI,EAAM7kB,QACxCy2B,EAAexJ,EAAkBpI,EAAMzJ,UAEd,kBAAfob,GAA4BA,IAAexY,MACrDc,GAAa0X,GAGc,kBAAjBC,GAA8BA,IAAiBrb,GAASjK,YAClEiK,GAASnK,OAAQwlB,EAEnB,CAED,EA6iBC3T,YAnzBD,WAGC,IAAI4T,EAAaz0B,KACbgyB,EAAYjyB,KAEhB,GAAI6E,EAAe,CAElB,IAAI8vB,EAAe9vB,EAAapV,iBAAkB,aAIlD,GAAIklC,EAAa9iC,OAAS,EAAI,CAC7B,IAII+iC,EAAiB,GAGrB3C,GAPuBptB,EAAapV,iBAAkB,qBAOtBoC,OAAS8iC,EAAa9iC,OAAW+iC,CAClE,CAED,CAEA,OAAOt/B,KAAKC,IAAK08B,GAAcyC,EAAa,GAAK,EAElD,EA2xBCv0B,cAIA00B,oBAlqBD,WAEC,OAAOrzB,KAAYlJ,KAAKsB,IAEvB,IAAIk7B,EAAa,CAAA,EACjB,IAAK,IAAI3lC,EAAI,EAAGA,EAAIyK,EAAMk7B,WAAWjjC,OAAQ1C,IAAM,CAClD,IAAI4lC,EAAYn7B,EAAMk7B,WAAY3lC,GAClC2lC,EAAYC,EAAUnX,MAASmX,EAAUnlC,KAC1C,CACA,OAAOklC,CAAU,GAInB,EAwpBC90B,qBAGAC,kBAGAkyB,YAGA6C,iBAAkBA,IAAMrnB,EAGxBjO,gBAAiBA,IAAMmF,EAGvBpI,mBA7nBD,SAA6B9E,EAAGjD,GAE/B,IAAIkF,EAAqB,iBAANjC,EAAiBw6B,GAAUx6B,EAAGjD,GAAMiD,EACvD,GAAIiC,EACH,OAAOA,EAAMU,sBAKf,EAunBCsc,cAAeD,GAAMC,cAAcxd,KAAMud,IAGzCnV,aAGA5B,uBACAwG,qBAIA8V,uBACAC,qBAGAqE,yBAA0BA,IAAM+J,EAAkB/J,yBAClDD,uBAAwBA,IAAMgK,EAAkBhK,uBAEhDxS,4BAGAwM,cAAewB,GAASxB,cAAcnhB,KAAM2iB,IAC5CrB,iBAAkBqB,GAASrB,iBAAiBthB,KAAM2iB,IAGlDpB,WAAYoB,GAASpB,WAAWvhB,KAAM2iB,IAGtCnB,yBAA0BmB,GAASnB,yBAAyBxhB,KAAM2iB,IAElEzL,wBACA8E,qBA53CD,SAA+B9D,EAAc7a,EAAG9B,GAE/C,IAAI27B,EAAejd,GAAU,EAE7BA,EAAS5c,EACT4O,EAAS1Q,EAET,MAAM87B,EAAe5rB,IAAiByM,EAEtC3D,EAAgB9I,EAChBA,EAAeyM,EAEXzM,GAAgB8I,GACf1O,EAAO2I,aAAemG,GAA0BJ,EAAe9I,EAAcyrB,EAAcjrB,IAE9FuC,GAAYV,IAAKyG,EAAe9I,GAK9B4rB,IACC9iB,IACH/H,GAAavH,oBAAqBsP,GAClC/H,GAAavH,oBAAqBsP,EAAcrT,yBAGjDsL,GAAa1I,qBAAsB2H,GACnCe,GAAa1I,qBAAsB2H,EAAavK,yBAGjDrG,uBAAuB,KACtBykB,GAAgBC,GAAe9T,GAAgB,IAGhDipB,IAED,EA21CCrkB,SAAUA,IAAMF,EAGhB/P,UAAWA,IAAMyF,EAGjB9M,aAAc84B,EAGdgK,aAAc5iC,GAASiO,QAAQlH,KAAM/G,IAGrC0M,iBAAkBA,IAAMsgB,EACxBtiB,iBAAkBA,IAAM0tB,EAAI9U,OAC5BF,mBAAoBA,IAAMgV,EAAIhK,SAC9BnH,sBAAuBA,IAAMjE,GAAYjlB,QAGzC8yB,eAAgBF,GAAQE,eAAe9pB,KAAM4pB,IAC7CoB,UAAWpB,GAAQoB,UAAUhrB,KAAM4pB,IACnCqB,UAAWrB,GAAQqB,UAAUjrB,KAAM4pB,IACnCkS,WAAYlS,GAAQsB,qBAAqBlrB,KAAM4pB,KAiChD,OA5BAiI,EAAahyB,EAAQ,IACjBu6B,GAGH9a,kBACAC,iBAGAxX,SACAg0B,OAAQtK,GACRpb,YACA/R,YACArL,YACA+mB,YACAnG,aACAoC,eACAzP,gBACAxG,eAEA+b,YA3YD,SAAsBtd,GAEjBoB,EAAO+d,oBACV0V,IAGF,EAsYCrV,gBACA5D,0BACAvD,uBACA6D,mBACAC,gBACAX,qBAGMma,EAER,CCr/FIv6B,IAAAA,EAASkxB,EAeTiL,EAAmB,UAEvBn8B,EAAOw6B,WAAa55B,IAGnB/B,OAAOO,OAAQY,EAAQ,IAAIkxB,EAAMl5B,SAASmL,cAAe,WAAavC,IAGtEu7B,EAAiB98B,KAAK46B,GAAUA,EAAQj6B,KAEjCA,EAAOw6B,cAUf,CAAE,YAAa,KAAM,MAAO,mBAAoB,sBAAuB,kBAAmBl/B,SAAS2+B,IAClGj6B,EAAOi6B,GAAU,IAAKC,KACrBiC,EAAiB58B,MAAM68B,GAAQA,EAAKnC,GAAQtiC,KAAM,QAASuiC,IAAQ,CACnE,IAGFl6B,EAAO6zB,QAAU,KAAM,EAEvB7zB,EAAOixB,QAAUA", "x_google_ignoreList": [2]}